/**
  **************************************************************************
  * @file     at32_selftest_startup.c
  * @version  v2.0.0
  * @date     2021-12-31
  * @brief    contains self-test related routines in startup phase.
  **************************************************************************
  */

#include "main.h"
#include "at32_selftest_param.h"
#include "at32_selftest_lib.h"
#ifdef __CC_ARM            /* keil compiler */
#include "crc32.h"
#endif

#ifdef __IAR_SYSTEMS_ICC__  /* iar compiler */
 /* ram location for temporary storage of original values at run time ram transparent test */
 __no_init uint32_t runtime_ram_buf[RUNTIME_RAM_BLOCK_SIZE + 2U] @ "RUNTIME_RAM_BUF";

 /* ram pointer for run-time tests */
 __no_init uint32_t *p_runtime_ram_chk @ "RUNTIME_RAM_POINTER";
 __no_init uint32_t *p_runtime_ram_chk_inv @ "RUNTIME_RAM_POINTER";
 
 __no_init uint32_t gap_for_ram_test_overlay[2] @ "RUNTIME_RAM_POINTER";

 /* class b variables area */
 /* counter for verifying correct program execution at start */     
 __no_init uint32_t ctrl_flow_cnt @ "CLASS_B_RAM";
 __no_init uint32_t ctrl_flow_cnt_inv @ "CLASS_B_RAM_REV";
 
 /* counter for verifying correct program execution in interrupt */
 __no_init __IO uint32_t isr_ctrl_flow_cnt @ "CLASS_B_RAM"; 
 __no_init __IO uint32_t isr_ctrl_flow_cnt_inv @ "CLASS_B_RAM_REV";

 /* lick period measurement at tmr irq handler */
 __no_init __IO uint32_t period_val @ "CLASS_B_RAM";        
 __no_init __IO uint32_t period_val_inv @ "CLASS_B_RAM_REV";  
 
 /* sofware time base (incremented in systick timer isr */
 __no_init uint32_t systick_cnt @ "CLASS_B_RAM";
 __no_init uint32_t systick_cnt_inv @ "CLASS_B_RAM_REV";
 
 /* indicates to the main routine a tick */
 __no_init __IO uint32_t time_base_flag @ "CLASS_B_RAM";
 __no_init __IO uint32_t time_base_flag_inv @ "CLASS_B_RAM_REV";
 
 /* indicates to the main routine a tick */
 __no_init __IO uint32_t lick_period_flag @ "CLASS_B_RAM";
 __no_init __IO uint32_t lick_period_flag_inv @ "CLASS_B_RAM_REV";
 
 /* stores the control flow counter from one main loop to the other */
 __no_init uint32_t last_ctrl_flow_cnt @ "CLASS_B_RAM";
 __no_init uint32_t last_ctrl_flow_cnt_inv @ "CLASS_B_RAM_REV";
 
 /* pointer to flash for crc runtime tests */
 __no_init uint32_t *p_runtime_crc_chk @ "CLASS_B_RAM";
 __no_init uint32_t *p_runtime_crc_chk_inv @ "CLASS_B_RAM_REV";
 
 /* reference crc for runtime tests */
 __no_init uint32_t reference_crc @ "CLASS_B_RAM";
 __no_init uint32_t reference_crc_inv @ "CLASS_B_RAM_REV";

 /* magic pattern for stack overflow in this array */
 __no_init __IO uint32_t stack_overflow_buf[4] @ "STACK_BOTTOM";
#endif

#ifdef __CC_ARM   /* keil compiler */
 /* ram location for temporary storage of original values at run time ram transparent test */
 uint32_t runtime_ram_buf[RUNTIME_RAM_BLOCK_SIZE + 2] __attribute__((section("RUNTIME_RAM_BUF")));

 /* ram pointer for run-time tests */
 uint32_t *p_runtime_ram_chk        __attribute__((section("RUNTIME_RAM_POINTER")));
 uint32_t *p_runtime_ram_chk_inv     __attribute__((section("RUNTIME_RAM_POINTER")));
 
 uint32_t gap_for_ram_test_overlay[2] __attribute__((section("RUNTIME_RAM_POINTER")));

 /* note: the zero_init forces the linker to place variables in the bss section */
 /* this allows the uninit directive (in scatter file) to work. on the contrary */
 /* all class b variables pairs should be initialized properly by user before using them */

 /* counter for verifying correct program execution at start */                                         
 uint32_t ctrl_flow_cnt             __attribute__((section("CLASS_B_RAM"), zero_init));
 uint32_t ctrl_flow_cnt_inv          __attribute__((section("CLASS_B_RAM_REV"), zero_init));

 /* counter for verifying correct program execution in interrupt */
 uint32_t isr_ctrl_flow_cnt          __attribute__((section("CLASS_B_RAM"), zero_init));
 uint32_t isr_ctrl_flow_cnt_inv       __attribute__((section("CLASS_B_RAM_REV"), zero_init));

 /* lick period measurement at tmr irq handler */
 uint32_t period_val           __attribute__((section("CLASS_B_RAM"), zero_init));
 uint32_t period_val_inv        __attribute__((section("CLASS_B_RAM_REV"), zero_init));

 /* sofware time base (incremented in systick timer isr */
 uint32_t systick_cnt             __attribute__((section("CLASS_B_RAM"), zero_init));
 uint32_t systick_cnt_inv          __attribute__((section("CLASS_B_RAM_REV"), zero_init));

 /* indicates to the main routine a tick */
 __IO uint32_t time_base_flag       __attribute__((section("CLASS_B_RAM"), zero_init));
 __IO uint32_t time_base_flag_inv    __attribute__((section("CLASS_B_RAM_REV"), zero_init));
  
 /* indicates to the main routine a tick */
 __IO uint32_t lick_period_flag      __attribute__((section("CLASS_B_RAM"), zero_init));
 __IO uint32_t lick_period_flag_inv   __attribute__((section("CLASS_B_RAM_REV"), zero_init));

 /* stores the control flow counter from one main loop to the other */
 uint32_t last_ctrl_flow_cnt         __attribute__((section("CLASS_B_RAM"), zero_init));
 uint32_t last_ctrl_flow_cnt_inv      __attribute__((section("CLASS_B_RAM_REV"), zero_init));

 /* pointer to flash for crc runtime tests */
 uint32_t *p_runtime_crc_chk           __attribute__((section("CLASS_B_RAM"), zero_init));
 uint32_t *p_runtime_crc_chk_inv        __attribute__((section("CLASS_B_RAM_REV"), zero_init));

 /* reference crc for runtime tests */
 uint32_t reference_crc                __attribute__((section("CLASS_B_RAM"), zero_init));
 uint32_t reference_crc_inv             __attribute__((section("CLASS_B_RAM_REV"), zero_init));

 /* magic pattern for stack overflow in this array */
 __IO uint32_t stack_overflow_buf[4]   __attribute__((section("STACK_BOTTOM"), zero_init));
#endif

void selftest_watchdog_test(void);

/**
  * @brief  handle routine when self-test fail. it has to be filled by end
  *         user to keep application safe.
  * @param  none
  * @retval none
  */
void selftest_fail_handle(void)
{
  /* disable any checking services at systtick interrupt */
  systick_cnt = systick_cnt_inv;
  
  #ifdef DEBUG_MESSAGE_STARTUP
  system_core_clock_update();
  usart_reconfigure();
  STARTUP_PRINTF("selftest fail!!! save handling!\r\n");
  #endif

  while(1)
  {
    #ifdef WDT_USED
    wdt_counter_reload();
    #endif
    #ifdef WWDT_USED
    wwdt_counter_set(127);
    #endif
  }
}

#ifdef __CC_ARM             /* keil compiler */
/**
  * @brief  extended function in startup phase, call before main entry
  * @param  none
  * @retval none
  */
void $Sub$$main(void)
{
  if(ENTER_MAIN_FLAG != 0xAABBAABB )
  {
    selftest_startup_check();
  }
  ENTER_MAIN_FLAG = 0xFFFFFFFF;
  $Super$$main(); 
}
#endif

/**
  * @brief  contains the selftest routines in startup phase
  * @param  none
  * @retval none
  */
void selftest_startup_check(void)
{
  uint32_t crc_result;
  clock_status_type clk_sts;

  systick_cnt = 0;
  systick_cnt_inv = 0;
  
  nvic_priority_group_config(NVIC_PRIORITY_GROUP_4);
 
  /* initializes the systick */
  nvic_irq_enable(SysTick_IRQn, 15, 0);
  system_core_clock_update();
  SysTick_Config(system_core_clock / 1000);
  #ifdef DEBUG_MESSAGE_STARTUP
  uart_print_init(115200);
  #endif
  STARTUP_PRINTF("\r\n******************* self-test in startup phase *******************\r\n");

  /***********************************************************************
                 cpu registers and flags self test 
  ***********************************************************************/
  /* init counters for control flow */
  control_flow_init();

  control_flow_call(CPU_TEST_CALLER);
  /* warning: all registers destroyed when exiting this function (including
  preserved registers r4 to r11) while excluding stack pointer r13) */
  if(selftest_cpu_startup_test() != CPU_TEST_SUCCESS)
  {
    STARTUP_PRINTF("cpu test fail in startup.\r\n");
    selftest_fail_handle();
  }
  else
  {
    control_flow_resume(CPU_TEST_CALLER);
    STARTUP_PRINTF("cpu test ok in startup.\r\n");
  }

  /***********************************************************************
                        watchdogs self test 
  ***********************************************************************/  
  control_flow_call(WDT_TEST_CALLER);

  /* wdt & wwdt test, system reset is performed here */
  selftest_watchdog_test();

  control_flow_resume(WDT_TEST_CALLER);

/***********************************************************************
                        clock frequency test
  ***********************************************************************/ 
  #ifdef DEBUG_MESSAGE_STARTUP
  /* finish communication flow prior system clock change */
  while(usart_flag_get(USARTx, USART_TDC_FLAG) != SET){}
  #endif
  control_flow_call(CLOCK_TEST_CALLER);

  /* test lick & hext(hick) clock systems */
  clk_sts = selftest_clock_startup_test();
  
  #ifdef DEBUG_MESSAGE_STARTUP
  /* Re-init communication channel with modified clock setting */
  system_core_clock_update();
  usart_reconfigure();

  /* print out the test result */
  if(clk_sts == CLOCK_TEST_SUCCESS)
  {
    STARTUP_PRINTF("clock frequency test ok.\r\n");
  }
  else
  {
    STARTUP_PRINTF("clock frequency test fail. err_index=%d.\r\n", clk_sts);
    selftest_fail_handle();
  }
  #endif
    
  control_flow_resume(CLOCK_TEST_CALLER);  
  
  /***********************************************************************
         switch to pll with maximum speed for flash and ram test
  ***********************************************************************/  
  selftest_system_clock_config();
  system_core_clock_update();
  #ifdef DEBUG_MESSAGE_STARTUP
  usart_reconfigure();
  #endif
  STARTUP_PRINTF("config pll for flash and ram test.\r\n");

  /***********************************************************************
                            flash crc test 
  ***********************************************************************/ 
  control_flow_call(CRC_TEST_CALLER);
  /* Compute the 32-bit crc of the whole Flash by CRC unit except the checksum
     pattern stored at top of FLASH */
  crm_periph_clock_enable(CRM_CRC_PERIPH_CLOCK, TRUE);

  #ifdef __CC_ARM            /* keil compiler */
  crc_result = crc32_fsl_single(0, (uint8_t*)ROM_START_ADDR, ROM_SIZE);
  #endif
  #ifdef __IAR_SYSTEMS_ICC__  /* iar compiler */
  uint32_t index;
  crc_data_reset();  
  for(index = 0; index < (ROM_SIZE / sizeof(uint32_t)); index++)
  {
    CRC->dt = __REV(*((uint32_t *)ROM_START_ADDR + index));
  }
  crc_result = CRC->dt;
  /* crc calculate done, reset crc ip */
  crc_data_reset(); 
  crc_common_data_set(0x00);
  #endif

  if(crc_result != STORED_ROM_CRC)
  {
    STARTUP_PRINTF("flash verify crc fail.\r\n");
    selftest_fail_handle();
  }
  else
  {
    STARTUP_PRINTF("flash verify crc ok.\r\n");
    control_flow_resume(CRC_TEST_CALLER);
  }

  /***********************************************************************
   verify control flow before ram init (which clears ctrl flow counters) 
  ***********************************************************************/ 
  if(control_flow_check_point(CHECKPOINT1) == ERROR)
  {
    STARTUP_PRINTF("control flow error checkpoint 1\r\n");
    selftest_fail_handle();
  }
  else
  {
    STARTUP_PRINTF("control flow checkpoint 1 ok\r\n");
  }
  
  /***********************************************************************
                            ram test 
  ***********************************************************************/ 
  __disable_irq();
  
  /* warning: the stack is initialized into background pattern when exiting from this routine */
  if(selftest_full_ram_test((uint32_t *)RAM_START_ADDR, (uint32_t *)RAM_END_ADDR, BACKGROUND_PATTERN) != RAM_TEST_SUCCESS)
  {
    #ifdef DEBUG_MESSAGE_STARTUP
    #ifdef __IAR_SYSTEMS_ICC__         /* iar compiler */
    __iar_data_init3();
    #endif

    /* restore interrupt capability */
    __enable_irq();

    system_core_clock_update();
    usart_reconfigure();
    STARTUP_PRINTF("ram test fail.\r\n");
    #endif
    selftest_fail_handle();
  }

  #ifdef DEBUG_MESSAGE_STARTUP       
  #ifdef __IAR_SYSTEMS_ICC__           /* iar compiler */
  __iar_data_init3();
  #endif
  system_core_clock_update();
  usart_reconfigure();
  STARTUP_PRINTF("ram test ok.\r\n");
  #endif

  /* restore interrupt capability */
  __enable_irq();
  
  /* initialization of counters for control flow monitoring (destroyed during ram test) */
  control_flow_init();

  /***********************************************************************
           store reference 32-bit crc in ram after ram test
  ***********************************************************************/ 
  control_flow_call(CRC_STORE_CALLER);
  
  reference_crc = 0;  
  reference_crc_inv = ~reference_crc;

  control_flow_resume(CRC_STORE_CALLER);  

  /***********************************************************************
                      initialize stack overflow pattern
  ***********************************************************************/   
  control_flow_call(STACK_OVERFLOW_CALLER);
  
  stack_overflow_buf[0] = STACK_OVERFLOW_FLAG_0;
  stack_overflow_buf[1] = STACK_OVERFLOW_FLAG_1;
  stack_overflow_buf[2] = STACK_OVERFLOW_FLAG_2;
  stack_overflow_buf[3] = STACK_OVERFLOW_FLAG_3;
  
  control_flow_resume(STACK_OVERFLOW_CALLER);

  /***********************************************************************
         verify control flow before starting main program execution
  ***********************************************************************/   
  if (control_flow_check_point(CHECKPOINT2) == ERROR)
  {
     STARTUP_PRINTF("control flow error checkpoint 2\r\n");
     selftest_fail_handle();
  }

  STARTUP_PRINTF("control flow checkpoint 2 ok\r\n"); 
  #ifdef DEBUG_MESSAGE_STARTUP
  /* finish communication flow prior system clock change */
  while(usart_flag_get(USARTx, USART_TDC_FLAG) != SET){}
  #endif
     
  /* startup test completed successfully - restart the application */
  goto_compiler_startup();
}

/**
  * @brief  watchdog test by forcing watchdog resets
  * @param  none
  * @retval none
  */
void selftest_watchdog_test(void)
{
  #ifdef DEBUG_MESSAGE_STARTUP  
  if(crm_flag_get(CRM_NRST_RESET_FLAG) != RESET) 
    STARTUP_PRINTF("nrst reset.\r\n");
  if(crm_flag_get(CRM_POR_RESET_FLAG)  != RESET)
    STARTUP_PRINTF("por reset.\r\n");
  if(crm_flag_get(CRM_SW_RESET_FLAG)  != RESET)
    STARTUP_PRINTF("sw reset.\r\n");
  if(crm_flag_get(CRM_WDT_RESET_FLAG) != RESET)
    STARTUP_PRINTF("wdt reset.\r\n");
  if(crm_flag_get(CRM_WWDT_RESET_FLAG) != RESET)
    STARTUP_PRINTF("wwdt reset.\r\n");
  if(crm_flag_get(CRM_LOWPOWER_RESET_FLAG) != RESET)
    STARTUP_PRINTF("lowpower reset.\r\n");
  #endif

  /* start watchdogs test */
  if((crm_flag_get(CRM_POR_RESET_FLAG) != RESET) || \
     (crm_flag_get(CRM_SW_RESET_FLAG) != RESET) || \
     (crm_flag_get(CRM_LOWPOWER_RESET_FLAG) != RESET) || \
    ((crm_flag_get(CRM_NRST_RESET_FLAG) != RESET) && (crm_flag_get(CRM_WDT_RESET_FLAG) == RESET) && (crm_flag_get(CRM_WWDT_RESET_FLAG) == RESET)))
  {
    STARTUP_PRINTF("first test wdt.\r\n");

    /* enable wdt debug mode for debug*/
    debug_periph_mode_set(DEBUG_WDT_PAUSE, TRUE);

    /* clear all flags before test */
    crm_flag_clear(CRM_ALL_RESET_FLAG);

    /* setup wdt to minimum period */
    wdt_register_write_enable(TRUE);
    wdt_divider_set(WDT_CLK_DIV_4);
    wdt_reload_value_set(1);
    wdt_counter_reload();
    wdt_enable();

    /* wait for wdt reset */
    while(1);
  }
  else if((crm_flag_get(CRM_NRST_RESET_FLAG) != RESET) && (crm_flag_get(CRM_WDT_RESET_FLAG) != RESET) && (crm_flag_get(CRM_WWDT_RESET_FLAG) == RESET))
  {
    /* only wdt flag set, continue wwdt test */
    STARTUP_PRINTF("only wdt reset, continue to test wwdt.\r\n");
    /* enable wwdt debug mode for debug */
    debug_periph_mode_set(DEBUG_WWDT_PAUSE, TRUE);
      
    /* setup wwdt to minimum period */
    crm_periph_clock_enable(CRM_WWDT_PERIPH_CLOCK, TRUE);
    wwdt_divider_set(WWDT_PCLK1_DIV_4096);
    wwdt_window_counter_set(0x40);
    wwdt_enable(0x40);
   
    /* wait for wwdt reset */
    while(1);
  }
  else if((crm_flag_get(CRM_NRST_RESET_FLAG) != RESET) && (crm_flag_get(CRM_WDT_RESET_FLAG) != RESET) && (crm_flag_get(CRM_WWDT_RESET_FLAG) != RESET))
  {
    /* both wdt & wwdt flags set, watchdogs test is completed */
    STARTUP_PRINTF("wwdt also reset, watchdogs test ok.\r\n");
    /* clear all flags before test */
    crm_flag_clear(CRM_ALL_RESET_FLAG);
  }
  else
  {
    /* unexpected case, re-start watchdogs test */
    STARTUP_PRINTF("watchdogs test fail.\r\n");
    STARTUP_PRINTF("unexpected case, re-start watchdogs test.\r\n");
    /* clear all flags before test */
    crm_flag_clear(CRM_ALL_RESET_FLAG);
    #ifdef DEBUG_MESSAGE_STARTUP
    while(usart_flag_get(USARTx, USART_TDC_FLAG) != SET){}
    #endif
    NVIC_SystemReset();
  }
}

/**
  * @brief  verifies the consistency and value of control flow counters
  * @param  check value of the positive counter
  * @retval error_status
  */
error_status control_flow_check_point(uint32_t chck)
{
  error_status result = SUCCESS;
  
  if((ctrl_flow_cnt != (chck)) || (ctrl_flow_cnt != ~ctrl_flow_cnt_inv))
  {
    result= ERROR;
  }
  return result;
}

