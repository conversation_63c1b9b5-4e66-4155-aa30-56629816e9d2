;/**
;  **************************************************************************
;  * @file     at32_selftest_cpustart_keil.s
;  * @version  v2.0.0
;  * @date     2021-12-31
;  * @brief    contains cpu self-test in startup phase.
;  **************************************************************************
;  */

  THUMB
  REQUIRE8
  PRESERVE8

  AREA |.text|, CODE, READONLY, ALIGN=2

  ; reference to the selftest_fail_handle routine to be executed in case of non-recoverable
  ; failure									
  IMPORT selftest_fail_handle

    ; c variables for control flow monitoring
  IMPORT ctrl_flow_cnt
  IMPORT ctrl_flow_cnt_inv

;/**
;  * @brief  cpu selftest in startup.
;  * @note   all registers destroyed when exiting this function (including preserved
;  *         registers r4 to r11) and excluding stack point r13)
;  *         when possible, branch are 16-bit only (depending on relative offset to 
;  *         final bl instruction
;  * @param  none
;  * @retval none
;  */
selftest_cpu_startup_test PROC
    EXPORT selftest_cpu_startup_test
    MOVS R0, #0x00000000      ; set z(ero) flag
    BNE.W selftest_fail_handle         ; fails if z clear
    SUBS R0,#1                ; set n(egative) flag
    BPL.W selftest_fail_handle         ; fails if n clear
    ADDS R0,#2                ; set c(arry) flag and do not set z
    BCC.W selftest_fail_handle         ; fails if c clear
    MOVS R0, #0x80000000      ; prepares overflow test
    ADDS R0, R0, R0           ; set v(overflow) flag
    BVC.W selftest_fail_handle         ; fails if v clear
    MOVS R0, #0xFFFFFFFF      ; prepares saturation test
    USAT R1,#10,R0            ; set q(saturation) flag
    MRS R0, APSR              ; get status register
    CMP R0, #0xB8000000       ; verifies that n=c=v=q=1
    BNE.W selftest_fail_handle         ; fails if q+n+c=v clear

    ; register r0 (holds value returned by the function)
    MOVS R0, #0xAAAAAAAA
    CMP R0, #0xAAAAAAAA
    BNE.W selftest_fail_handle
    MOVS R0, #0x55555555
    CMP R0, #0x55555555
    BNE.W selftest_fail_handle

    ; this is for control flow test (entry point)
    LDR R0,=ctrl_flow_cnt
    ; assumes r1 ok; if not, error will be detected by r1 test and ctrl flow test later on
    LDR R1,[R0]
    ADDS R1,R1,#0x3	 ; ctrl_flow_cnt += OxO3
    STR R1,[R0]

    ; link register (register r14)
    MOVS R0, R14              ; contains the return address and must be saved
    MOVS R14, #0xAAAAAAAA
    CMP R14, #0xAAAAAAAA
    BNE.W selftest_fail_handle
    MOVS R14, #0x55555555
    CMP R14, #0x55555555
    BNE.W selftest_fail_handle
    MOVS R14, R0
    MOVS R0, #0x0             ; for ramp test

    ; Register R1
    MOVS R1, #0xAAAAAAAA
    CMP R1, #0xAAAAAAAA
    BNE.W selftest_fail_handle
    MOVS R1, #0x55555555
    CMP R1, #0x55555555
    BNE.W selftest_fail_handle
    MOVS R1, #0x01            ; for ramp test

    ; Register R2
    MOVS R2, #0xAAAAAAAA
    CMP R2, #0xAAAAAAAA
    BNE.W selftest_fail_handle
    MOVS R2, #0x55555555
    CMP R2, #0x55555555
    BNE.W selftest_fail_handle
    MOVS R2, #0x02            ; for ramp test

    ; Register R3
    MOVS R3, #0xAAAAAAAA
    CMP R3, #0xAAAAAAAA
    BNE.W selftest_fail_handle
    MOVS R3, #0x55555555
    CMP R3, #0x55555555
    BNE.W selftest_fail_handle
    MOVS R3, #0x03            ; for ramp test

    ; Register R4
    MOVS R4, #0xAAAAAAAA
    CMP R4, #0xAAAAAAAA
    BNE.W selftest_fail_handle
    MOVS R4, #0x55555555
    CMP R4, #0x55555555
    BNE.W selftest_fail_handle
    MOVS R4, #0x04            ; for ramp test

    ; Register R5
    MOVS R5, #0xAAAAAAAA
    CMP R5, #0xAAAAAAAA
    BNE.W selftest_fail_handle
    MOVS R5, #0x55555555
    CMP R5, #0x55555555
    BNE.W selftest_fail_handle
    MOVS R5, #0x05            ; for ramp test

    ; Register R6
    MOVS R6, #0xAAAAAAAA
    CMP R6, #0xAAAAAAAA
    BNE.W selftest_fail_handle
    MOVS R6, #0x55555555
    CMP R6, #0x55555555
    BNE.W selftest_fail_handle
    MOVS R6, #0x06            ; for ramp test

    ; Register R7
    MOVS R7, #0xAAAAAAAA
    CMP R7, #0xAAAAAAAA
    BNE.W selftest_fail_handle
    MOVS R7, #0x55555555
    CMP R7, #0x55555555
    BNE.W selftest_fail_handle
    MOVS R7, #0x07            ; for ramp test

    ; Register R8
    MOVS R8, #0xAAAAAAAA
    CMP R8, #0xAAAAAAAA
    BNE.W selftest_fail_handle
    MOVS R8, #0x55555555
    CMP R8, #0x55555555
    BNE.W selftest_fail_handle
    MOVS R8, #0x08            ; for ramp test

    ; Register R9
    MOVS R9, #0xAAAAAAAA
    CMP R9, #0xAAAAAAAA
    BNE.W selftest_fail_handle
    MOVS R9, #0x55555555
    CMP R9, #0x55555555
    BNE.W selftest_fail_handle
    MOVS R9, #0x09            ; for ramp test

    ; Register R10
    MOVS R10, #0xAAAAAAAA
    CMP R10, #0xAAAAAAAA
    BNE selftest_fail_handle
    MOVS R10, #0x55555555
    CMP R10, #0x55555555
    BNE selftest_fail_handle
    MOVS R10, #0x0A           ; for ramp test

    ; Register R11
    MOVS R11, #0xAAAAAAAA
    CMP R11, #0xAAAAAAAA
    BNE selftest_fail_handle
    MOVS R11, #0x55555555
    CMP R11, #0x55555555
    BNE selftest_fail_handle
    MOVS R11, #0x0B           ; for ramp test

    ; Register R12
    MOVS R12, #0xAAAAAAAA
    CMP R12, #0xAAAAAAAA
    BNE selftest_fail_handle
    MOVS R12, #0x55555555
    CMP R12, #0x55555555
    BNE selftest_fail_handle
    MOVS R12, #0x0C           ; for ramp test

    ; Ramp pattern verification
    CMP R0, #0x00
    BNE selftest_fail_handle
    CMP R1, #0x01
    BNE selftest_fail_handle
    CMP R2, #0x02
    BNE selftest_fail_handle
    CMP R3, #0x03
    BNE selftest_fail_handle
    CMP R4, #0x04
    BNE selftest_fail_handle
    CMP R5, #0x05
    BNE selftest_fail_handle
    CMP R6, #0x06
    BNE selftest_fail_handle
    CMP R7, #0x07
    BNE selftest_fail_handle
    CMP R8, #0x08
    BNE selftest_fail_handle
    CMP R9, #0x09
    BNE selftest_fail_handle
    CMP R10, #0x0A
    BNE selftest_fail_handle
    CMP R11, #0x0B
    BNE selftest_fail_handle
    CMP R12, #0x0C
    BNE selftest_fail_handle

    ; process stack pointer (banked register r13)
    MRS R0, PSP         ; save process stack value
    MOV R1, #0xAAA8     ; test is different (psp is word aligned)
    MOVT R1, #0xAAAA    ; load in two times and 2 least significant bits cleared
    MSR PSP, R1         ; load process stack value
    MRS R2, PSP         ; get back process stack value
    CMP R2, R1          ; verify value
    BNE selftest_fail_handle
    MOV R1, #0x5554     ; test is different (psp is word aligned)
    MOVT R1, #0x5555    ; load in two times and 2 least significant bits cleared
    MSR PSP, R1         ; load process stack value
    MRS R2, PSP         ; get back process stack value
    CMP R2, R1          ; verify value
    BNE selftest_fail_handle
    MSR PSP, R0         ; restore process stack value

    ; stack pointer (register r13)
    MRS R0, MSP         ; save process stack value
    MOV R1, #0xAAA8     ; test is different (msp is word aligned)
    MOVT R1, #0xAAAA    ; load in two times and 2 least significant bits cleared
    MSR MSP, R1         ; load process stack value
    MRS R2, MSP         ; get back process stack value
    CMP R2, R1          ; verify value
    BNE selftest_fail_handle
    MOV R1, #0x5554     ; test is different (msp is word aligned)
    MOVT R1, #0x5555    ; load in two times and 2 least significant bits cleared
    MSR MSP, R1         ; load process stack value
    MRS R2, MSP         ; get back process stack value
    CMP R2, R1          ; verify value
    BNE selftest_fail_handle
    MSR MSP, R0         ; restore process stack value
	
    ; control flow test (exit point)
    LDR R0,=ctrl_flow_cnt_inv
    LDR R1,[R0]
    SUBS R1,R1,#0x3	 ; ctrl_flow_cnt_inv -= OxO3
    STR R1,[R0]

    ; if next instruction is not executed, r0 will hold a value different from 0
    MOVS R0, #0x1       ; cpu_test_success
    BX LR               ; return to the caller
    ENDP

  END


