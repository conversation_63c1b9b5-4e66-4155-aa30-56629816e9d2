Dependencies for Project 'an0041_demo', Target 'an0041_demo': (DO NOT MODIFY !)
CompilerVersion: 5050106::V5.05 update 1 (build 106)::ARMCC
F (..\src\main.c)(0x623BFD7A)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\inc -I ..\..\..\libraries\drivers\inc -I ..\..\..\project\at32f403a_407_board -I ..\..\..\libraries\cmsis\cm4\device_support -I ..\..\..\libraries\cmsis\cm4\core_support -I ..\..\..\middlewares\classb_lib\inc -I ..\mdk_v5

-ID:\keil5\ARM\Pack\ArteryTek\AT32F403A_407_DFP\2.1.6\Device\Include

-D__UVISION_VERSION="538" -DAT32F403AVGT7 -DAT32F403AVGT7 -DUSE_STDPERIPH_DRIVER -DAT_START_F403A_V1

-o .\objects\main.o --omf_browse .\objects\main.crf --depend .\objects\main.d)
I (..\..\..\project\at32f403a_407_board\at32f403a_407_board.h)(0x620396A5)
I (D:\keil5\ARM\ARMCC\include\stdio.h)(0x5475F300)
I (..\..\..\libraries\cmsis\cm4\device_support\at32f403a_407.h)(0x6203960D)
I (..\..\..\libraries\cmsis\cm4\core_support\core_cm4.h)(0x620395CA)
I (D:\keil5\ARM\ARMCC\include\stdint.h)(0x5475F300)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_version.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_compiler.h)(0x622B0D2A)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_armcc.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\core_support\mpu_armv7.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\device_support\system_at32f403a_407.h)(0x62039612)
I (..\..\..\libraries\drivers\inc\at32f403a_407_def.h)(0x6203961D)
I (..\inc\at32f403a_407_conf.h)(0x61CD8ABA)
I (..\..\..\libraries\drivers\inc\at32f403a_407_crm.h)(0x62039619)
I (..\..\..\libraries\drivers\inc\at32f403a_407_tmr.h)(0x6203962C)
I (..\..\..\libraries\drivers\inc\at32f403a_407_rtc.h)(0x62039628)
I (..\..\..\libraries\drivers\inc\at32f403a_407_bpr.h)(0x62039616)
I (..\..\..\libraries\drivers\inc\at32f403a_407_gpio.h)(0x62039623)
I (..\..\..\libraries\drivers\inc\at32f403a_407_i2c.h)(0x62039625)
I (..\..\..\libraries\drivers\inc\at32f403a_407_usart.h)(0x6203962E)
I (..\..\..\libraries\drivers\inc\at32f403a_407_pwc.h)(0x62039627)
I (..\..\..\libraries\drivers\inc\at32f403a_407_can.h)(0x62039617)
I (..\..\..\libraries\drivers\inc\at32f403a_407_adc.h)(0x62039615)
I (..\..\..\libraries\drivers\inc\at32f403a_407_dac.h)(0x6203961A)
I (..\..\..\libraries\drivers\inc\at32f403a_407_spi.h)(0x6203962B)
I (..\..\..\libraries\drivers\inc\at32f403a_407_dma.h)(0x6203961E)
I (..\..\..\libraries\drivers\inc\at32f403a_407_debug.h)(0x6203961C)
I (..\..\..\libraries\drivers\inc\at32f403a_407_flash.h)(0x62039622)
I (..\..\..\libraries\drivers\inc\at32f403a_407_crc.h)(0x62039618)
I (..\..\..\libraries\drivers\inc\at32f403a_407_wwdt.h)(0x62039632)
I (..\..\..\libraries\drivers\inc\at32f403a_407_wdt.h)(0x62039630)
I (..\..\..\libraries\drivers\inc\at32f403a_407_exint.h)(0x62039621)
I (..\..\..\libraries\drivers\inc\at32f403a_407_sdio.h)(0x6203962A)
I (..\..\..\libraries\drivers\inc\at32f403a_407_xmc.h)(0x62039633)
I (..\..\..\libraries\drivers\inc\at32f403a_407_acc.h)(0x62039613)
I (..\..\..\libraries\drivers\inc\at32f403a_407_misc.h)(0x62039626)
I (..\..\..\libraries\drivers\inc\at32f403a_407_usb.h)(0x6203962F)
I (..\..\..\libraries\drivers\inc\at32f403a_407_emac.h)(0x6203961F)
I (..\inc\at32f403a_407_clock.h)(0x61CD8AB8)
I (..\inc\main.h)(0x62329F1B)
I (..\inc\at32_selftest_param.h)(0x624D0D9A)
I (..\..\..\middlewares\classb_lib\inc\at32_selftest_lib.h)(0x623AD2D2)
I (..\..\..\middlewares\classb_lib\inc\at32_selftest_classbvar.h)(0x623AD2D2)
I (..\..\..\middlewares\classb_lib\inc\at32_selftest_startup.h)(0x6225F4AB)
I (..\..\..\middlewares\classb_lib\inc\at32_selftest_runtime.h)(0x6225F3FE)
I (..\..\..\middlewares\classb_lib\inc\at32_selftest_cpu.h)(0x6225B254)
I (..\..\..\middlewares\classb_lib\inc\at32_selftest_clock.h)(0x6225F0E9)
I (..\..\..\middlewares\classb_lib\inc\at32_selftest_crc.h)(0x6225C13C)
I (..\..\..\middlewares\classb_lib\inc\at32_selftest_ram.h)(0x6225F3FE)
F (..\src\at32f403a_407_clock.c)(0x61CD8ABE)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\inc -I ..\..\..\libraries\drivers\inc -I ..\..\..\project\at32f403a_407_board -I ..\..\..\libraries\cmsis\cm4\device_support -I ..\..\..\libraries\cmsis\cm4\core_support -I ..\..\..\middlewares\classb_lib\inc -I ..\mdk_v5

-ID:\keil5\ARM\Pack\ArteryTek\AT32F403A_407_DFP\2.1.6\Device\Include

-D__UVISION_VERSION="538" -DAT32F403AVGT7 -DAT32F403AVGT7 -DUSE_STDPERIPH_DRIVER -DAT_START_F403A_V1

-o .\objects\at32f403a_407_clock.o --omf_browse .\objects\at32f403a_407_clock.crf --depend .\objects\at32f403a_407_clock.d)
I (..\inc\at32f403a_407_clock.h)(0x61CD8AB8)
I (..\..\..\libraries\cmsis\cm4\device_support\at32f403a_407.h)(0x6203960D)
I (..\..\..\libraries\cmsis\cm4\core_support\core_cm4.h)(0x620395CA)
I (D:\keil5\ARM\ARMCC\include\stdint.h)(0x5475F300)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_version.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_compiler.h)(0x622B0D2A)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_armcc.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\core_support\mpu_armv7.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\device_support\system_at32f403a_407.h)(0x62039612)
I (..\..\..\libraries\drivers\inc\at32f403a_407_def.h)(0x6203961D)
I (..\inc\at32f403a_407_conf.h)(0x61CD8ABA)
I (..\..\..\libraries\drivers\inc\at32f403a_407_crm.h)(0x62039619)
I (..\..\..\libraries\drivers\inc\at32f403a_407_tmr.h)(0x6203962C)
I (..\..\..\libraries\drivers\inc\at32f403a_407_rtc.h)(0x62039628)
I (..\..\..\libraries\drivers\inc\at32f403a_407_bpr.h)(0x62039616)
I (..\..\..\libraries\drivers\inc\at32f403a_407_gpio.h)(0x62039623)
I (..\..\..\libraries\drivers\inc\at32f403a_407_i2c.h)(0x62039625)
I (..\..\..\libraries\drivers\inc\at32f403a_407_usart.h)(0x6203962E)
I (..\..\..\libraries\drivers\inc\at32f403a_407_pwc.h)(0x62039627)
I (..\..\..\libraries\drivers\inc\at32f403a_407_can.h)(0x62039617)
I (..\..\..\libraries\drivers\inc\at32f403a_407_adc.h)(0x62039615)
I (..\..\..\libraries\drivers\inc\at32f403a_407_dac.h)(0x6203961A)
I (..\..\..\libraries\drivers\inc\at32f403a_407_spi.h)(0x6203962B)
I (..\..\..\libraries\drivers\inc\at32f403a_407_dma.h)(0x6203961E)
I (..\..\..\libraries\drivers\inc\at32f403a_407_debug.h)(0x6203961C)
I (..\..\..\libraries\drivers\inc\at32f403a_407_flash.h)(0x62039622)
I (..\..\..\libraries\drivers\inc\at32f403a_407_crc.h)(0x62039618)
I (..\..\..\libraries\drivers\inc\at32f403a_407_wwdt.h)(0x62039632)
I (..\..\..\libraries\drivers\inc\at32f403a_407_wdt.h)(0x62039630)
I (..\..\..\libraries\drivers\inc\at32f403a_407_exint.h)(0x62039621)
I (..\..\..\libraries\drivers\inc\at32f403a_407_sdio.h)(0x6203962A)
I (..\..\..\libraries\drivers\inc\at32f403a_407_xmc.h)(0x62039633)
I (..\..\..\libraries\drivers\inc\at32f403a_407_acc.h)(0x62039613)
I (..\..\..\libraries\drivers\inc\at32f403a_407_misc.h)(0x62039626)
I (..\..\..\libraries\drivers\inc\at32f403a_407_usb.h)(0x6203962F)
I (..\..\..\libraries\drivers\inc\at32f403a_407_emac.h)(0x6203961F)
F (..\src\at32f403a_407_int.c)(0x623ADBF4)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\inc -I ..\..\..\libraries\drivers\inc -I ..\..\..\project\at32f403a_407_board -I ..\..\..\libraries\cmsis\cm4\device_support -I ..\..\..\libraries\cmsis\cm4\core_support -I ..\..\..\middlewares\classb_lib\inc -I ..\mdk_v5

-ID:\keil5\ARM\Pack\ArteryTek\AT32F403A_407_DFP\2.1.6\Device\Include

-D__UVISION_VERSION="538" -DAT32F403AVGT7 -DAT32F403AVGT7 -DUSE_STDPERIPH_DRIVER -DAT_START_F403A_V1

-o .\objects\at32f403a_407_int.o --omf_browse .\objects\at32f403a_407_int.crf --depend .\objects\at32f403a_407_int.d)
I (..\inc\at32f403a_407_int.h)(0x61CD8ABB)
I (..\..\..\libraries\cmsis\cm4\device_support\at32f403a_407.h)(0x6203960D)
I (..\..\..\libraries\cmsis\cm4\core_support\core_cm4.h)(0x620395CA)
I (D:\keil5\ARM\ARMCC\include\stdint.h)(0x5475F300)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_version.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_compiler.h)(0x622B0D2A)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_armcc.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\core_support\mpu_armv7.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\device_support\system_at32f403a_407.h)(0x62039612)
I (..\..\..\libraries\drivers\inc\at32f403a_407_def.h)(0x6203961D)
I (..\inc\at32f403a_407_conf.h)(0x61CD8ABA)
I (..\..\..\libraries\drivers\inc\at32f403a_407_crm.h)(0x62039619)
I (..\..\..\libraries\drivers\inc\at32f403a_407_tmr.h)(0x6203962C)
I (..\..\..\libraries\drivers\inc\at32f403a_407_rtc.h)(0x62039628)
I (..\..\..\libraries\drivers\inc\at32f403a_407_bpr.h)(0x62039616)
I (..\..\..\libraries\drivers\inc\at32f403a_407_gpio.h)(0x62039623)
I (..\..\..\libraries\drivers\inc\at32f403a_407_i2c.h)(0x62039625)
I (..\..\..\libraries\drivers\inc\at32f403a_407_usart.h)(0x6203962E)
I (..\..\..\libraries\drivers\inc\at32f403a_407_pwc.h)(0x62039627)
I (..\..\..\libraries\drivers\inc\at32f403a_407_can.h)(0x62039617)
I (..\..\..\libraries\drivers\inc\at32f403a_407_adc.h)(0x62039615)
I (..\..\..\libraries\drivers\inc\at32f403a_407_dac.h)(0x6203961A)
I (..\..\..\libraries\drivers\inc\at32f403a_407_spi.h)(0x6203962B)
I (..\..\..\libraries\drivers\inc\at32f403a_407_dma.h)(0x6203961E)
I (..\..\..\libraries\drivers\inc\at32f403a_407_debug.h)(0x6203961C)
I (..\..\..\libraries\drivers\inc\at32f403a_407_flash.h)(0x62039622)
I (..\..\..\libraries\drivers\inc\at32f403a_407_crc.h)(0x62039618)
I (..\..\..\libraries\drivers\inc\at32f403a_407_wwdt.h)(0x62039632)
I (..\..\..\libraries\drivers\inc\at32f403a_407_wdt.h)(0x62039630)
I (..\..\..\libraries\drivers\inc\at32f403a_407_exint.h)(0x62039621)
I (..\..\..\libraries\drivers\inc\at32f403a_407_sdio.h)(0x6203962A)
I (..\..\..\libraries\drivers\inc\at32f403a_407_xmc.h)(0x62039633)
I (..\..\..\libraries\drivers\inc\at32f403a_407_acc.h)(0x62039613)
I (..\..\..\libraries\drivers\inc\at32f403a_407_misc.h)(0x62039626)
I (..\..\..\libraries\drivers\inc\at32f403a_407_usb.h)(0x6203962F)
I (..\..\..\libraries\drivers\inc\at32f403a_407_emac.h)(0x6203961F)
I (..\inc\main.h)(0x62329F1B)
I (..\..\..\project\at32f403a_407_board\at32f403a_407_board.h)(0x620396A5)
I (D:\keil5\ARM\ARMCC\include\stdio.h)(0x5475F300)
I (..\inc\at32_selftest_param.h)(0x624D0D9A)
I (..\..\..\middlewares\classb_lib\inc\at32_selftest_lib.h)(0x623AD2D2)
I (..\..\..\middlewares\classb_lib\inc\at32_selftest_classbvar.h)(0x623AD2D2)
I (..\..\..\middlewares\classb_lib\inc\at32_selftest_startup.h)(0x6225F4AB)
I (..\..\..\middlewares\classb_lib\inc\at32_selftest_runtime.h)(0x6225F3FE)
I (..\..\..\middlewares\classb_lib\inc\at32_selftest_cpu.h)(0x6225B254)
I (..\..\..\middlewares\classb_lib\inc\at32_selftest_clock.h)(0x6225F0E9)
I (..\..\..\middlewares\classb_lib\inc\at32_selftest_crc.h)(0x6225C13C)
I (..\..\..\middlewares\classb_lib\inc\at32_selftest_ram.h)(0x6225F3FE)
F (.\at32_selftest_cpurun_keil.s)(0x622F3102)(--cpu Cortex-M4.fp.sp -g --apcs=interwork 

-ID:\keil5\ARM\Pack\ArteryTek\AT32F403A_407_DFP\2.1.6\Device\Include

--pd "__UVISION_VERSION SETA 538" --pd "AT32F403AVGT7 SETA 1"

--list .\listings\at32_selftest_cpurun_keil.lst --xref -o .\objects\at32_selftest_cpurun_keil.o --depend .\objects\at32_selftest_cpurun_keil.d)
F (.\at32_selftest_cpustart_keil.s)(0x622F2D6E)(--cpu Cortex-M4.fp.sp -g --apcs=interwork 

-ID:\keil5\ARM\Pack\ArteryTek\AT32F403A_407_DFP\2.1.6\Device\Include

--pd "__UVISION_VERSION SETA 538" --pd "AT32F403AVGT7 SETA 1"

--list .\listings\at32_selftest_cpustart_keil.lst --xref -o .\objects\at32_selftest_cpustart_keil.o --depend .\objects\at32_selftest_cpustart_keil.d)
F (.\at32_selftest_ram_keil.s)(0x624D3154)(--cpu Cortex-M4.fp.sp -g --apcs=interwork 

-ID:\keil5\ARM\Pack\ArteryTek\AT32F403A_407_DFP\2.1.6\Device\Include

--pd "__UVISION_VERSION SETA 538" --pd "AT32F403AVGT7 SETA 1"

--list .\listings\at32_selftest_ram_keil.lst --xref -o .\objects\at32_selftest_ram_keil.o --depend .\objects\at32_selftest_ram_keil.d)
F (.\crc32.c)(0x622B2024)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\inc -I ..\..\..\libraries\drivers\inc -I ..\..\..\project\at32f403a_407_board -I ..\..\..\libraries\cmsis\cm4\device_support -I ..\..\..\libraries\cmsis\cm4\core_support -I ..\..\..\middlewares\classb_lib\inc -I ..\mdk_v5

-ID:\keil5\ARM\Pack\ArteryTek\AT32F403A_407_DFP\2.1.6\Device\Include

-D__UVISION_VERSION="538" -DAT32F403AVGT7 -DAT32F403AVGT7 -DUSE_STDPERIPH_DRIVER -DAT_START_F403A_V1

-o .\objects\crc32.o --omf_browse .\objects\crc32.crf --depend .\objects\crc32.d)
I (crc32.h)(0x622B1FD5)
I (D:\keil5\ARM\ARMCC\include\stdint.h)(0x5475F300)
F (..\..\..\project\at32f403a_407_board\at32f403a_407_board.c)(0x620396A3)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\inc -I ..\..\..\libraries\drivers\inc -I ..\..\..\project\at32f403a_407_board -I ..\..\..\libraries\cmsis\cm4\device_support -I ..\..\..\libraries\cmsis\cm4\core_support -I ..\..\..\middlewares\classb_lib\inc -I ..\mdk_v5

-ID:\keil5\ARM\Pack\ArteryTek\AT32F403A_407_DFP\2.1.6\Device\Include

-D__UVISION_VERSION="538" -DAT32F403AVGT7 -DAT32F403AVGT7 -DUSE_STDPERIPH_DRIVER -DAT_START_F403A_V1

-o .\objects\at32f403a_407_board.o --omf_browse .\objects\at32f403a_407_board.crf --depend .\objects\at32f403a_407_board.d)
I (..\..\..\project\at32f403a_407_board\at32f403a_407_board.h)(0x620396A5)
I (D:\keil5\ARM\ARMCC\include\stdio.h)(0x5475F300)
I (..\..\..\libraries\cmsis\cm4\device_support\at32f403a_407.h)(0x6203960D)
I (..\..\..\libraries\cmsis\cm4\core_support\core_cm4.h)(0x620395CA)
I (D:\keil5\ARM\ARMCC\include\stdint.h)(0x5475F300)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_version.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_compiler.h)(0x622B0D2A)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_armcc.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\core_support\mpu_armv7.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\device_support\system_at32f403a_407.h)(0x62039612)
I (..\..\..\libraries\drivers\inc\at32f403a_407_def.h)(0x6203961D)
I (..\inc\at32f403a_407_conf.h)(0x61CD8ABA)
I (..\..\..\libraries\drivers\inc\at32f403a_407_crm.h)(0x62039619)
I (..\..\..\libraries\drivers\inc\at32f403a_407_tmr.h)(0x6203962C)
I (..\..\..\libraries\drivers\inc\at32f403a_407_rtc.h)(0x62039628)
I (..\..\..\libraries\drivers\inc\at32f403a_407_bpr.h)(0x62039616)
I (..\..\..\libraries\drivers\inc\at32f403a_407_gpio.h)(0x62039623)
I (..\..\..\libraries\drivers\inc\at32f403a_407_i2c.h)(0x62039625)
I (..\..\..\libraries\drivers\inc\at32f403a_407_usart.h)(0x6203962E)
I (..\..\..\libraries\drivers\inc\at32f403a_407_pwc.h)(0x62039627)
I (..\..\..\libraries\drivers\inc\at32f403a_407_can.h)(0x62039617)
I (..\..\..\libraries\drivers\inc\at32f403a_407_adc.h)(0x62039615)
I (..\..\..\libraries\drivers\inc\at32f403a_407_dac.h)(0x6203961A)
I (..\..\..\libraries\drivers\inc\at32f403a_407_spi.h)(0x6203962B)
I (..\..\..\libraries\drivers\inc\at32f403a_407_dma.h)(0x6203961E)
I (..\..\..\libraries\drivers\inc\at32f403a_407_debug.h)(0x6203961C)
I (..\..\..\libraries\drivers\inc\at32f403a_407_flash.h)(0x62039622)
I (..\..\..\libraries\drivers\inc\at32f403a_407_crc.h)(0x62039618)
I (..\..\..\libraries\drivers\inc\at32f403a_407_wwdt.h)(0x62039632)
I (..\..\..\libraries\drivers\inc\at32f403a_407_wdt.h)(0x62039630)
I (..\..\..\libraries\drivers\inc\at32f403a_407_exint.h)(0x62039621)
I (..\..\..\libraries\drivers\inc\at32f403a_407_sdio.h)(0x6203962A)
I (..\..\..\libraries\drivers\inc\at32f403a_407_xmc.h)(0x62039633)
I (..\..\..\libraries\drivers\inc\at32f403a_407_acc.h)(0x62039613)
I (..\..\..\libraries\drivers\inc\at32f403a_407_misc.h)(0x62039626)
I (..\..\..\libraries\drivers\inc\at32f403a_407_usb.h)(0x6203962F)
I (..\..\..\libraries\drivers\inc\at32f403a_407_emac.h)(0x6203961F)
F (..\..\..\libraries\drivers\src\at32f403a_407_acc.c)(0x62039634)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\inc -I ..\..\..\libraries\drivers\inc -I ..\..\..\project\at32f403a_407_board -I ..\..\..\libraries\cmsis\cm4\device_support -I ..\..\..\libraries\cmsis\cm4\core_support -I ..\..\..\middlewares\classb_lib\inc -I ..\mdk_v5

-ID:\keil5\ARM\Pack\ArteryTek\AT32F403A_407_DFP\2.1.6\Device\Include

-D__UVISION_VERSION="538" -DAT32F403AVGT7 -DAT32F403AVGT7 -DUSE_STDPERIPH_DRIVER -DAT_START_F403A_V1

-o .\objects\at32f403a_407_acc.o --omf_browse .\objects\at32f403a_407_acc.crf --depend .\objects\at32f403a_407_acc.d)
I (..\inc\at32f403a_407_conf.h)(0x61CD8ABA)
I (..\..\..\libraries\drivers\inc\at32f403a_407_crm.h)(0x62039619)
I (..\..\..\libraries\cmsis\cm4\device_support\at32f403a_407.h)(0x6203960D)
I (..\..\..\libraries\cmsis\cm4\core_support\core_cm4.h)(0x620395CA)
I (D:\keil5\ARM\ARMCC\include\stdint.h)(0x5475F300)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_version.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_compiler.h)(0x622B0D2A)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_armcc.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\core_support\mpu_armv7.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\device_support\system_at32f403a_407.h)(0x62039612)
I (..\..\..\libraries\drivers\inc\at32f403a_407_def.h)(0x6203961D)
I (..\..\..\libraries\drivers\inc\at32f403a_407_tmr.h)(0x6203962C)
I (..\..\..\libraries\drivers\inc\at32f403a_407_rtc.h)(0x62039628)
I (..\..\..\libraries\drivers\inc\at32f403a_407_bpr.h)(0x62039616)
I (..\..\..\libraries\drivers\inc\at32f403a_407_gpio.h)(0x62039623)
I (..\..\..\libraries\drivers\inc\at32f403a_407_i2c.h)(0x62039625)
I (..\..\..\libraries\drivers\inc\at32f403a_407_usart.h)(0x6203962E)
I (..\..\..\libraries\drivers\inc\at32f403a_407_pwc.h)(0x62039627)
I (..\..\..\libraries\drivers\inc\at32f403a_407_can.h)(0x62039617)
I (..\..\..\libraries\drivers\inc\at32f403a_407_adc.h)(0x62039615)
I (..\..\..\libraries\drivers\inc\at32f403a_407_dac.h)(0x6203961A)
I (..\..\..\libraries\drivers\inc\at32f403a_407_spi.h)(0x6203962B)
I (..\..\..\libraries\drivers\inc\at32f403a_407_dma.h)(0x6203961E)
I (..\..\..\libraries\drivers\inc\at32f403a_407_debug.h)(0x6203961C)
I (..\..\..\libraries\drivers\inc\at32f403a_407_flash.h)(0x62039622)
I (..\..\..\libraries\drivers\inc\at32f403a_407_crc.h)(0x62039618)
I (..\..\..\libraries\drivers\inc\at32f403a_407_wwdt.h)(0x62039632)
I (..\..\..\libraries\drivers\inc\at32f403a_407_wdt.h)(0x62039630)
I (..\..\..\libraries\drivers\inc\at32f403a_407_exint.h)(0x62039621)
I (..\..\..\libraries\drivers\inc\at32f403a_407_sdio.h)(0x6203962A)
I (..\..\..\libraries\drivers\inc\at32f403a_407_xmc.h)(0x62039633)
I (..\..\..\libraries\drivers\inc\at32f403a_407_acc.h)(0x62039613)
I (..\..\..\libraries\drivers\inc\at32f403a_407_misc.h)(0x62039626)
I (..\..\..\libraries\drivers\inc\at32f403a_407_usb.h)(0x6203962F)
I (..\..\..\libraries\drivers\inc\at32f403a_407_emac.h)(0x6203961F)
F (..\..\..\libraries\drivers\src\at32f403a_407_adc.c)(0x62039635)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\inc -I ..\..\..\libraries\drivers\inc -I ..\..\..\project\at32f403a_407_board -I ..\..\..\libraries\cmsis\cm4\device_support -I ..\..\..\libraries\cmsis\cm4\core_support -I ..\..\..\middlewares\classb_lib\inc -I ..\mdk_v5

-ID:\keil5\ARM\Pack\ArteryTek\AT32F403A_407_DFP\2.1.6\Device\Include

-D__UVISION_VERSION="538" -DAT32F403AVGT7 -DAT32F403AVGT7 -DUSE_STDPERIPH_DRIVER -DAT_START_F403A_V1

-o .\objects\at32f403a_407_adc.o --omf_browse .\objects\at32f403a_407_adc.crf --depend .\objects\at32f403a_407_adc.d)
I (..\inc\at32f403a_407_conf.h)(0x61CD8ABA)
I (..\..\..\libraries\drivers\inc\at32f403a_407_crm.h)(0x62039619)
I (..\..\..\libraries\cmsis\cm4\device_support\at32f403a_407.h)(0x6203960D)
I (..\..\..\libraries\cmsis\cm4\core_support\core_cm4.h)(0x620395CA)
I (D:\keil5\ARM\ARMCC\include\stdint.h)(0x5475F300)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_version.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_compiler.h)(0x622B0D2A)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_armcc.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\core_support\mpu_armv7.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\device_support\system_at32f403a_407.h)(0x62039612)
I (..\..\..\libraries\drivers\inc\at32f403a_407_def.h)(0x6203961D)
I (..\..\..\libraries\drivers\inc\at32f403a_407_tmr.h)(0x6203962C)
I (..\..\..\libraries\drivers\inc\at32f403a_407_rtc.h)(0x62039628)
I (..\..\..\libraries\drivers\inc\at32f403a_407_bpr.h)(0x62039616)
I (..\..\..\libraries\drivers\inc\at32f403a_407_gpio.h)(0x62039623)
I (..\..\..\libraries\drivers\inc\at32f403a_407_i2c.h)(0x62039625)
I (..\..\..\libraries\drivers\inc\at32f403a_407_usart.h)(0x6203962E)
I (..\..\..\libraries\drivers\inc\at32f403a_407_pwc.h)(0x62039627)
I (..\..\..\libraries\drivers\inc\at32f403a_407_can.h)(0x62039617)
I (..\..\..\libraries\drivers\inc\at32f403a_407_adc.h)(0x62039615)
I (..\..\..\libraries\drivers\inc\at32f403a_407_dac.h)(0x6203961A)
I (..\..\..\libraries\drivers\inc\at32f403a_407_spi.h)(0x6203962B)
I (..\..\..\libraries\drivers\inc\at32f403a_407_dma.h)(0x6203961E)
I (..\..\..\libraries\drivers\inc\at32f403a_407_debug.h)(0x6203961C)
I (..\..\..\libraries\drivers\inc\at32f403a_407_flash.h)(0x62039622)
I (..\..\..\libraries\drivers\inc\at32f403a_407_crc.h)(0x62039618)
I (..\..\..\libraries\drivers\inc\at32f403a_407_wwdt.h)(0x62039632)
I (..\..\..\libraries\drivers\inc\at32f403a_407_wdt.h)(0x62039630)
I (..\..\..\libraries\drivers\inc\at32f403a_407_exint.h)(0x62039621)
I (..\..\..\libraries\drivers\inc\at32f403a_407_sdio.h)(0x6203962A)
I (..\..\..\libraries\drivers\inc\at32f403a_407_xmc.h)(0x62039633)
I (..\..\..\libraries\drivers\inc\at32f403a_407_acc.h)(0x62039613)
I (..\..\..\libraries\drivers\inc\at32f403a_407_misc.h)(0x62039626)
I (..\..\..\libraries\drivers\inc\at32f403a_407_usb.h)(0x6203962F)
I (..\..\..\libraries\drivers\inc\at32f403a_407_emac.h)(0x6203961F)
F (..\..\..\libraries\drivers\src\at32f403a_407_bpr.c)(0x62039637)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\inc -I ..\..\..\libraries\drivers\inc -I ..\..\..\project\at32f403a_407_board -I ..\..\..\libraries\cmsis\cm4\device_support -I ..\..\..\libraries\cmsis\cm4\core_support -I ..\..\..\middlewares\classb_lib\inc -I ..\mdk_v5

-ID:\keil5\ARM\Pack\ArteryTek\AT32F403A_407_DFP\2.1.6\Device\Include

-D__UVISION_VERSION="538" -DAT32F403AVGT7 -DAT32F403AVGT7 -DUSE_STDPERIPH_DRIVER -DAT_START_F403A_V1

-o .\objects\at32f403a_407_bpr.o --omf_browse .\objects\at32f403a_407_bpr.crf --depend .\objects\at32f403a_407_bpr.d)
I (..\inc\at32f403a_407_conf.h)(0x61CD8ABA)
I (..\..\..\libraries\drivers\inc\at32f403a_407_crm.h)(0x62039619)
I (..\..\..\libraries\cmsis\cm4\device_support\at32f403a_407.h)(0x6203960D)
I (..\..\..\libraries\cmsis\cm4\core_support\core_cm4.h)(0x620395CA)
I (D:\keil5\ARM\ARMCC\include\stdint.h)(0x5475F300)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_version.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_compiler.h)(0x622B0D2A)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_armcc.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\core_support\mpu_armv7.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\device_support\system_at32f403a_407.h)(0x62039612)
I (..\..\..\libraries\drivers\inc\at32f403a_407_def.h)(0x6203961D)
I (..\..\..\libraries\drivers\inc\at32f403a_407_tmr.h)(0x6203962C)
I (..\..\..\libraries\drivers\inc\at32f403a_407_rtc.h)(0x62039628)
I (..\..\..\libraries\drivers\inc\at32f403a_407_bpr.h)(0x62039616)
I (..\..\..\libraries\drivers\inc\at32f403a_407_gpio.h)(0x62039623)
I (..\..\..\libraries\drivers\inc\at32f403a_407_i2c.h)(0x62039625)
I (..\..\..\libraries\drivers\inc\at32f403a_407_usart.h)(0x6203962E)
I (..\..\..\libraries\drivers\inc\at32f403a_407_pwc.h)(0x62039627)
I (..\..\..\libraries\drivers\inc\at32f403a_407_can.h)(0x62039617)
I (..\..\..\libraries\drivers\inc\at32f403a_407_adc.h)(0x62039615)
I (..\..\..\libraries\drivers\inc\at32f403a_407_dac.h)(0x6203961A)
I (..\..\..\libraries\drivers\inc\at32f403a_407_spi.h)(0x6203962B)
I (..\..\..\libraries\drivers\inc\at32f403a_407_dma.h)(0x6203961E)
I (..\..\..\libraries\drivers\inc\at32f403a_407_debug.h)(0x6203961C)
I (..\..\..\libraries\drivers\inc\at32f403a_407_flash.h)(0x62039622)
I (..\..\..\libraries\drivers\inc\at32f403a_407_crc.h)(0x62039618)
I (..\..\..\libraries\drivers\inc\at32f403a_407_wwdt.h)(0x62039632)
I (..\..\..\libraries\drivers\inc\at32f403a_407_wdt.h)(0x62039630)
I (..\..\..\libraries\drivers\inc\at32f403a_407_exint.h)(0x62039621)
I (..\..\..\libraries\drivers\inc\at32f403a_407_sdio.h)(0x6203962A)
I (..\..\..\libraries\drivers\inc\at32f403a_407_xmc.h)(0x62039633)
I (..\..\..\libraries\drivers\inc\at32f403a_407_acc.h)(0x62039613)
I (..\..\..\libraries\drivers\inc\at32f403a_407_misc.h)(0x62039626)
I (..\..\..\libraries\drivers\inc\at32f403a_407_usb.h)(0x6203962F)
I (..\..\..\libraries\drivers\inc\at32f403a_407_emac.h)(0x6203961F)
F (..\..\..\libraries\drivers\src\at32f403a_407_can.c)(0x62039638)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\inc -I ..\..\..\libraries\drivers\inc -I ..\..\..\project\at32f403a_407_board -I ..\..\..\libraries\cmsis\cm4\device_support -I ..\..\..\libraries\cmsis\cm4\core_support -I ..\..\..\middlewares\classb_lib\inc -I ..\mdk_v5

-ID:\keil5\ARM\Pack\ArteryTek\AT32F403A_407_DFP\2.1.6\Device\Include

-D__UVISION_VERSION="538" -DAT32F403AVGT7 -DAT32F403AVGT7 -DUSE_STDPERIPH_DRIVER -DAT_START_F403A_V1

-o .\objects\at32f403a_407_can.o --omf_browse .\objects\at32f403a_407_can.crf --depend .\objects\at32f403a_407_can.d)
I (..\inc\at32f403a_407_conf.h)(0x61CD8ABA)
I (..\..\..\libraries\drivers\inc\at32f403a_407_crm.h)(0x62039619)
I (..\..\..\libraries\cmsis\cm4\device_support\at32f403a_407.h)(0x6203960D)
I (..\..\..\libraries\cmsis\cm4\core_support\core_cm4.h)(0x620395CA)
I (D:\keil5\ARM\ARMCC\include\stdint.h)(0x5475F300)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_version.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_compiler.h)(0x622B0D2A)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_armcc.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\core_support\mpu_armv7.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\device_support\system_at32f403a_407.h)(0x62039612)
I (..\..\..\libraries\drivers\inc\at32f403a_407_def.h)(0x6203961D)
I (..\..\..\libraries\drivers\inc\at32f403a_407_tmr.h)(0x6203962C)
I (..\..\..\libraries\drivers\inc\at32f403a_407_rtc.h)(0x62039628)
I (..\..\..\libraries\drivers\inc\at32f403a_407_bpr.h)(0x62039616)
I (..\..\..\libraries\drivers\inc\at32f403a_407_gpio.h)(0x62039623)
I (..\..\..\libraries\drivers\inc\at32f403a_407_i2c.h)(0x62039625)
I (..\..\..\libraries\drivers\inc\at32f403a_407_usart.h)(0x6203962E)
I (..\..\..\libraries\drivers\inc\at32f403a_407_pwc.h)(0x62039627)
I (..\..\..\libraries\drivers\inc\at32f403a_407_can.h)(0x62039617)
I (..\..\..\libraries\drivers\inc\at32f403a_407_adc.h)(0x62039615)
I (..\..\..\libraries\drivers\inc\at32f403a_407_dac.h)(0x6203961A)
I (..\..\..\libraries\drivers\inc\at32f403a_407_spi.h)(0x6203962B)
I (..\..\..\libraries\drivers\inc\at32f403a_407_dma.h)(0x6203961E)
I (..\..\..\libraries\drivers\inc\at32f403a_407_debug.h)(0x6203961C)
I (..\..\..\libraries\drivers\inc\at32f403a_407_flash.h)(0x62039622)
I (..\..\..\libraries\drivers\inc\at32f403a_407_crc.h)(0x62039618)
I (..\..\..\libraries\drivers\inc\at32f403a_407_wwdt.h)(0x62039632)
I (..\..\..\libraries\drivers\inc\at32f403a_407_wdt.h)(0x62039630)
I (..\..\..\libraries\drivers\inc\at32f403a_407_exint.h)(0x62039621)
I (..\..\..\libraries\drivers\inc\at32f403a_407_sdio.h)(0x6203962A)
I (..\..\..\libraries\drivers\inc\at32f403a_407_xmc.h)(0x62039633)
I (..\..\..\libraries\drivers\inc\at32f403a_407_acc.h)(0x62039613)
I (..\..\..\libraries\drivers\inc\at32f403a_407_misc.h)(0x62039626)
I (..\..\..\libraries\drivers\inc\at32f403a_407_usb.h)(0x6203962F)
I (..\..\..\libraries\drivers\inc\at32f403a_407_emac.h)(0x6203961F)
F (..\..\..\libraries\drivers\src\at32f403a_407_crc.c)(0x6203963A)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\inc -I ..\..\..\libraries\drivers\inc -I ..\..\..\project\at32f403a_407_board -I ..\..\..\libraries\cmsis\cm4\device_support -I ..\..\..\libraries\cmsis\cm4\core_support -I ..\..\..\middlewares\classb_lib\inc -I ..\mdk_v5

-ID:\keil5\ARM\Pack\ArteryTek\AT32F403A_407_DFP\2.1.6\Device\Include

-D__UVISION_VERSION="538" -DAT32F403AVGT7 -DAT32F403AVGT7 -DUSE_STDPERIPH_DRIVER -DAT_START_F403A_V1

-o .\objects\at32f403a_407_crc.o --omf_browse .\objects\at32f403a_407_crc.crf --depend .\objects\at32f403a_407_crc.d)
I (..\inc\at32f403a_407_conf.h)(0x61CD8ABA)
I (..\..\..\libraries\drivers\inc\at32f403a_407_crm.h)(0x62039619)
I (..\..\..\libraries\cmsis\cm4\device_support\at32f403a_407.h)(0x6203960D)
I (..\..\..\libraries\cmsis\cm4\core_support\core_cm4.h)(0x620395CA)
I (D:\keil5\ARM\ARMCC\include\stdint.h)(0x5475F300)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_version.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_compiler.h)(0x622B0D2A)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_armcc.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\core_support\mpu_armv7.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\device_support\system_at32f403a_407.h)(0x62039612)
I (..\..\..\libraries\drivers\inc\at32f403a_407_def.h)(0x6203961D)
I (..\..\..\libraries\drivers\inc\at32f403a_407_tmr.h)(0x6203962C)
I (..\..\..\libraries\drivers\inc\at32f403a_407_rtc.h)(0x62039628)
I (..\..\..\libraries\drivers\inc\at32f403a_407_bpr.h)(0x62039616)
I (..\..\..\libraries\drivers\inc\at32f403a_407_gpio.h)(0x62039623)
I (..\..\..\libraries\drivers\inc\at32f403a_407_i2c.h)(0x62039625)
I (..\..\..\libraries\drivers\inc\at32f403a_407_usart.h)(0x6203962E)
I (..\..\..\libraries\drivers\inc\at32f403a_407_pwc.h)(0x62039627)
I (..\..\..\libraries\drivers\inc\at32f403a_407_can.h)(0x62039617)
I (..\..\..\libraries\drivers\inc\at32f403a_407_adc.h)(0x62039615)
I (..\..\..\libraries\drivers\inc\at32f403a_407_dac.h)(0x6203961A)
I (..\..\..\libraries\drivers\inc\at32f403a_407_spi.h)(0x6203962B)
I (..\..\..\libraries\drivers\inc\at32f403a_407_dma.h)(0x6203961E)
I (..\..\..\libraries\drivers\inc\at32f403a_407_debug.h)(0x6203961C)
I (..\..\..\libraries\drivers\inc\at32f403a_407_flash.h)(0x62039622)
I (..\..\..\libraries\drivers\inc\at32f403a_407_crc.h)(0x62039618)
I (..\..\..\libraries\drivers\inc\at32f403a_407_wwdt.h)(0x62039632)
I (..\..\..\libraries\drivers\inc\at32f403a_407_wdt.h)(0x62039630)
I (..\..\..\libraries\drivers\inc\at32f403a_407_exint.h)(0x62039621)
I (..\..\..\libraries\drivers\inc\at32f403a_407_sdio.h)(0x6203962A)
I (..\..\..\libraries\drivers\inc\at32f403a_407_xmc.h)(0x62039633)
I (..\..\..\libraries\drivers\inc\at32f403a_407_acc.h)(0x62039613)
I (..\..\..\libraries\drivers\inc\at32f403a_407_misc.h)(0x62039626)
I (..\..\..\libraries\drivers\inc\at32f403a_407_usb.h)(0x6203962F)
I (..\..\..\libraries\drivers\inc\at32f403a_407_emac.h)(0x6203961F)
F (..\..\..\libraries\drivers\src\at32f403a_407_crm.c)(0x6203963B)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\inc -I ..\..\..\libraries\drivers\inc -I ..\..\..\project\at32f403a_407_board -I ..\..\..\libraries\cmsis\cm4\device_support -I ..\..\..\libraries\cmsis\cm4\core_support -I ..\..\..\middlewares\classb_lib\inc -I ..\mdk_v5

-ID:\keil5\ARM\Pack\ArteryTek\AT32F403A_407_DFP\2.1.6\Device\Include

-D__UVISION_VERSION="538" -DAT32F403AVGT7 -DAT32F403AVGT7 -DUSE_STDPERIPH_DRIVER -DAT_START_F403A_V1

-o .\objects\at32f403a_407_crm.o --omf_browse .\objects\at32f403a_407_crm.crf --depend .\objects\at32f403a_407_crm.d)
I (..\inc\at32f403a_407_conf.h)(0x61CD8ABA)
I (..\..\..\libraries\drivers\inc\at32f403a_407_crm.h)(0x62039619)
I (..\..\..\libraries\cmsis\cm4\device_support\at32f403a_407.h)(0x6203960D)
I (..\..\..\libraries\cmsis\cm4\core_support\core_cm4.h)(0x620395CA)
I (D:\keil5\ARM\ARMCC\include\stdint.h)(0x5475F300)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_version.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_compiler.h)(0x622B0D2A)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_armcc.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\core_support\mpu_armv7.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\device_support\system_at32f403a_407.h)(0x62039612)
I (..\..\..\libraries\drivers\inc\at32f403a_407_def.h)(0x6203961D)
I (..\..\..\libraries\drivers\inc\at32f403a_407_tmr.h)(0x6203962C)
I (..\..\..\libraries\drivers\inc\at32f403a_407_rtc.h)(0x62039628)
I (..\..\..\libraries\drivers\inc\at32f403a_407_bpr.h)(0x62039616)
I (..\..\..\libraries\drivers\inc\at32f403a_407_gpio.h)(0x62039623)
I (..\..\..\libraries\drivers\inc\at32f403a_407_i2c.h)(0x62039625)
I (..\..\..\libraries\drivers\inc\at32f403a_407_usart.h)(0x6203962E)
I (..\..\..\libraries\drivers\inc\at32f403a_407_pwc.h)(0x62039627)
I (..\..\..\libraries\drivers\inc\at32f403a_407_can.h)(0x62039617)
I (..\..\..\libraries\drivers\inc\at32f403a_407_adc.h)(0x62039615)
I (..\..\..\libraries\drivers\inc\at32f403a_407_dac.h)(0x6203961A)
I (..\..\..\libraries\drivers\inc\at32f403a_407_spi.h)(0x6203962B)
I (..\..\..\libraries\drivers\inc\at32f403a_407_dma.h)(0x6203961E)
I (..\..\..\libraries\drivers\inc\at32f403a_407_debug.h)(0x6203961C)
I (..\..\..\libraries\drivers\inc\at32f403a_407_flash.h)(0x62039622)
I (..\..\..\libraries\drivers\inc\at32f403a_407_crc.h)(0x62039618)
I (..\..\..\libraries\drivers\inc\at32f403a_407_wwdt.h)(0x62039632)
I (..\..\..\libraries\drivers\inc\at32f403a_407_wdt.h)(0x62039630)
I (..\..\..\libraries\drivers\inc\at32f403a_407_exint.h)(0x62039621)
I (..\..\..\libraries\drivers\inc\at32f403a_407_sdio.h)(0x6203962A)
I (..\..\..\libraries\drivers\inc\at32f403a_407_xmc.h)(0x62039633)
I (..\..\..\libraries\drivers\inc\at32f403a_407_acc.h)(0x62039613)
I (..\..\..\libraries\drivers\inc\at32f403a_407_misc.h)(0x62039626)
I (..\..\..\libraries\drivers\inc\at32f403a_407_usb.h)(0x6203962F)
I (..\..\..\libraries\drivers\inc\at32f403a_407_emac.h)(0x6203961F)
F (..\..\..\libraries\drivers\src\at32f403a_407_dac.c)(0x6203963C)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\inc -I ..\..\..\libraries\drivers\inc -I ..\..\..\project\at32f403a_407_board -I ..\..\..\libraries\cmsis\cm4\device_support -I ..\..\..\libraries\cmsis\cm4\core_support -I ..\..\..\middlewares\classb_lib\inc -I ..\mdk_v5

-ID:\keil5\ARM\Pack\ArteryTek\AT32F403A_407_DFP\2.1.6\Device\Include

-D__UVISION_VERSION="538" -DAT32F403AVGT7 -DAT32F403AVGT7 -DUSE_STDPERIPH_DRIVER -DAT_START_F403A_V1

-o .\objects\at32f403a_407_dac.o --omf_browse .\objects\at32f403a_407_dac.crf --depend .\objects\at32f403a_407_dac.d)
I (..\inc\at32f403a_407_conf.h)(0x61CD8ABA)
I (..\..\..\libraries\drivers\inc\at32f403a_407_crm.h)(0x62039619)
I (..\..\..\libraries\cmsis\cm4\device_support\at32f403a_407.h)(0x6203960D)
I (..\..\..\libraries\cmsis\cm4\core_support\core_cm4.h)(0x620395CA)
I (D:\keil5\ARM\ARMCC\include\stdint.h)(0x5475F300)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_version.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_compiler.h)(0x622B0D2A)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_armcc.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\core_support\mpu_armv7.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\device_support\system_at32f403a_407.h)(0x62039612)
I (..\..\..\libraries\drivers\inc\at32f403a_407_def.h)(0x6203961D)
I (..\..\..\libraries\drivers\inc\at32f403a_407_tmr.h)(0x6203962C)
I (..\..\..\libraries\drivers\inc\at32f403a_407_rtc.h)(0x62039628)
I (..\..\..\libraries\drivers\inc\at32f403a_407_bpr.h)(0x62039616)
I (..\..\..\libraries\drivers\inc\at32f403a_407_gpio.h)(0x62039623)
I (..\..\..\libraries\drivers\inc\at32f403a_407_i2c.h)(0x62039625)
I (..\..\..\libraries\drivers\inc\at32f403a_407_usart.h)(0x6203962E)
I (..\..\..\libraries\drivers\inc\at32f403a_407_pwc.h)(0x62039627)
I (..\..\..\libraries\drivers\inc\at32f403a_407_can.h)(0x62039617)
I (..\..\..\libraries\drivers\inc\at32f403a_407_adc.h)(0x62039615)
I (..\..\..\libraries\drivers\inc\at32f403a_407_dac.h)(0x6203961A)
I (..\..\..\libraries\drivers\inc\at32f403a_407_spi.h)(0x6203962B)
I (..\..\..\libraries\drivers\inc\at32f403a_407_dma.h)(0x6203961E)
I (..\..\..\libraries\drivers\inc\at32f403a_407_debug.h)(0x6203961C)
I (..\..\..\libraries\drivers\inc\at32f403a_407_flash.h)(0x62039622)
I (..\..\..\libraries\drivers\inc\at32f403a_407_crc.h)(0x62039618)
I (..\..\..\libraries\drivers\inc\at32f403a_407_wwdt.h)(0x62039632)
I (..\..\..\libraries\drivers\inc\at32f403a_407_wdt.h)(0x62039630)
I (..\..\..\libraries\drivers\inc\at32f403a_407_exint.h)(0x62039621)
I (..\..\..\libraries\drivers\inc\at32f403a_407_sdio.h)(0x6203962A)
I (..\..\..\libraries\drivers\inc\at32f403a_407_xmc.h)(0x62039633)
I (..\..\..\libraries\drivers\inc\at32f403a_407_acc.h)(0x62039613)
I (..\..\..\libraries\drivers\inc\at32f403a_407_misc.h)(0x62039626)
I (..\..\..\libraries\drivers\inc\at32f403a_407_usb.h)(0x6203962F)
I (..\..\..\libraries\drivers\inc\at32f403a_407_emac.h)(0x6203961F)
F (..\..\..\libraries\drivers\src\at32f403a_407_debug.c)(0x6203963E)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\inc -I ..\..\..\libraries\drivers\inc -I ..\..\..\project\at32f403a_407_board -I ..\..\..\libraries\cmsis\cm4\device_support -I ..\..\..\libraries\cmsis\cm4\core_support -I ..\..\..\middlewares\classb_lib\inc -I ..\mdk_v5

-ID:\keil5\ARM\Pack\ArteryTek\AT32F403A_407_DFP\2.1.6\Device\Include

-D__UVISION_VERSION="538" -DAT32F403AVGT7 -DAT32F403AVGT7 -DUSE_STDPERIPH_DRIVER -DAT_START_F403A_V1

-o .\objects\at32f403a_407_debug.o --omf_browse .\objects\at32f403a_407_debug.crf --depend .\objects\at32f403a_407_debug.d)
I (..\inc\at32f403a_407_conf.h)(0x61CD8ABA)
I (..\..\..\libraries\drivers\inc\at32f403a_407_crm.h)(0x62039619)
I (..\..\..\libraries\cmsis\cm4\device_support\at32f403a_407.h)(0x6203960D)
I (..\..\..\libraries\cmsis\cm4\core_support\core_cm4.h)(0x620395CA)
I (D:\keil5\ARM\ARMCC\include\stdint.h)(0x5475F300)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_version.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_compiler.h)(0x622B0D2A)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_armcc.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\core_support\mpu_armv7.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\device_support\system_at32f403a_407.h)(0x62039612)
I (..\..\..\libraries\drivers\inc\at32f403a_407_def.h)(0x6203961D)
I (..\..\..\libraries\drivers\inc\at32f403a_407_tmr.h)(0x6203962C)
I (..\..\..\libraries\drivers\inc\at32f403a_407_rtc.h)(0x62039628)
I (..\..\..\libraries\drivers\inc\at32f403a_407_bpr.h)(0x62039616)
I (..\..\..\libraries\drivers\inc\at32f403a_407_gpio.h)(0x62039623)
I (..\..\..\libraries\drivers\inc\at32f403a_407_i2c.h)(0x62039625)
I (..\..\..\libraries\drivers\inc\at32f403a_407_usart.h)(0x6203962E)
I (..\..\..\libraries\drivers\inc\at32f403a_407_pwc.h)(0x62039627)
I (..\..\..\libraries\drivers\inc\at32f403a_407_can.h)(0x62039617)
I (..\..\..\libraries\drivers\inc\at32f403a_407_adc.h)(0x62039615)
I (..\..\..\libraries\drivers\inc\at32f403a_407_dac.h)(0x6203961A)
I (..\..\..\libraries\drivers\inc\at32f403a_407_spi.h)(0x6203962B)
I (..\..\..\libraries\drivers\inc\at32f403a_407_dma.h)(0x6203961E)
I (..\..\..\libraries\drivers\inc\at32f403a_407_debug.h)(0x6203961C)
I (..\..\..\libraries\drivers\inc\at32f403a_407_flash.h)(0x62039622)
I (..\..\..\libraries\drivers\inc\at32f403a_407_crc.h)(0x62039618)
I (..\..\..\libraries\drivers\inc\at32f403a_407_wwdt.h)(0x62039632)
I (..\..\..\libraries\drivers\inc\at32f403a_407_wdt.h)(0x62039630)
I (..\..\..\libraries\drivers\inc\at32f403a_407_exint.h)(0x62039621)
I (..\..\..\libraries\drivers\inc\at32f403a_407_sdio.h)(0x6203962A)
I (..\..\..\libraries\drivers\inc\at32f403a_407_xmc.h)(0x62039633)
I (..\..\..\libraries\drivers\inc\at32f403a_407_acc.h)(0x62039613)
I (..\..\..\libraries\drivers\inc\at32f403a_407_misc.h)(0x62039626)
I (..\..\..\libraries\drivers\inc\at32f403a_407_usb.h)(0x6203962F)
I (..\..\..\libraries\drivers\inc\at32f403a_407_emac.h)(0x6203961F)
F (..\..\..\libraries\drivers\src\at32f403a_407_dma.c)(0x6203963F)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\inc -I ..\..\..\libraries\drivers\inc -I ..\..\..\project\at32f403a_407_board -I ..\..\..\libraries\cmsis\cm4\device_support -I ..\..\..\libraries\cmsis\cm4\core_support -I ..\..\..\middlewares\classb_lib\inc -I ..\mdk_v5

-ID:\keil5\ARM\Pack\ArteryTek\AT32F403A_407_DFP\2.1.6\Device\Include

-D__UVISION_VERSION="538" -DAT32F403AVGT7 -DAT32F403AVGT7 -DUSE_STDPERIPH_DRIVER -DAT_START_F403A_V1

-o .\objects\at32f403a_407_dma.o --omf_browse .\objects\at32f403a_407_dma.crf --depend .\objects\at32f403a_407_dma.d)
I (..\inc\at32f403a_407_conf.h)(0x61CD8ABA)
I (..\..\..\libraries\drivers\inc\at32f403a_407_crm.h)(0x62039619)
I (..\..\..\libraries\cmsis\cm4\device_support\at32f403a_407.h)(0x6203960D)
I (..\..\..\libraries\cmsis\cm4\core_support\core_cm4.h)(0x620395CA)
I (D:\keil5\ARM\ARMCC\include\stdint.h)(0x5475F300)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_version.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_compiler.h)(0x622B0D2A)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_armcc.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\core_support\mpu_armv7.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\device_support\system_at32f403a_407.h)(0x62039612)
I (..\..\..\libraries\drivers\inc\at32f403a_407_def.h)(0x6203961D)
I (..\..\..\libraries\drivers\inc\at32f403a_407_tmr.h)(0x6203962C)
I (..\..\..\libraries\drivers\inc\at32f403a_407_rtc.h)(0x62039628)
I (..\..\..\libraries\drivers\inc\at32f403a_407_bpr.h)(0x62039616)
I (..\..\..\libraries\drivers\inc\at32f403a_407_gpio.h)(0x62039623)
I (..\..\..\libraries\drivers\inc\at32f403a_407_i2c.h)(0x62039625)
I (..\..\..\libraries\drivers\inc\at32f403a_407_usart.h)(0x6203962E)
I (..\..\..\libraries\drivers\inc\at32f403a_407_pwc.h)(0x62039627)
I (..\..\..\libraries\drivers\inc\at32f403a_407_can.h)(0x62039617)
I (..\..\..\libraries\drivers\inc\at32f403a_407_adc.h)(0x62039615)
I (..\..\..\libraries\drivers\inc\at32f403a_407_dac.h)(0x6203961A)
I (..\..\..\libraries\drivers\inc\at32f403a_407_spi.h)(0x6203962B)
I (..\..\..\libraries\drivers\inc\at32f403a_407_dma.h)(0x6203961E)
I (..\..\..\libraries\drivers\inc\at32f403a_407_debug.h)(0x6203961C)
I (..\..\..\libraries\drivers\inc\at32f403a_407_flash.h)(0x62039622)
I (..\..\..\libraries\drivers\inc\at32f403a_407_crc.h)(0x62039618)
I (..\..\..\libraries\drivers\inc\at32f403a_407_wwdt.h)(0x62039632)
I (..\..\..\libraries\drivers\inc\at32f403a_407_wdt.h)(0x62039630)
I (..\..\..\libraries\drivers\inc\at32f403a_407_exint.h)(0x62039621)
I (..\..\..\libraries\drivers\inc\at32f403a_407_sdio.h)(0x6203962A)
I (..\..\..\libraries\drivers\inc\at32f403a_407_xmc.h)(0x62039633)
I (..\..\..\libraries\drivers\inc\at32f403a_407_acc.h)(0x62039613)
I (..\..\..\libraries\drivers\inc\at32f403a_407_misc.h)(0x62039626)
I (..\..\..\libraries\drivers\inc\at32f403a_407_usb.h)(0x6203962F)
I (..\..\..\libraries\drivers\inc\at32f403a_407_emac.h)(0x6203961F)
F (..\..\..\libraries\drivers\src\at32f403a_407_emac.c)(0x62039640)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\inc -I ..\..\..\libraries\drivers\inc -I ..\..\..\project\at32f403a_407_board -I ..\..\..\libraries\cmsis\cm4\device_support -I ..\..\..\libraries\cmsis\cm4\core_support -I ..\..\..\middlewares\classb_lib\inc -I ..\mdk_v5

-ID:\keil5\ARM\Pack\ArteryTek\AT32F403A_407_DFP\2.1.6\Device\Include

-D__UVISION_VERSION="538" -DAT32F403AVGT7 -DAT32F403AVGT7 -DUSE_STDPERIPH_DRIVER -DAT_START_F403A_V1

-o .\objects\at32f403a_407_emac.o --omf_browse .\objects\at32f403a_407_emac.crf --depend .\objects\at32f403a_407_emac.d)
I (..\inc\at32f403a_407_conf.h)(0x61CD8ABA)
I (..\..\..\libraries\drivers\inc\at32f403a_407_crm.h)(0x62039619)
I (..\..\..\libraries\cmsis\cm4\device_support\at32f403a_407.h)(0x6203960D)
I (..\..\..\libraries\cmsis\cm4\core_support\core_cm4.h)(0x620395CA)
I (D:\keil5\ARM\ARMCC\include\stdint.h)(0x5475F300)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_version.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_compiler.h)(0x622B0D2A)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_armcc.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\core_support\mpu_armv7.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\device_support\system_at32f403a_407.h)(0x62039612)
I (..\..\..\libraries\drivers\inc\at32f403a_407_def.h)(0x6203961D)
I (..\..\..\libraries\drivers\inc\at32f403a_407_tmr.h)(0x6203962C)
I (..\..\..\libraries\drivers\inc\at32f403a_407_rtc.h)(0x62039628)
I (..\..\..\libraries\drivers\inc\at32f403a_407_bpr.h)(0x62039616)
I (..\..\..\libraries\drivers\inc\at32f403a_407_gpio.h)(0x62039623)
I (..\..\..\libraries\drivers\inc\at32f403a_407_i2c.h)(0x62039625)
I (..\..\..\libraries\drivers\inc\at32f403a_407_usart.h)(0x6203962E)
I (..\..\..\libraries\drivers\inc\at32f403a_407_pwc.h)(0x62039627)
I (..\..\..\libraries\drivers\inc\at32f403a_407_can.h)(0x62039617)
I (..\..\..\libraries\drivers\inc\at32f403a_407_adc.h)(0x62039615)
I (..\..\..\libraries\drivers\inc\at32f403a_407_dac.h)(0x6203961A)
I (..\..\..\libraries\drivers\inc\at32f403a_407_spi.h)(0x6203962B)
I (..\..\..\libraries\drivers\inc\at32f403a_407_dma.h)(0x6203961E)
I (..\..\..\libraries\drivers\inc\at32f403a_407_debug.h)(0x6203961C)
I (..\..\..\libraries\drivers\inc\at32f403a_407_flash.h)(0x62039622)
I (..\..\..\libraries\drivers\inc\at32f403a_407_crc.h)(0x62039618)
I (..\..\..\libraries\drivers\inc\at32f403a_407_wwdt.h)(0x62039632)
I (..\..\..\libraries\drivers\inc\at32f403a_407_wdt.h)(0x62039630)
I (..\..\..\libraries\drivers\inc\at32f403a_407_exint.h)(0x62039621)
I (..\..\..\libraries\drivers\inc\at32f403a_407_sdio.h)(0x6203962A)
I (..\..\..\libraries\drivers\inc\at32f403a_407_xmc.h)(0x62039633)
I (..\..\..\libraries\drivers\inc\at32f403a_407_acc.h)(0x62039613)
I (..\..\..\libraries\drivers\inc\at32f403a_407_misc.h)(0x62039626)
I (..\..\..\libraries\drivers\inc\at32f403a_407_usb.h)(0x6203962F)
I (..\..\..\libraries\drivers\inc\at32f403a_407_emac.h)(0x6203961F)
F (..\..\..\libraries\drivers\src\at32f403a_407_exint.c)(0x62039642)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\inc -I ..\..\..\libraries\drivers\inc -I ..\..\..\project\at32f403a_407_board -I ..\..\..\libraries\cmsis\cm4\device_support -I ..\..\..\libraries\cmsis\cm4\core_support -I ..\..\..\middlewares\classb_lib\inc -I ..\mdk_v5

-ID:\keil5\ARM\Pack\ArteryTek\AT32F403A_407_DFP\2.1.6\Device\Include

-D__UVISION_VERSION="538" -DAT32F403AVGT7 -DAT32F403AVGT7 -DUSE_STDPERIPH_DRIVER -DAT_START_F403A_V1

-o .\objects\at32f403a_407_exint.o --omf_browse .\objects\at32f403a_407_exint.crf --depend .\objects\at32f403a_407_exint.d)
I (..\inc\at32f403a_407_conf.h)(0x61CD8ABA)
I (..\..\..\libraries\drivers\inc\at32f403a_407_crm.h)(0x62039619)
I (..\..\..\libraries\cmsis\cm4\device_support\at32f403a_407.h)(0x6203960D)
I (..\..\..\libraries\cmsis\cm4\core_support\core_cm4.h)(0x620395CA)
I (D:\keil5\ARM\ARMCC\include\stdint.h)(0x5475F300)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_version.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_compiler.h)(0x622B0D2A)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_armcc.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\core_support\mpu_armv7.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\device_support\system_at32f403a_407.h)(0x62039612)
I (..\..\..\libraries\drivers\inc\at32f403a_407_def.h)(0x6203961D)
I (..\..\..\libraries\drivers\inc\at32f403a_407_tmr.h)(0x6203962C)
I (..\..\..\libraries\drivers\inc\at32f403a_407_rtc.h)(0x62039628)
I (..\..\..\libraries\drivers\inc\at32f403a_407_bpr.h)(0x62039616)
I (..\..\..\libraries\drivers\inc\at32f403a_407_gpio.h)(0x62039623)
I (..\..\..\libraries\drivers\inc\at32f403a_407_i2c.h)(0x62039625)
I (..\..\..\libraries\drivers\inc\at32f403a_407_usart.h)(0x6203962E)
I (..\..\..\libraries\drivers\inc\at32f403a_407_pwc.h)(0x62039627)
I (..\..\..\libraries\drivers\inc\at32f403a_407_can.h)(0x62039617)
I (..\..\..\libraries\drivers\inc\at32f403a_407_adc.h)(0x62039615)
I (..\..\..\libraries\drivers\inc\at32f403a_407_dac.h)(0x6203961A)
I (..\..\..\libraries\drivers\inc\at32f403a_407_spi.h)(0x6203962B)
I (..\..\..\libraries\drivers\inc\at32f403a_407_dma.h)(0x6203961E)
I (..\..\..\libraries\drivers\inc\at32f403a_407_debug.h)(0x6203961C)
I (..\..\..\libraries\drivers\inc\at32f403a_407_flash.h)(0x62039622)
I (..\..\..\libraries\drivers\inc\at32f403a_407_crc.h)(0x62039618)
I (..\..\..\libraries\drivers\inc\at32f403a_407_wwdt.h)(0x62039632)
I (..\..\..\libraries\drivers\inc\at32f403a_407_wdt.h)(0x62039630)
I (..\..\..\libraries\drivers\inc\at32f403a_407_exint.h)(0x62039621)
I (..\..\..\libraries\drivers\inc\at32f403a_407_sdio.h)(0x6203962A)
I (..\..\..\libraries\drivers\inc\at32f403a_407_xmc.h)(0x62039633)
I (..\..\..\libraries\drivers\inc\at32f403a_407_acc.h)(0x62039613)
I (..\..\..\libraries\drivers\inc\at32f403a_407_misc.h)(0x62039626)
I (..\..\..\libraries\drivers\inc\at32f403a_407_usb.h)(0x6203962F)
I (..\..\..\libraries\drivers\inc\at32f403a_407_emac.h)(0x6203961F)
F (..\..\..\libraries\drivers\src\at32f403a_407_flash.c)(0x62039643)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\inc -I ..\..\..\libraries\drivers\inc -I ..\..\..\project\at32f403a_407_board -I ..\..\..\libraries\cmsis\cm4\device_support -I ..\..\..\libraries\cmsis\cm4\core_support -I ..\..\..\middlewares\classb_lib\inc -I ..\mdk_v5

-ID:\keil5\ARM\Pack\ArteryTek\AT32F403A_407_DFP\2.1.6\Device\Include

-D__UVISION_VERSION="538" -DAT32F403AVGT7 -DAT32F403AVGT7 -DUSE_STDPERIPH_DRIVER -DAT_START_F403A_V1

-o .\objects\at32f403a_407_flash.o --omf_browse .\objects\at32f403a_407_flash.crf --depend .\objects\at32f403a_407_flash.d)
I (..\inc\at32f403a_407_conf.h)(0x61CD8ABA)
I (..\..\..\libraries\drivers\inc\at32f403a_407_crm.h)(0x62039619)
I (..\..\..\libraries\cmsis\cm4\device_support\at32f403a_407.h)(0x6203960D)
I (..\..\..\libraries\cmsis\cm4\core_support\core_cm4.h)(0x620395CA)
I (D:\keil5\ARM\ARMCC\include\stdint.h)(0x5475F300)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_version.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_compiler.h)(0x622B0D2A)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_armcc.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\core_support\mpu_armv7.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\device_support\system_at32f403a_407.h)(0x62039612)
I (..\..\..\libraries\drivers\inc\at32f403a_407_def.h)(0x6203961D)
I (..\..\..\libraries\drivers\inc\at32f403a_407_tmr.h)(0x6203962C)
I (..\..\..\libraries\drivers\inc\at32f403a_407_rtc.h)(0x62039628)
I (..\..\..\libraries\drivers\inc\at32f403a_407_bpr.h)(0x62039616)
I (..\..\..\libraries\drivers\inc\at32f403a_407_gpio.h)(0x62039623)
I (..\..\..\libraries\drivers\inc\at32f403a_407_i2c.h)(0x62039625)
I (..\..\..\libraries\drivers\inc\at32f403a_407_usart.h)(0x6203962E)
I (..\..\..\libraries\drivers\inc\at32f403a_407_pwc.h)(0x62039627)
I (..\..\..\libraries\drivers\inc\at32f403a_407_can.h)(0x62039617)
I (..\..\..\libraries\drivers\inc\at32f403a_407_adc.h)(0x62039615)
I (..\..\..\libraries\drivers\inc\at32f403a_407_dac.h)(0x6203961A)
I (..\..\..\libraries\drivers\inc\at32f403a_407_spi.h)(0x6203962B)
I (..\..\..\libraries\drivers\inc\at32f403a_407_dma.h)(0x6203961E)
I (..\..\..\libraries\drivers\inc\at32f403a_407_debug.h)(0x6203961C)
I (..\..\..\libraries\drivers\inc\at32f403a_407_flash.h)(0x62039622)
I (..\..\..\libraries\drivers\inc\at32f403a_407_crc.h)(0x62039618)
I (..\..\..\libraries\drivers\inc\at32f403a_407_wwdt.h)(0x62039632)
I (..\..\..\libraries\drivers\inc\at32f403a_407_wdt.h)(0x62039630)
I (..\..\..\libraries\drivers\inc\at32f403a_407_exint.h)(0x62039621)
I (..\..\..\libraries\drivers\inc\at32f403a_407_sdio.h)(0x6203962A)
I (..\..\..\libraries\drivers\inc\at32f403a_407_xmc.h)(0x62039633)
I (..\..\..\libraries\drivers\inc\at32f403a_407_acc.h)(0x62039613)
I (..\..\..\libraries\drivers\inc\at32f403a_407_misc.h)(0x62039626)
I (..\..\..\libraries\drivers\inc\at32f403a_407_usb.h)(0x6203962F)
I (..\..\..\libraries\drivers\inc\at32f403a_407_emac.h)(0x6203961F)
F (..\..\..\libraries\drivers\src\at32f403a_407_gpio.c)(0x62039644)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\inc -I ..\..\..\libraries\drivers\inc -I ..\..\..\project\at32f403a_407_board -I ..\..\..\libraries\cmsis\cm4\device_support -I ..\..\..\libraries\cmsis\cm4\core_support -I ..\..\..\middlewares\classb_lib\inc -I ..\mdk_v5

-ID:\keil5\ARM\Pack\ArteryTek\AT32F403A_407_DFP\2.1.6\Device\Include

-D__UVISION_VERSION="538" -DAT32F403AVGT7 -DAT32F403AVGT7 -DUSE_STDPERIPH_DRIVER -DAT_START_F403A_V1

-o .\objects\at32f403a_407_gpio.o --omf_browse .\objects\at32f403a_407_gpio.crf --depend .\objects\at32f403a_407_gpio.d)
I (..\inc\at32f403a_407_conf.h)(0x61CD8ABA)
I (..\..\..\libraries\drivers\inc\at32f403a_407_crm.h)(0x62039619)
I (..\..\..\libraries\cmsis\cm4\device_support\at32f403a_407.h)(0x6203960D)
I (..\..\..\libraries\cmsis\cm4\core_support\core_cm4.h)(0x620395CA)
I (D:\keil5\ARM\ARMCC\include\stdint.h)(0x5475F300)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_version.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_compiler.h)(0x622B0D2A)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_armcc.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\core_support\mpu_armv7.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\device_support\system_at32f403a_407.h)(0x62039612)
I (..\..\..\libraries\drivers\inc\at32f403a_407_def.h)(0x6203961D)
I (..\..\..\libraries\drivers\inc\at32f403a_407_tmr.h)(0x6203962C)
I (..\..\..\libraries\drivers\inc\at32f403a_407_rtc.h)(0x62039628)
I (..\..\..\libraries\drivers\inc\at32f403a_407_bpr.h)(0x62039616)
I (..\..\..\libraries\drivers\inc\at32f403a_407_gpio.h)(0x62039623)
I (..\..\..\libraries\drivers\inc\at32f403a_407_i2c.h)(0x62039625)
I (..\..\..\libraries\drivers\inc\at32f403a_407_usart.h)(0x6203962E)
I (..\..\..\libraries\drivers\inc\at32f403a_407_pwc.h)(0x62039627)
I (..\..\..\libraries\drivers\inc\at32f403a_407_can.h)(0x62039617)
I (..\..\..\libraries\drivers\inc\at32f403a_407_adc.h)(0x62039615)
I (..\..\..\libraries\drivers\inc\at32f403a_407_dac.h)(0x6203961A)
I (..\..\..\libraries\drivers\inc\at32f403a_407_spi.h)(0x6203962B)
I (..\..\..\libraries\drivers\inc\at32f403a_407_dma.h)(0x6203961E)
I (..\..\..\libraries\drivers\inc\at32f403a_407_debug.h)(0x6203961C)
I (..\..\..\libraries\drivers\inc\at32f403a_407_flash.h)(0x62039622)
I (..\..\..\libraries\drivers\inc\at32f403a_407_crc.h)(0x62039618)
I (..\..\..\libraries\drivers\inc\at32f403a_407_wwdt.h)(0x62039632)
I (..\..\..\libraries\drivers\inc\at32f403a_407_wdt.h)(0x62039630)
I (..\..\..\libraries\drivers\inc\at32f403a_407_exint.h)(0x62039621)
I (..\..\..\libraries\drivers\inc\at32f403a_407_sdio.h)(0x6203962A)
I (..\..\..\libraries\drivers\inc\at32f403a_407_xmc.h)(0x62039633)
I (..\..\..\libraries\drivers\inc\at32f403a_407_acc.h)(0x62039613)
I (..\..\..\libraries\drivers\inc\at32f403a_407_misc.h)(0x62039626)
I (..\..\..\libraries\drivers\inc\at32f403a_407_usb.h)(0x6203962F)
I (..\..\..\libraries\drivers\inc\at32f403a_407_emac.h)(0x6203961F)
F (..\..\..\libraries\drivers\src\at32f403a_407_i2c.c)(0x62039646)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\inc -I ..\..\..\libraries\drivers\inc -I ..\..\..\project\at32f403a_407_board -I ..\..\..\libraries\cmsis\cm4\device_support -I ..\..\..\libraries\cmsis\cm4\core_support -I ..\..\..\middlewares\classb_lib\inc -I ..\mdk_v5

-ID:\keil5\ARM\Pack\ArteryTek\AT32F403A_407_DFP\2.1.6\Device\Include

-D__UVISION_VERSION="538" -DAT32F403AVGT7 -DAT32F403AVGT7 -DUSE_STDPERIPH_DRIVER -DAT_START_F403A_V1

-o .\objects\at32f403a_407_i2c.o --omf_browse .\objects\at32f403a_407_i2c.crf --depend .\objects\at32f403a_407_i2c.d)
I (..\inc\at32f403a_407_conf.h)(0x61CD8ABA)
I (..\..\..\libraries\drivers\inc\at32f403a_407_crm.h)(0x62039619)
I (..\..\..\libraries\cmsis\cm4\device_support\at32f403a_407.h)(0x6203960D)
I (..\..\..\libraries\cmsis\cm4\core_support\core_cm4.h)(0x620395CA)
I (D:\keil5\ARM\ARMCC\include\stdint.h)(0x5475F300)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_version.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_compiler.h)(0x622B0D2A)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_armcc.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\core_support\mpu_armv7.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\device_support\system_at32f403a_407.h)(0x62039612)
I (..\..\..\libraries\drivers\inc\at32f403a_407_def.h)(0x6203961D)
I (..\..\..\libraries\drivers\inc\at32f403a_407_tmr.h)(0x6203962C)
I (..\..\..\libraries\drivers\inc\at32f403a_407_rtc.h)(0x62039628)
I (..\..\..\libraries\drivers\inc\at32f403a_407_bpr.h)(0x62039616)
I (..\..\..\libraries\drivers\inc\at32f403a_407_gpio.h)(0x62039623)
I (..\..\..\libraries\drivers\inc\at32f403a_407_i2c.h)(0x62039625)
I (..\..\..\libraries\drivers\inc\at32f403a_407_usart.h)(0x6203962E)
I (..\..\..\libraries\drivers\inc\at32f403a_407_pwc.h)(0x62039627)
I (..\..\..\libraries\drivers\inc\at32f403a_407_can.h)(0x62039617)
I (..\..\..\libraries\drivers\inc\at32f403a_407_adc.h)(0x62039615)
I (..\..\..\libraries\drivers\inc\at32f403a_407_dac.h)(0x6203961A)
I (..\..\..\libraries\drivers\inc\at32f403a_407_spi.h)(0x6203962B)
I (..\..\..\libraries\drivers\inc\at32f403a_407_dma.h)(0x6203961E)
I (..\..\..\libraries\drivers\inc\at32f403a_407_debug.h)(0x6203961C)
I (..\..\..\libraries\drivers\inc\at32f403a_407_flash.h)(0x62039622)
I (..\..\..\libraries\drivers\inc\at32f403a_407_crc.h)(0x62039618)
I (..\..\..\libraries\drivers\inc\at32f403a_407_wwdt.h)(0x62039632)
I (..\..\..\libraries\drivers\inc\at32f403a_407_wdt.h)(0x62039630)
I (..\..\..\libraries\drivers\inc\at32f403a_407_exint.h)(0x62039621)
I (..\..\..\libraries\drivers\inc\at32f403a_407_sdio.h)(0x6203962A)
I (..\..\..\libraries\drivers\inc\at32f403a_407_xmc.h)(0x62039633)
I (..\..\..\libraries\drivers\inc\at32f403a_407_acc.h)(0x62039613)
I (..\..\..\libraries\drivers\inc\at32f403a_407_misc.h)(0x62039626)
I (..\..\..\libraries\drivers\inc\at32f403a_407_usb.h)(0x6203962F)
I (..\..\..\libraries\drivers\inc\at32f403a_407_emac.h)(0x6203961F)
F (..\..\..\libraries\drivers\src\at32f403a_407_misc.c)(0x62039647)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\inc -I ..\..\..\libraries\drivers\inc -I ..\..\..\project\at32f403a_407_board -I ..\..\..\libraries\cmsis\cm4\device_support -I ..\..\..\libraries\cmsis\cm4\core_support -I ..\..\..\middlewares\classb_lib\inc -I ..\mdk_v5

-ID:\keil5\ARM\Pack\ArteryTek\AT32F403A_407_DFP\2.1.6\Device\Include

-D__UVISION_VERSION="538" -DAT32F403AVGT7 -DAT32F403AVGT7 -DUSE_STDPERIPH_DRIVER -DAT_START_F403A_V1

-o .\objects\at32f403a_407_misc.o --omf_browse .\objects\at32f403a_407_misc.crf --depend .\objects\at32f403a_407_misc.d)
I (..\inc\at32f403a_407_conf.h)(0x61CD8ABA)
I (..\..\..\libraries\drivers\inc\at32f403a_407_crm.h)(0x62039619)
I (..\..\..\libraries\cmsis\cm4\device_support\at32f403a_407.h)(0x6203960D)
I (..\..\..\libraries\cmsis\cm4\core_support\core_cm4.h)(0x620395CA)
I (D:\keil5\ARM\ARMCC\include\stdint.h)(0x5475F300)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_version.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_compiler.h)(0x622B0D2A)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_armcc.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\core_support\mpu_armv7.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\device_support\system_at32f403a_407.h)(0x62039612)
I (..\..\..\libraries\drivers\inc\at32f403a_407_def.h)(0x6203961D)
I (..\..\..\libraries\drivers\inc\at32f403a_407_tmr.h)(0x6203962C)
I (..\..\..\libraries\drivers\inc\at32f403a_407_rtc.h)(0x62039628)
I (..\..\..\libraries\drivers\inc\at32f403a_407_bpr.h)(0x62039616)
I (..\..\..\libraries\drivers\inc\at32f403a_407_gpio.h)(0x62039623)
I (..\..\..\libraries\drivers\inc\at32f403a_407_i2c.h)(0x62039625)
I (..\..\..\libraries\drivers\inc\at32f403a_407_usart.h)(0x6203962E)
I (..\..\..\libraries\drivers\inc\at32f403a_407_pwc.h)(0x62039627)
I (..\..\..\libraries\drivers\inc\at32f403a_407_can.h)(0x62039617)
I (..\..\..\libraries\drivers\inc\at32f403a_407_adc.h)(0x62039615)
I (..\..\..\libraries\drivers\inc\at32f403a_407_dac.h)(0x6203961A)
I (..\..\..\libraries\drivers\inc\at32f403a_407_spi.h)(0x6203962B)
I (..\..\..\libraries\drivers\inc\at32f403a_407_dma.h)(0x6203961E)
I (..\..\..\libraries\drivers\inc\at32f403a_407_debug.h)(0x6203961C)
I (..\..\..\libraries\drivers\inc\at32f403a_407_flash.h)(0x62039622)
I (..\..\..\libraries\drivers\inc\at32f403a_407_crc.h)(0x62039618)
I (..\..\..\libraries\drivers\inc\at32f403a_407_wwdt.h)(0x62039632)
I (..\..\..\libraries\drivers\inc\at32f403a_407_wdt.h)(0x62039630)
I (..\..\..\libraries\drivers\inc\at32f403a_407_exint.h)(0x62039621)
I (..\..\..\libraries\drivers\inc\at32f403a_407_sdio.h)(0x6203962A)
I (..\..\..\libraries\drivers\inc\at32f403a_407_xmc.h)(0x62039633)
I (..\..\..\libraries\drivers\inc\at32f403a_407_acc.h)(0x62039613)
I (..\..\..\libraries\drivers\inc\at32f403a_407_misc.h)(0x62039626)
I (..\..\..\libraries\drivers\inc\at32f403a_407_usb.h)(0x6203962F)
I (..\..\..\libraries\drivers\inc\at32f403a_407_emac.h)(0x6203961F)
F (..\..\..\libraries\drivers\src\at32f403a_407_pwc.c)(0x62039649)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\inc -I ..\..\..\libraries\drivers\inc -I ..\..\..\project\at32f403a_407_board -I ..\..\..\libraries\cmsis\cm4\device_support -I ..\..\..\libraries\cmsis\cm4\core_support -I ..\..\..\middlewares\classb_lib\inc -I ..\mdk_v5

-ID:\keil5\ARM\Pack\ArteryTek\AT32F403A_407_DFP\2.1.6\Device\Include

-D__UVISION_VERSION="538" -DAT32F403AVGT7 -DAT32F403AVGT7 -DUSE_STDPERIPH_DRIVER -DAT_START_F403A_V1

-o .\objects\at32f403a_407_pwc.o --omf_browse .\objects\at32f403a_407_pwc.crf --depend .\objects\at32f403a_407_pwc.d)
I (..\inc\at32f403a_407_conf.h)(0x61CD8ABA)
I (..\..\..\libraries\drivers\inc\at32f403a_407_crm.h)(0x62039619)
I (..\..\..\libraries\cmsis\cm4\device_support\at32f403a_407.h)(0x6203960D)
I (..\..\..\libraries\cmsis\cm4\core_support\core_cm4.h)(0x620395CA)
I (D:\keil5\ARM\ARMCC\include\stdint.h)(0x5475F300)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_version.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_compiler.h)(0x622B0D2A)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_armcc.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\core_support\mpu_armv7.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\device_support\system_at32f403a_407.h)(0x62039612)
I (..\..\..\libraries\drivers\inc\at32f403a_407_def.h)(0x6203961D)
I (..\..\..\libraries\drivers\inc\at32f403a_407_tmr.h)(0x6203962C)
I (..\..\..\libraries\drivers\inc\at32f403a_407_rtc.h)(0x62039628)
I (..\..\..\libraries\drivers\inc\at32f403a_407_bpr.h)(0x62039616)
I (..\..\..\libraries\drivers\inc\at32f403a_407_gpio.h)(0x62039623)
I (..\..\..\libraries\drivers\inc\at32f403a_407_i2c.h)(0x62039625)
I (..\..\..\libraries\drivers\inc\at32f403a_407_usart.h)(0x6203962E)
I (..\..\..\libraries\drivers\inc\at32f403a_407_pwc.h)(0x62039627)
I (..\..\..\libraries\drivers\inc\at32f403a_407_can.h)(0x62039617)
I (..\..\..\libraries\drivers\inc\at32f403a_407_adc.h)(0x62039615)
I (..\..\..\libraries\drivers\inc\at32f403a_407_dac.h)(0x6203961A)
I (..\..\..\libraries\drivers\inc\at32f403a_407_spi.h)(0x6203962B)
I (..\..\..\libraries\drivers\inc\at32f403a_407_dma.h)(0x6203961E)
I (..\..\..\libraries\drivers\inc\at32f403a_407_debug.h)(0x6203961C)
I (..\..\..\libraries\drivers\inc\at32f403a_407_flash.h)(0x62039622)
I (..\..\..\libraries\drivers\inc\at32f403a_407_crc.h)(0x62039618)
I (..\..\..\libraries\drivers\inc\at32f403a_407_wwdt.h)(0x62039632)
I (..\..\..\libraries\drivers\inc\at32f403a_407_wdt.h)(0x62039630)
I (..\..\..\libraries\drivers\inc\at32f403a_407_exint.h)(0x62039621)
I (..\..\..\libraries\drivers\inc\at32f403a_407_sdio.h)(0x6203962A)
I (..\..\..\libraries\drivers\inc\at32f403a_407_xmc.h)(0x62039633)
I (..\..\..\libraries\drivers\inc\at32f403a_407_acc.h)(0x62039613)
I (..\..\..\libraries\drivers\inc\at32f403a_407_misc.h)(0x62039626)
I (..\..\..\libraries\drivers\inc\at32f403a_407_usb.h)(0x6203962F)
I (..\..\..\libraries\drivers\inc\at32f403a_407_emac.h)(0x6203961F)
F (..\..\..\libraries\drivers\src\at32f403a_407_rtc.c)(0x6203964A)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\inc -I ..\..\..\libraries\drivers\inc -I ..\..\..\project\at32f403a_407_board -I ..\..\..\libraries\cmsis\cm4\device_support -I ..\..\..\libraries\cmsis\cm4\core_support -I ..\..\..\middlewares\classb_lib\inc -I ..\mdk_v5

-ID:\keil5\ARM\Pack\ArteryTek\AT32F403A_407_DFP\2.1.6\Device\Include

-D__UVISION_VERSION="538" -DAT32F403AVGT7 -DAT32F403AVGT7 -DUSE_STDPERIPH_DRIVER -DAT_START_F403A_V1

-o .\objects\at32f403a_407_rtc.o --omf_browse .\objects\at32f403a_407_rtc.crf --depend .\objects\at32f403a_407_rtc.d)
I (..\inc\at32f403a_407_conf.h)(0x61CD8ABA)
I (..\..\..\libraries\drivers\inc\at32f403a_407_crm.h)(0x62039619)
I (..\..\..\libraries\cmsis\cm4\device_support\at32f403a_407.h)(0x6203960D)
I (..\..\..\libraries\cmsis\cm4\core_support\core_cm4.h)(0x620395CA)
I (D:\keil5\ARM\ARMCC\include\stdint.h)(0x5475F300)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_version.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_compiler.h)(0x622B0D2A)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_armcc.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\core_support\mpu_armv7.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\device_support\system_at32f403a_407.h)(0x62039612)
I (..\..\..\libraries\drivers\inc\at32f403a_407_def.h)(0x6203961D)
I (..\..\..\libraries\drivers\inc\at32f403a_407_tmr.h)(0x6203962C)
I (..\..\..\libraries\drivers\inc\at32f403a_407_rtc.h)(0x62039628)
I (..\..\..\libraries\drivers\inc\at32f403a_407_bpr.h)(0x62039616)
I (..\..\..\libraries\drivers\inc\at32f403a_407_gpio.h)(0x62039623)
I (..\..\..\libraries\drivers\inc\at32f403a_407_i2c.h)(0x62039625)
I (..\..\..\libraries\drivers\inc\at32f403a_407_usart.h)(0x6203962E)
I (..\..\..\libraries\drivers\inc\at32f403a_407_pwc.h)(0x62039627)
I (..\..\..\libraries\drivers\inc\at32f403a_407_can.h)(0x62039617)
I (..\..\..\libraries\drivers\inc\at32f403a_407_adc.h)(0x62039615)
I (..\..\..\libraries\drivers\inc\at32f403a_407_dac.h)(0x6203961A)
I (..\..\..\libraries\drivers\inc\at32f403a_407_spi.h)(0x6203962B)
I (..\..\..\libraries\drivers\inc\at32f403a_407_dma.h)(0x6203961E)
I (..\..\..\libraries\drivers\inc\at32f403a_407_debug.h)(0x6203961C)
I (..\..\..\libraries\drivers\inc\at32f403a_407_flash.h)(0x62039622)
I (..\..\..\libraries\drivers\inc\at32f403a_407_crc.h)(0x62039618)
I (..\..\..\libraries\drivers\inc\at32f403a_407_wwdt.h)(0x62039632)
I (..\..\..\libraries\drivers\inc\at32f403a_407_wdt.h)(0x62039630)
I (..\..\..\libraries\drivers\inc\at32f403a_407_exint.h)(0x62039621)
I (..\..\..\libraries\drivers\inc\at32f403a_407_sdio.h)(0x6203962A)
I (..\..\..\libraries\drivers\inc\at32f403a_407_xmc.h)(0x62039633)
I (..\..\..\libraries\drivers\inc\at32f403a_407_acc.h)(0x62039613)
I (..\..\..\libraries\drivers\inc\at32f403a_407_misc.h)(0x62039626)
I (..\..\..\libraries\drivers\inc\at32f403a_407_usb.h)(0x6203962F)
I (..\..\..\libraries\drivers\inc\at32f403a_407_emac.h)(0x6203961F)
F (..\..\..\libraries\drivers\src\at32f403a_407_sdio.c)(0x6203964B)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\inc -I ..\..\..\libraries\drivers\inc -I ..\..\..\project\at32f403a_407_board -I ..\..\..\libraries\cmsis\cm4\device_support -I ..\..\..\libraries\cmsis\cm4\core_support -I ..\..\..\middlewares\classb_lib\inc -I ..\mdk_v5

-ID:\keil5\ARM\Pack\ArteryTek\AT32F403A_407_DFP\2.1.6\Device\Include

-D__UVISION_VERSION="538" -DAT32F403AVGT7 -DAT32F403AVGT7 -DUSE_STDPERIPH_DRIVER -DAT_START_F403A_V1

-o .\objects\at32f403a_407_sdio.o --omf_browse .\objects\at32f403a_407_sdio.crf --depend .\objects\at32f403a_407_sdio.d)
I (..\inc\at32f403a_407_conf.h)(0x61CD8ABA)
I (..\..\..\libraries\drivers\inc\at32f403a_407_crm.h)(0x62039619)
I (..\..\..\libraries\cmsis\cm4\device_support\at32f403a_407.h)(0x6203960D)
I (..\..\..\libraries\cmsis\cm4\core_support\core_cm4.h)(0x620395CA)
I (D:\keil5\ARM\ARMCC\include\stdint.h)(0x5475F300)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_version.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_compiler.h)(0x622B0D2A)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_armcc.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\core_support\mpu_armv7.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\device_support\system_at32f403a_407.h)(0x62039612)
I (..\..\..\libraries\drivers\inc\at32f403a_407_def.h)(0x6203961D)
I (..\..\..\libraries\drivers\inc\at32f403a_407_tmr.h)(0x6203962C)
I (..\..\..\libraries\drivers\inc\at32f403a_407_rtc.h)(0x62039628)
I (..\..\..\libraries\drivers\inc\at32f403a_407_bpr.h)(0x62039616)
I (..\..\..\libraries\drivers\inc\at32f403a_407_gpio.h)(0x62039623)
I (..\..\..\libraries\drivers\inc\at32f403a_407_i2c.h)(0x62039625)
I (..\..\..\libraries\drivers\inc\at32f403a_407_usart.h)(0x6203962E)
I (..\..\..\libraries\drivers\inc\at32f403a_407_pwc.h)(0x62039627)
I (..\..\..\libraries\drivers\inc\at32f403a_407_can.h)(0x62039617)
I (..\..\..\libraries\drivers\inc\at32f403a_407_adc.h)(0x62039615)
I (..\..\..\libraries\drivers\inc\at32f403a_407_dac.h)(0x6203961A)
I (..\..\..\libraries\drivers\inc\at32f403a_407_spi.h)(0x6203962B)
I (..\..\..\libraries\drivers\inc\at32f403a_407_dma.h)(0x6203961E)
I (..\..\..\libraries\drivers\inc\at32f403a_407_debug.h)(0x6203961C)
I (..\..\..\libraries\drivers\inc\at32f403a_407_flash.h)(0x62039622)
I (..\..\..\libraries\drivers\inc\at32f403a_407_crc.h)(0x62039618)
I (..\..\..\libraries\drivers\inc\at32f403a_407_wwdt.h)(0x62039632)
I (..\..\..\libraries\drivers\inc\at32f403a_407_wdt.h)(0x62039630)
I (..\..\..\libraries\drivers\inc\at32f403a_407_exint.h)(0x62039621)
I (..\..\..\libraries\drivers\inc\at32f403a_407_sdio.h)(0x6203962A)
I (..\..\..\libraries\drivers\inc\at32f403a_407_xmc.h)(0x62039633)
I (..\..\..\libraries\drivers\inc\at32f403a_407_acc.h)(0x62039613)
I (..\..\..\libraries\drivers\inc\at32f403a_407_misc.h)(0x62039626)
I (..\..\..\libraries\drivers\inc\at32f403a_407_usb.h)(0x6203962F)
I (..\..\..\libraries\drivers\inc\at32f403a_407_emac.h)(0x6203961F)
F (..\..\..\libraries\drivers\src\at32f403a_407_spi.c)(0x6203964D)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\inc -I ..\..\..\libraries\drivers\inc -I ..\..\..\project\at32f403a_407_board -I ..\..\..\libraries\cmsis\cm4\device_support -I ..\..\..\libraries\cmsis\cm4\core_support -I ..\..\..\middlewares\classb_lib\inc -I ..\mdk_v5

-ID:\keil5\ARM\Pack\ArteryTek\AT32F403A_407_DFP\2.1.6\Device\Include

-D__UVISION_VERSION="538" -DAT32F403AVGT7 -DAT32F403AVGT7 -DUSE_STDPERIPH_DRIVER -DAT_START_F403A_V1

-o .\objects\at32f403a_407_spi.o --omf_browse .\objects\at32f403a_407_spi.crf --depend .\objects\at32f403a_407_spi.d)
I (..\inc\at32f403a_407_conf.h)(0x61CD8ABA)
I (..\..\..\libraries\drivers\inc\at32f403a_407_crm.h)(0x62039619)
I (..\..\..\libraries\cmsis\cm4\device_support\at32f403a_407.h)(0x6203960D)
I (..\..\..\libraries\cmsis\cm4\core_support\core_cm4.h)(0x620395CA)
I (D:\keil5\ARM\ARMCC\include\stdint.h)(0x5475F300)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_version.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_compiler.h)(0x622B0D2A)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_armcc.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\core_support\mpu_armv7.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\device_support\system_at32f403a_407.h)(0x62039612)
I (..\..\..\libraries\drivers\inc\at32f403a_407_def.h)(0x6203961D)
I (..\..\..\libraries\drivers\inc\at32f403a_407_tmr.h)(0x6203962C)
I (..\..\..\libraries\drivers\inc\at32f403a_407_rtc.h)(0x62039628)
I (..\..\..\libraries\drivers\inc\at32f403a_407_bpr.h)(0x62039616)
I (..\..\..\libraries\drivers\inc\at32f403a_407_gpio.h)(0x62039623)
I (..\..\..\libraries\drivers\inc\at32f403a_407_i2c.h)(0x62039625)
I (..\..\..\libraries\drivers\inc\at32f403a_407_usart.h)(0x6203962E)
I (..\..\..\libraries\drivers\inc\at32f403a_407_pwc.h)(0x62039627)
I (..\..\..\libraries\drivers\inc\at32f403a_407_can.h)(0x62039617)
I (..\..\..\libraries\drivers\inc\at32f403a_407_adc.h)(0x62039615)
I (..\..\..\libraries\drivers\inc\at32f403a_407_dac.h)(0x6203961A)
I (..\..\..\libraries\drivers\inc\at32f403a_407_spi.h)(0x6203962B)
I (..\..\..\libraries\drivers\inc\at32f403a_407_dma.h)(0x6203961E)
I (..\..\..\libraries\drivers\inc\at32f403a_407_debug.h)(0x6203961C)
I (..\..\..\libraries\drivers\inc\at32f403a_407_flash.h)(0x62039622)
I (..\..\..\libraries\drivers\inc\at32f403a_407_crc.h)(0x62039618)
I (..\..\..\libraries\drivers\inc\at32f403a_407_wwdt.h)(0x62039632)
I (..\..\..\libraries\drivers\inc\at32f403a_407_wdt.h)(0x62039630)
I (..\..\..\libraries\drivers\inc\at32f403a_407_exint.h)(0x62039621)
I (..\..\..\libraries\drivers\inc\at32f403a_407_sdio.h)(0x6203962A)
I (..\..\..\libraries\drivers\inc\at32f403a_407_xmc.h)(0x62039633)
I (..\..\..\libraries\drivers\inc\at32f403a_407_acc.h)(0x62039613)
I (..\..\..\libraries\drivers\inc\at32f403a_407_misc.h)(0x62039626)
I (..\..\..\libraries\drivers\inc\at32f403a_407_usb.h)(0x6203962F)
I (..\..\..\libraries\drivers\inc\at32f403a_407_emac.h)(0x6203961F)
F (..\..\..\libraries\drivers\src\at32f403a_407_tmr.c)(0x6203964E)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\inc -I ..\..\..\libraries\drivers\inc -I ..\..\..\project\at32f403a_407_board -I ..\..\..\libraries\cmsis\cm4\device_support -I ..\..\..\libraries\cmsis\cm4\core_support -I ..\..\..\middlewares\classb_lib\inc -I ..\mdk_v5

-ID:\keil5\ARM\Pack\ArteryTek\AT32F403A_407_DFP\2.1.6\Device\Include

-D__UVISION_VERSION="538" -DAT32F403AVGT7 -DAT32F403AVGT7 -DUSE_STDPERIPH_DRIVER -DAT_START_F403A_V1

-o .\objects\at32f403a_407_tmr.o --omf_browse .\objects\at32f403a_407_tmr.crf --depend .\objects\at32f403a_407_tmr.d)
I (..\inc\at32f403a_407_conf.h)(0x61CD8ABA)
I (..\..\..\libraries\drivers\inc\at32f403a_407_crm.h)(0x62039619)
I (..\..\..\libraries\cmsis\cm4\device_support\at32f403a_407.h)(0x6203960D)
I (..\..\..\libraries\cmsis\cm4\core_support\core_cm4.h)(0x620395CA)
I (D:\keil5\ARM\ARMCC\include\stdint.h)(0x5475F300)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_version.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_compiler.h)(0x622B0D2A)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_armcc.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\core_support\mpu_armv7.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\device_support\system_at32f403a_407.h)(0x62039612)
I (..\..\..\libraries\drivers\inc\at32f403a_407_def.h)(0x6203961D)
I (..\..\..\libraries\drivers\inc\at32f403a_407_tmr.h)(0x6203962C)
I (..\..\..\libraries\drivers\inc\at32f403a_407_rtc.h)(0x62039628)
I (..\..\..\libraries\drivers\inc\at32f403a_407_bpr.h)(0x62039616)
I (..\..\..\libraries\drivers\inc\at32f403a_407_gpio.h)(0x62039623)
I (..\..\..\libraries\drivers\inc\at32f403a_407_i2c.h)(0x62039625)
I (..\..\..\libraries\drivers\inc\at32f403a_407_usart.h)(0x6203962E)
I (..\..\..\libraries\drivers\inc\at32f403a_407_pwc.h)(0x62039627)
I (..\..\..\libraries\drivers\inc\at32f403a_407_can.h)(0x62039617)
I (..\..\..\libraries\drivers\inc\at32f403a_407_adc.h)(0x62039615)
I (..\..\..\libraries\drivers\inc\at32f403a_407_dac.h)(0x6203961A)
I (..\..\..\libraries\drivers\inc\at32f403a_407_spi.h)(0x6203962B)
I (..\..\..\libraries\drivers\inc\at32f403a_407_dma.h)(0x6203961E)
I (..\..\..\libraries\drivers\inc\at32f403a_407_debug.h)(0x6203961C)
I (..\..\..\libraries\drivers\inc\at32f403a_407_flash.h)(0x62039622)
I (..\..\..\libraries\drivers\inc\at32f403a_407_crc.h)(0x62039618)
I (..\..\..\libraries\drivers\inc\at32f403a_407_wwdt.h)(0x62039632)
I (..\..\..\libraries\drivers\inc\at32f403a_407_wdt.h)(0x62039630)
I (..\..\..\libraries\drivers\inc\at32f403a_407_exint.h)(0x62039621)
I (..\..\..\libraries\drivers\inc\at32f403a_407_sdio.h)(0x6203962A)
I (..\..\..\libraries\drivers\inc\at32f403a_407_xmc.h)(0x62039633)
I (..\..\..\libraries\drivers\inc\at32f403a_407_acc.h)(0x62039613)
I (..\..\..\libraries\drivers\inc\at32f403a_407_misc.h)(0x62039626)
I (..\..\..\libraries\drivers\inc\at32f403a_407_usb.h)(0x6203962F)
I (..\..\..\libraries\drivers\inc\at32f403a_407_emac.h)(0x6203961F)
F (..\..\..\libraries\drivers\src\at32f403a_407_usart.c)(0x6203964F)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\inc -I ..\..\..\libraries\drivers\inc -I ..\..\..\project\at32f403a_407_board -I ..\..\..\libraries\cmsis\cm4\device_support -I ..\..\..\libraries\cmsis\cm4\core_support -I ..\..\..\middlewares\classb_lib\inc -I ..\mdk_v5

-ID:\keil5\ARM\Pack\ArteryTek\AT32F403A_407_DFP\2.1.6\Device\Include

-D__UVISION_VERSION="538" -DAT32F403AVGT7 -DAT32F403AVGT7 -DUSE_STDPERIPH_DRIVER -DAT_START_F403A_V1

-o .\objects\at32f403a_407_usart.o --omf_browse .\objects\at32f403a_407_usart.crf --depend .\objects\at32f403a_407_usart.d)
I (..\inc\at32f403a_407_conf.h)(0x61CD8ABA)
I (..\..\..\libraries\drivers\inc\at32f403a_407_crm.h)(0x62039619)
I (..\..\..\libraries\cmsis\cm4\device_support\at32f403a_407.h)(0x6203960D)
I (..\..\..\libraries\cmsis\cm4\core_support\core_cm4.h)(0x620395CA)
I (D:\keil5\ARM\ARMCC\include\stdint.h)(0x5475F300)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_version.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_compiler.h)(0x622B0D2A)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_armcc.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\core_support\mpu_armv7.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\device_support\system_at32f403a_407.h)(0x62039612)
I (..\..\..\libraries\drivers\inc\at32f403a_407_def.h)(0x6203961D)
I (..\..\..\libraries\drivers\inc\at32f403a_407_tmr.h)(0x6203962C)
I (..\..\..\libraries\drivers\inc\at32f403a_407_rtc.h)(0x62039628)
I (..\..\..\libraries\drivers\inc\at32f403a_407_bpr.h)(0x62039616)
I (..\..\..\libraries\drivers\inc\at32f403a_407_gpio.h)(0x62039623)
I (..\..\..\libraries\drivers\inc\at32f403a_407_i2c.h)(0x62039625)
I (..\..\..\libraries\drivers\inc\at32f403a_407_usart.h)(0x6203962E)
I (..\..\..\libraries\drivers\inc\at32f403a_407_pwc.h)(0x62039627)
I (..\..\..\libraries\drivers\inc\at32f403a_407_can.h)(0x62039617)
I (..\..\..\libraries\drivers\inc\at32f403a_407_adc.h)(0x62039615)
I (..\..\..\libraries\drivers\inc\at32f403a_407_dac.h)(0x6203961A)
I (..\..\..\libraries\drivers\inc\at32f403a_407_spi.h)(0x6203962B)
I (..\..\..\libraries\drivers\inc\at32f403a_407_dma.h)(0x6203961E)
I (..\..\..\libraries\drivers\inc\at32f403a_407_debug.h)(0x6203961C)
I (..\..\..\libraries\drivers\inc\at32f403a_407_flash.h)(0x62039622)
I (..\..\..\libraries\drivers\inc\at32f403a_407_crc.h)(0x62039618)
I (..\..\..\libraries\drivers\inc\at32f403a_407_wwdt.h)(0x62039632)
I (..\..\..\libraries\drivers\inc\at32f403a_407_wdt.h)(0x62039630)
I (..\..\..\libraries\drivers\inc\at32f403a_407_exint.h)(0x62039621)
I (..\..\..\libraries\drivers\inc\at32f403a_407_sdio.h)(0x6203962A)
I (..\..\..\libraries\drivers\inc\at32f403a_407_xmc.h)(0x62039633)
I (..\..\..\libraries\drivers\inc\at32f403a_407_acc.h)(0x62039613)
I (..\..\..\libraries\drivers\inc\at32f403a_407_misc.h)(0x62039626)
I (..\..\..\libraries\drivers\inc\at32f403a_407_usb.h)(0x6203962F)
I (..\..\..\libraries\drivers\inc\at32f403a_407_emac.h)(0x6203961F)
F (..\..\..\libraries\drivers\src\at32f403a_407_usb.c)(0x62039651)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\inc -I ..\..\..\libraries\drivers\inc -I ..\..\..\project\at32f403a_407_board -I ..\..\..\libraries\cmsis\cm4\device_support -I ..\..\..\libraries\cmsis\cm4\core_support -I ..\..\..\middlewares\classb_lib\inc -I ..\mdk_v5

-ID:\keil5\ARM\Pack\ArteryTek\AT32F403A_407_DFP\2.1.6\Device\Include

-D__UVISION_VERSION="538" -DAT32F403AVGT7 -DAT32F403AVGT7 -DUSE_STDPERIPH_DRIVER -DAT_START_F403A_V1

-o .\objects\at32f403a_407_usb.o --omf_browse .\objects\at32f403a_407_usb.crf --depend .\objects\at32f403a_407_usb.d)
I (..\inc\at32f403a_407_conf.h)(0x61CD8ABA)
I (..\..\..\libraries\drivers\inc\at32f403a_407_crm.h)(0x62039619)
I (..\..\..\libraries\cmsis\cm4\device_support\at32f403a_407.h)(0x6203960D)
I (..\..\..\libraries\cmsis\cm4\core_support\core_cm4.h)(0x620395CA)
I (D:\keil5\ARM\ARMCC\include\stdint.h)(0x5475F300)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_version.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_compiler.h)(0x622B0D2A)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_armcc.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\core_support\mpu_armv7.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\device_support\system_at32f403a_407.h)(0x62039612)
I (..\..\..\libraries\drivers\inc\at32f403a_407_def.h)(0x6203961D)
I (..\..\..\libraries\drivers\inc\at32f403a_407_tmr.h)(0x6203962C)
I (..\..\..\libraries\drivers\inc\at32f403a_407_rtc.h)(0x62039628)
I (..\..\..\libraries\drivers\inc\at32f403a_407_bpr.h)(0x62039616)
I (..\..\..\libraries\drivers\inc\at32f403a_407_gpio.h)(0x62039623)
I (..\..\..\libraries\drivers\inc\at32f403a_407_i2c.h)(0x62039625)
I (..\..\..\libraries\drivers\inc\at32f403a_407_usart.h)(0x6203962E)
I (..\..\..\libraries\drivers\inc\at32f403a_407_pwc.h)(0x62039627)
I (..\..\..\libraries\drivers\inc\at32f403a_407_can.h)(0x62039617)
I (..\..\..\libraries\drivers\inc\at32f403a_407_adc.h)(0x62039615)
I (..\..\..\libraries\drivers\inc\at32f403a_407_dac.h)(0x6203961A)
I (..\..\..\libraries\drivers\inc\at32f403a_407_spi.h)(0x6203962B)
I (..\..\..\libraries\drivers\inc\at32f403a_407_dma.h)(0x6203961E)
I (..\..\..\libraries\drivers\inc\at32f403a_407_debug.h)(0x6203961C)
I (..\..\..\libraries\drivers\inc\at32f403a_407_flash.h)(0x62039622)
I (..\..\..\libraries\drivers\inc\at32f403a_407_crc.h)(0x62039618)
I (..\..\..\libraries\drivers\inc\at32f403a_407_wwdt.h)(0x62039632)
I (..\..\..\libraries\drivers\inc\at32f403a_407_wdt.h)(0x62039630)
I (..\..\..\libraries\drivers\inc\at32f403a_407_exint.h)(0x62039621)
I (..\..\..\libraries\drivers\inc\at32f403a_407_sdio.h)(0x6203962A)
I (..\..\..\libraries\drivers\inc\at32f403a_407_xmc.h)(0x62039633)
I (..\..\..\libraries\drivers\inc\at32f403a_407_acc.h)(0x62039613)
I (..\..\..\libraries\drivers\inc\at32f403a_407_misc.h)(0x62039626)
I (..\..\..\libraries\drivers\inc\at32f403a_407_usb.h)(0x6203962F)
I (..\..\..\libraries\drivers\inc\at32f403a_407_emac.h)(0x6203961F)
F (..\..\..\libraries\drivers\src\at32f403a_407_wdt.c)(0x62039652)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\inc -I ..\..\..\libraries\drivers\inc -I ..\..\..\project\at32f403a_407_board -I ..\..\..\libraries\cmsis\cm4\device_support -I ..\..\..\libraries\cmsis\cm4\core_support -I ..\..\..\middlewares\classb_lib\inc -I ..\mdk_v5

-ID:\keil5\ARM\Pack\ArteryTek\AT32F403A_407_DFP\2.1.6\Device\Include

-D__UVISION_VERSION="538" -DAT32F403AVGT7 -DAT32F403AVGT7 -DUSE_STDPERIPH_DRIVER -DAT_START_F403A_V1

-o .\objects\at32f403a_407_wdt.o --omf_browse .\objects\at32f403a_407_wdt.crf --depend .\objects\at32f403a_407_wdt.d)
I (..\inc\at32f403a_407_conf.h)(0x61CD8ABA)
I (..\..\..\libraries\drivers\inc\at32f403a_407_crm.h)(0x62039619)
I (..\..\..\libraries\cmsis\cm4\device_support\at32f403a_407.h)(0x6203960D)
I (..\..\..\libraries\cmsis\cm4\core_support\core_cm4.h)(0x620395CA)
I (D:\keil5\ARM\ARMCC\include\stdint.h)(0x5475F300)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_version.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_compiler.h)(0x622B0D2A)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_armcc.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\core_support\mpu_armv7.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\device_support\system_at32f403a_407.h)(0x62039612)
I (..\..\..\libraries\drivers\inc\at32f403a_407_def.h)(0x6203961D)
I (..\..\..\libraries\drivers\inc\at32f403a_407_tmr.h)(0x6203962C)
I (..\..\..\libraries\drivers\inc\at32f403a_407_rtc.h)(0x62039628)
I (..\..\..\libraries\drivers\inc\at32f403a_407_bpr.h)(0x62039616)
I (..\..\..\libraries\drivers\inc\at32f403a_407_gpio.h)(0x62039623)
I (..\..\..\libraries\drivers\inc\at32f403a_407_i2c.h)(0x62039625)
I (..\..\..\libraries\drivers\inc\at32f403a_407_usart.h)(0x6203962E)
I (..\..\..\libraries\drivers\inc\at32f403a_407_pwc.h)(0x62039627)
I (..\..\..\libraries\drivers\inc\at32f403a_407_can.h)(0x62039617)
I (..\..\..\libraries\drivers\inc\at32f403a_407_adc.h)(0x62039615)
I (..\..\..\libraries\drivers\inc\at32f403a_407_dac.h)(0x6203961A)
I (..\..\..\libraries\drivers\inc\at32f403a_407_spi.h)(0x6203962B)
I (..\..\..\libraries\drivers\inc\at32f403a_407_dma.h)(0x6203961E)
I (..\..\..\libraries\drivers\inc\at32f403a_407_debug.h)(0x6203961C)
I (..\..\..\libraries\drivers\inc\at32f403a_407_flash.h)(0x62039622)
I (..\..\..\libraries\drivers\inc\at32f403a_407_crc.h)(0x62039618)
I (..\..\..\libraries\drivers\inc\at32f403a_407_wwdt.h)(0x62039632)
I (..\..\..\libraries\drivers\inc\at32f403a_407_wdt.h)(0x62039630)
I (..\..\..\libraries\drivers\inc\at32f403a_407_exint.h)(0x62039621)
I (..\..\..\libraries\drivers\inc\at32f403a_407_sdio.h)(0x6203962A)
I (..\..\..\libraries\drivers\inc\at32f403a_407_xmc.h)(0x62039633)
I (..\..\..\libraries\drivers\inc\at32f403a_407_acc.h)(0x62039613)
I (..\..\..\libraries\drivers\inc\at32f403a_407_misc.h)(0x62039626)
I (..\..\..\libraries\drivers\inc\at32f403a_407_usb.h)(0x6203962F)
I (..\..\..\libraries\drivers\inc\at32f403a_407_emac.h)(0x6203961F)
F (..\..\..\libraries\drivers\src\at32f403a_407_wwdt.c)(0x62039654)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\inc -I ..\..\..\libraries\drivers\inc -I ..\..\..\project\at32f403a_407_board -I ..\..\..\libraries\cmsis\cm4\device_support -I ..\..\..\libraries\cmsis\cm4\core_support -I ..\..\..\middlewares\classb_lib\inc -I ..\mdk_v5

-ID:\keil5\ARM\Pack\ArteryTek\AT32F403A_407_DFP\2.1.6\Device\Include

-D__UVISION_VERSION="538" -DAT32F403AVGT7 -DAT32F403AVGT7 -DUSE_STDPERIPH_DRIVER -DAT_START_F403A_V1

-o .\objects\at32f403a_407_wwdt.o --omf_browse .\objects\at32f403a_407_wwdt.crf --depend .\objects\at32f403a_407_wwdt.d)
I (..\inc\at32f403a_407_conf.h)(0x61CD8ABA)
I (..\..\..\libraries\drivers\inc\at32f403a_407_crm.h)(0x62039619)
I (..\..\..\libraries\cmsis\cm4\device_support\at32f403a_407.h)(0x6203960D)
I (..\..\..\libraries\cmsis\cm4\core_support\core_cm4.h)(0x620395CA)
I (D:\keil5\ARM\ARMCC\include\stdint.h)(0x5475F300)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_version.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_compiler.h)(0x622B0D2A)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_armcc.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\core_support\mpu_armv7.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\device_support\system_at32f403a_407.h)(0x62039612)
I (..\..\..\libraries\drivers\inc\at32f403a_407_def.h)(0x6203961D)
I (..\..\..\libraries\drivers\inc\at32f403a_407_tmr.h)(0x6203962C)
I (..\..\..\libraries\drivers\inc\at32f403a_407_rtc.h)(0x62039628)
I (..\..\..\libraries\drivers\inc\at32f403a_407_bpr.h)(0x62039616)
I (..\..\..\libraries\drivers\inc\at32f403a_407_gpio.h)(0x62039623)
I (..\..\..\libraries\drivers\inc\at32f403a_407_i2c.h)(0x62039625)
I (..\..\..\libraries\drivers\inc\at32f403a_407_usart.h)(0x6203962E)
I (..\..\..\libraries\drivers\inc\at32f403a_407_pwc.h)(0x62039627)
I (..\..\..\libraries\drivers\inc\at32f403a_407_can.h)(0x62039617)
I (..\..\..\libraries\drivers\inc\at32f403a_407_adc.h)(0x62039615)
I (..\..\..\libraries\drivers\inc\at32f403a_407_dac.h)(0x6203961A)
I (..\..\..\libraries\drivers\inc\at32f403a_407_spi.h)(0x6203962B)
I (..\..\..\libraries\drivers\inc\at32f403a_407_dma.h)(0x6203961E)
I (..\..\..\libraries\drivers\inc\at32f403a_407_debug.h)(0x6203961C)
I (..\..\..\libraries\drivers\inc\at32f403a_407_flash.h)(0x62039622)
I (..\..\..\libraries\drivers\inc\at32f403a_407_crc.h)(0x62039618)
I (..\..\..\libraries\drivers\inc\at32f403a_407_wwdt.h)(0x62039632)
I (..\..\..\libraries\drivers\inc\at32f403a_407_wdt.h)(0x62039630)
I (..\..\..\libraries\drivers\inc\at32f403a_407_exint.h)(0x62039621)
I (..\..\..\libraries\drivers\inc\at32f403a_407_sdio.h)(0x6203962A)
I (..\..\..\libraries\drivers\inc\at32f403a_407_xmc.h)(0x62039633)
I (..\..\..\libraries\drivers\inc\at32f403a_407_acc.h)(0x62039613)
I (..\..\..\libraries\drivers\inc\at32f403a_407_misc.h)(0x62039626)
I (..\..\..\libraries\drivers\inc\at32f403a_407_usb.h)(0x6203962F)
I (..\..\..\libraries\drivers\inc\at32f403a_407_emac.h)(0x6203961F)
F (..\..\..\libraries\drivers\src\at32f403a_407_xmc.c)(0x62039655)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\inc -I ..\..\..\libraries\drivers\inc -I ..\..\..\project\at32f403a_407_board -I ..\..\..\libraries\cmsis\cm4\device_support -I ..\..\..\libraries\cmsis\cm4\core_support -I ..\..\..\middlewares\classb_lib\inc -I ..\mdk_v5

-ID:\keil5\ARM\Pack\ArteryTek\AT32F403A_407_DFP\2.1.6\Device\Include

-D__UVISION_VERSION="538" -DAT32F403AVGT7 -DAT32F403AVGT7 -DUSE_STDPERIPH_DRIVER -DAT_START_F403A_V1

-o .\objects\at32f403a_407_xmc.o --omf_browse .\objects\at32f403a_407_xmc.crf --depend .\objects\at32f403a_407_xmc.d)
I (..\inc\at32f403a_407_conf.h)(0x61CD8ABA)
I (..\..\..\libraries\drivers\inc\at32f403a_407_crm.h)(0x62039619)
I (..\..\..\libraries\cmsis\cm4\device_support\at32f403a_407.h)(0x6203960D)
I (..\..\..\libraries\cmsis\cm4\core_support\core_cm4.h)(0x620395CA)
I (D:\keil5\ARM\ARMCC\include\stdint.h)(0x5475F300)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_version.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_compiler.h)(0x622B0D2A)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_armcc.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\core_support\mpu_armv7.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\device_support\system_at32f403a_407.h)(0x62039612)
I (..\..\..\libraries\drivers\inc\at32f403a_407_def.h)(0x6203961D)
I (..\..\..\libraries\drivers\inc\at32f403a_407_tmr.h)(0x6203962C)
I (..\..\..\libraries\drivers\inc\at32f403a_407_rtc.h)(0x62039628)
I (..\..\..\libraries\drivers\inc\at32f403a_407_bpr.h)(0x62039616)
I (..\..\..\libraries\drivers\inc\at32f403a_407_gpio.h)(0x62039623)
I (..\..\..\libraries\drivers\inc\at32f403a_407_i2c.h)(0x62039625)
I (..\..\..\libraries\drivers\inc\at32f403a_407_usart.h)(0x6203962E)
I (..\..\..\libraries\drivers\inc\at32f403a_407_pwc.h)(0x62039627)
I (..\..\..\libraries\drivers\inc\at32f403a_407_can.h)(0x62039617)
I (..\..\..\libraries\drivers\inc\at32f403a_407_adc.h)(0x62039615)
I (..\..\..\libraries\drivers\inc\at32f403a_407_dac.h)(0x6203961A)
I (..\..\..\libraries\drivers\inc\at32f403a_407_spi.h)(0x6203962B)
I (..\..\..\libraries\drivers\inc\at32f403a_407_dma.h)(0x6203961E)
I (..\..\..\libraries\drivers\inc\at32f403a_407_debug.h)(0x6203961C)
I (..\..\..\libraries\drivers\inc\at32f403a_407_flash.h)(0x62039622)
I (..\..\..\libraries\drivers\inc\at32f403a_407_crc.h)(0x62039618)
I (..\..\..\libraries\drivers\inc\at32f403a_407_wwdt.h)(0x62039632)
I (..\..\..\libraries\drivers\inc\at32f403a_407_wdt.h)(0x62039630)
I (..\..\..\libraries\drivers\inc\at32f403a_407_exint.h)(0x62039621)
I (..\..\..\libraries\drivers\inc\at32f403a_407_sdio.h)(0x6203962A)
I (..\..\..\libraries\drivers\inc\at32f403a_407_xmc.h)(0x62039633)
I (..\..\..\libraries\drivers\inc\at32f403a_407_acc.h)(0x62039613)
I (..\..\..\libraries\drivers\inc\at32f403a_407_misc.h)(0x62039626)
I (..\..\..\libraries\drivers\inc\at32f403a_407_usb.h)(0x6203962F)
I (..\..\..\libraries\drivers\inc\at32f403a_407_emac.h)(0x6203961F)
F (..\..\..\middlewares\classb_lib\src\at32_selftest_startup.c)(0x62580A83)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\inc -I ..\..\..\libraries\drivers\inc -I ..\..\..\project\at32f403a_407_board -I ..\..\..\libraries\cmsis\cm4\device_support -I ..\..\..\libraries\cmsis\cm4\core_support -I ..\..\..\middlewares\classb_lib\inc -I ..\mdk_v5

-ID:\keil5\ARM\Pack\ArteryTek\AT32F403A_407_DFP\2.1.6\Device\Include

-D__UVISION_VERSION="538" -DAT32F403AVGT7 -DAT32F403AVGT7 -DUSE_STDPERIPH_DRIVER -DAT_START_F403A_V1

-o .\objects\at32_selftest_startup.o --omf_browse .\objects\at32_selftest_startup.crf --depend .\objects\at32_selftest_startup.d)
I (..\inc\main.h)(0x62329F1B)
I (..\..\..\project\at32f403a_407_board\at32f403a_407_board.h)(0x620396A5)
I (D:\keil5\ARM\ARMCC\include\stdio.h)(0x5475F300)
I (..\..\..\libraries\cmsis\cm4\device_support\at32f403a_407.h)(0x6203960D)
I (..\..\..\libraries\cmsis\cm4\core_support\core_cm4.h)(0x620395CA)
I (D:\keil5\ARM\ARMCC\include\stdint.h)(0x5475F300)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_version.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_compiler.h)(0x622B0D2A)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_armcc.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\core_support\mpu_armv7.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\device_support\system_at32f403a_407.h)(0x62039612)
I (..\..\..\libraries\drivers\inc\at32f403a_407_def.h)(0x6203961D)
I (..\inc\at32f403a_407_conf.h)(0x61CD8ABA)
I (..\..\..\libraries\drivers\inc\at32f403a_407_crm.h)(0x62039619)
I (..\..\..\libraries\drivers\inc\at32f403a_407_tmr.h)(0x6203962C)
I (..\..\..\libraries\drivers\inc\at32f403a_407_rtc.h)(0x62039628)
I (..\..\..\libraries\drivers\inc\at32f403a_407_bpr.h)(0x62039616)
I (..\..\..\libraries\drivers\inc\at32f403a_407_gpio.h)(0x62039623)
I (..\..\..\libraries\drivers\inc\at32f403a_407_i2c.h)(0x62039625)
I (..\..\..\libraries\drivers\inc\at32f403a_407_usart.h)(0x6203962E)
I (..\..\..\libraries\drivers\inc\at32f403a_407_pwc.h)(0x62039627)
I (..\..\..\libraries\drivers\inc\at32f403a_407_can.h)(0x62039617)
I (..\..\..\libraries\drivers\inc\at32f403a_407_adc.h)(0x62039615)
I (..\..\..\libraries\drivers\inc\at32f403a_407_dac.h)(0x6203961A)
I (..\..\..\libraries\drivers\inc\at32f403a_407_spi.h)(0x6203962B)
I (..\..\..\libraries\drivers\inc\at32f403a_407_dma.h)(0x6203961E)
I (..\..\..\libraries\drivers\inc\at32f403a_407_debug.h)(0x6203961C)
I (..\..\..\libraries\drivers\inc\at32f403a_407_flash.h)(0x62039622)
I (..\..\..\libraries\drivers\inc\at32f403a_407_crc.h)(0x62039618)
I (..\..\..\libraries\drivers\inc\at32f403a_407_wwdt.h)(0x62039632)
I (..\..\..\libraries\drivers\inc\at32f403a_407_wdt.h)(0x62039630)
I (..\..\..\libraries\drivers\inc\at32f403a_407_exint.h)(0x62039621)
I (..\..\..\libraries\drivers\inc\at32f403a_407_sdio.h)(0x6203962A)
I (..\..\..\libraries\drivers\inc\at32f403a_407_xmc.h)(0x62039633)
I (..\..\..\libraries\drivers\inc\at32f403a_407_acc.h)(0x62039613)
I (..\..\..\libraries\drivers\inc\at32f403a_407_misc.h)(0x62039626)
I (..\..\..\libraries\drivers\inc\at32f403a_407_usb.h)(0x6203962F)
I (..\..\..\libraries\drivers\inc\at32f403a_407_emac.h)(0x6203961F)
I (..\inc\at32_selftest_param.h)(0x624D0D9A)
I (..\..\..\middlewares\classb_lib\inc\at32_selftest_lib.h)(0x623AD2D2)
I (..\..\..\middlewares\classb_lib\inc\at32_selftest_classbvar.h)(0x623AD2D2)
I (..\..\..\middlewares\classb_lib\inc\at32_selftest_startup.h)(0x6225F4AB)
I (..\..\..\middlewares\classb_lib\inc\at32_selftest_runtime.h)(0x6225F3FE)
I (..\..\..\middlewares\classb_lib\inc\at32_selftest_cpu.h)(0x6225B254)
I (..\..\..\middlewares\classb_lib\inc\at32_selftest_clock.h)(0x6225F0E9)
I (..\..\..\middlewares\classb_lib\inc\at32_selftest_crc.h)(0x6225C13C)
I (..\..\..\middlewares\classb_lib\inc\at32_selftest_ram.h)(0x6225F3FE)
I (..\mdk_v5\crc32.h)(0x622B1FD5)
F (..\..\..\middlewares\classb_lib\src\at32_selftest_runtime.c)(0x623ADBB8)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\inc -I ..\..\..\libraries\drivers\inc -I ..\..\..\project\at32f403a_407_board -I ..\..\..\libraries\cmsis\cm4\device_support -I ..\..\..\libraries\cmsis\cm4\core_support -I ..\..\..\middlewares\classb_lib\inc -I ..\mdk_v5

-ID:\keil5\ARM\Pack\ArteryTek\AT32F403A_407_DFP\2.1.6\Device\Include

-D__UVISION_VERSION="538" -DAT32F403AVGT7 -DAT32F403AVGT7 -DUSE_STDPERIPH_DRIVER -DAT_START_F403A_V1

-o .\objects\at32_selftest_runtime.o --omf_browse .\objects\at32_selftest_runtime.crf --depend .\objects\at32_selftest_runtime.d)
I (..\inc\main.h)(0x62329F1B)
I (..\..\..\project\at32f403a_407_board\at32f403a_407_board.h)(0x620396A5)
I (D:\keil5\ARM\ARMCC\include\stdio.h)(0x5475F300)
I (..\..\..\libraries\cmsis\cm4\device_support\at32f403a_407.h)(0x6203960D)
I (..\..\..\libraries\cmsis\cm4\core_support\core_cm4.h)(0x620395CA)
I (D:\keil5\ARM\ARMCC\include\stdint.h)(0x5475F300)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_version.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_compiler.h)(0x622B0D2A)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_armcc.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\core_support\mpu_armv7.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\device_support\system_at32f403a_407.h)(0x62039612)
I (..\..\..\libraries\drivers\inc\at32f403a_407_def.h)(0x6203961D)
I (..\inc\at32f403a_407_conf.h)(0x61CD8ABA)
I (..\..\..\libraries\drivers\inc\at32f403a_407_crm.h)(0x62039619)
I (..\..\..\libraries\drivers\inc\at32f403a_407_tmr.h)(0x6203962C)
I (..\..\..\libraries\drivers\inc\at32f403a_407_rtc.h)(0x62039628)
I (..\..\..\libraries\drivers\inc\at32f403a_407_bpr.h)(0x62039616)
I (..\..\..\libraries\drivers\inc\at32f403a_407_gpio.h)(0x62039623)
I (..\..\..\libraries\drivers\inc\at32f403a_407_i2c.h)(0x62039625)
I (..\..\..\libraries\drivers\inc\at32f403a_407_usart.h)(0x6203962E)
I (..\..\..\libraries\drivers\inc\at32f403a_407_pwc.h)(0x62039627)
I (..\..\..\libraries\drivers\inc\at32f403a_407_can.h)(0x62039617)
I (..\..\..\libraries\drivers\inc\at32f403a_407_adc.h)(0x62039615)
I (..\..\..\libraries\drivers\inc\at32f403a_407_dac.h)(0x6203961A)
I (..\..\..\libraries\drivers\inc\at32f403a_407_spi.h)(0x6203962B)
I (..\..\..\libraries\drivers\inc\at32f403a_407_dma.h)(0x6203961E)
I (..\..\..\libraries\drivers\inc\at32f403a_407_debug.h)(0x6203961C)
I (..\..\..\libraries\drivers\inc\at32f403a_407_flash.h)(0x62039622)
I (..\..\..\libraries\drivers\inc\at32f403a_407_crc.h)(0x62039618)
I (..\..\..\libraries\drivers\inc\at32f403a_407_wwdt.h)(0x62039632)
I (..\..\..\libraries\drivers\inc\at32f403a_407_wdt.h)(0x62039630)
I (..\..\..\libraries\drivers\inc\at32f403a_407_exint.h)(0x62039621)
I (..\..\..\libraries\drivers\inc\at32f403a_407_sdio.h)(0x6203962A)
I (..\..\..\libraries\drivers\inc\at32f403a_407_xmc.h)(0x62039633)
I (..\..\..\libraries\drivers\inc\at32f403a_407_acc.h)(0x62039613)
I (..\..\..\libraries\drivers\inc\at32f403a_407_misc.h)(0x62039626)
I (..\..\..\libraries\drivers\inc\at32f403a_407_usb.h)(0x6203962F)
I (..\..\..\libraries\drivers\inc\at32f403a_407_emac.h)(0x6203961F)
I (..\inc\at32_selftest_param.h)(0x624D0D9A)
I (..\..\..\middlewares\classb_lib\inc\at32_selftest_lib.h)(0x623AD2D2)
I (..\..\..\middlewares\classb_lib\inc\at32_selftest_classbvar.h)(0x623AD2D2)
I (..\..\..\middlewares\classb_lib\inc\at32_selftest_startup.h)(0x6225F4AB)
I (..\..\..\middlewares\classb_lib\inc\at32_selftest_runtime.h)(0x6225F3FE)
I (..\..\..\middlewares\classb_lib\inc\at32_selftest_cpu.h)(0x6225B254)
I (..\..\..\middlewares\classb_lib\inc\at32_selftest_clock.h)(0x6225F0E9)
I (..\..\..\middlewares\classb_lib\inc\at32_selftest_crc.h)(0x6225C13C)
I (..\..\..\middlewares\classb_lib\inc\at32_selftest_ram.h)(0x6225F3FE)
F (..\..\..\middlewares\classb_lib\src\at32_selftest_crc.c)(0x622F0A1B)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\inc -I ..\..\..\libraries\drivers\inc -I ..\..\..\project\at32f403a_407_board -I ..\..\..\libraries\cmsis\cm4\device_support -I ..\..\..\libraries\cmsis\cm4\core_support -I ..\..\..\middlewares\classb_lib\inc -I ..\mdk_v5

-ID:\keil5\ARM\Pack\ArteryTek\AT32F403A_407_DFP\2.1.6\Device\Include

-D__UVISION_VERSION="538" -DAT32F403AVGT7 -DAT32F403AVGT7 -DUSE_STDPERIPH_DRIVER -DAT_START_F403A_V1

-o .\objects\at32_selftest_crc.o --omf_browse .\objects\at32_selftest_crc.crf --depend .\objects\at32_selftest_crc.d)
I (..\inc\main.h)(0x62329F1B)
I (..\..\..\project\at32f403a_407_board\at32f403a_407_board.h)(0x620396A5)
I (D:\keil5\ARM\ARMCC\include\stdio.h)(0x5475F300)
I (..\..\..\libraries\cmsis\cm4\device_support\at32f403a_407.h)(0x6203960D)
I (..\..\..\libraries\cmsis\cm4\core_support\core_cm4.h)(0x620395CA)
I (D:\keil5\ARM\ARMCC\include\stdint.h)(0x5475F300)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_version.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_compiler.h)(0x622B0D2A)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_armcc.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\core_support\mpu_armv7.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\device_support\system_at32f403a_407.h)(0x62039612)
I (..\..\..\libraries\drivers\inc\at32f403a_407_def.h)(0x6203961D)
I (..\inc\at32f403a_407_conf.h)(0x61CD8ABA)
I (..\..\..\libraries\drivers\inc\at32f403a_407_crm.h)(0x62039619)
I (..\..\..\libraries\drivers\inc\at32f403a_407_tmr.h)(0x6203962C)
I (..\..\..\libraries\drivers\inc\at32f403a_407_rtc.h)(0x62039628)
I (..\..\..\libraries\drivers\inc\at32f403a_407_bpr.h)(0x62039616)
I (..\..\..\libraries\drivers\inc\at32f403a_407_gpio.h)(0x62039623)
I (..\..\..\libraries\drivers\inc\at32f403a_407_i2c.h)(0x62039625)
I (..\..\..\libraries\drivers\inc\at32f403a_407_usart.h)(0x6203962E)
I (..\..\..\libraries\drivers\inc\at32f403a_407_pwc.h)(0x62039627)
I (..\..\..\libraries\drivers\inc\at32f403a_407_can.h)(0x62039617)
I (..\..\..\libraries\drivers\inc\at32f403a_407_adc.h)(0x62039615)
I (..\..\..\libraries\drivers\inc\at32f403a_407_dac.h)(0x6203961A)
I (..\..\..\libraries\drivers\inc\at32f403a_407_spi.h)(0x6203962B)
I (..\..\..\libraries\drivers\inc\at32f403a_407_dma.h)(0x6203961E)
I (..\..\..\libraries\drivers\inc\at32f403a_407_debug.h)(0x6203961C)
I (..\..\..\libraries\drivers\inc\at32f403a_407_flash.h)(0x62039622)
I (..\..\..\libraries\drivers\inc\at32f403a_407_crc.h)(0x62039618)
I (..\..\..\libraries\drivers\inc\at32f403a_407_wwdt.h)(0x62039632)
I (..\..\..\libraries\drivers\inc\at32f403a_407_wdt.h)(0x62039630)
I (..\..\..\libraries\drivers\inc\at32f403a_407_exint.h)(0x62039621)
I (..\..\..\libraries\drivers\inc\at32f403a_407_sdio.h)(0x6203962A)
I (..\..\..\libraries\drivers\inc\at32f403a_407_xmc.h)(0x62039633)
I (..\..\..\libraries\drivers\inc\at32f403a_407_acc.h)(0x62039613)
I (..\..\..\libraries\drivers\inc\at32f403a_407_misc.h)(0x62039626)
I (..\..\..\libraries\drivers\inc\at32f403a_407_usb.h)(0x6203962F)
I (..\..\..\libraries\drivers\inc\at32f403a_407_emac.h)(0x6203961F)
I (..\inc\at32_selftest_param.h)(0x624D0D9A)
I (..\..\..\middlewares\classb_lib\inc\at32_selftest_lib.h)(0x623AD2D2)
I (..\..\..\middlewares\classb_lib\inc\at32_selftest_classbvar.h)(0x623AD2D2)
I (..\..\..\middlewares\classb_lib\inc\at32_selftest_startup.h)(0x6225F4AB)
I (..\..\..\middlewares\classb_lib\inc\at32_selftest_runtime.h)(0x6225F3FE)
I (..\..\..\middlewares\classb_lib\inc\at32_selftest_cpu.h)(0x6225B254)
I (..\..\..\middlewares\classb_lib\inc\at32_selftest_clock.h)(0x6225F0E9)
I (..\..\..\middlewares\classb_lib\inc\at32_selftest_crc.h)(0x6225C13C)
I (..\..\..\middlewares\classb_lib\inc\at32_selftest_ram.h)(0x6225F3FE)
I (..\mdk_v5\crc32.h)(0x622B1FD5)
F (..\..\..\middlewares\classb_lib\src\at32_selftest_ram.c)(0x623AB344)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\inc -I ..\..\..\libraries\drivers\inc -I ..\..\..\project\at32f403a_407_board -I ..\..\..\libraries\cmsis\cm4\device_support -I ..\..\..\libraries\cmsis\cm4\core_support -I ..\..\..\middlewares\classb_lib\inc -I ..\mdk_v5

-ID:\keil5\ARM\Pack\ArteryTek\AT32F403A_407_DFP\2.1.6\Device\Include

-D__UVISION_VERSION="538" -DAT32F403AVGT7 -DAT32F403AVGT7 -DUSE_STDPERIPH_DRIVER -DAT_START_F403A_V1

-o .\objects\at32_selftest_ram.o --omf_browse .\objects\at32_selftest_ram.crf --depend .\objects\at32_selftest_ram.d)
I (..\inc\main.h)(0x62329F1B)
I (..\..\..\project\at32f403a_407_board\at32f403a_407_board.h)(0x620396A5)
I (D:\keil5\ARM\ARMCC\include\stdio.h)(0x5475F300)
I (..\..\..\libraries\cmsis\cm4\device_support\at32f403a_407.h)(0x6203960D)
I (..\..\..\libraries\cmsis\cm4\core_support\core_cm4.h)(0x620395CA)
I (D:\keil5\ARM\ARMCC\include\stdint.h)(0x5475F300)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_version.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_compiler.h)(0x622B0D2A)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_armcc.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\core_support\mpu_armv7.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\device_support\system_at32f403a_407.h)(0x62039612)
I (..\..\..\libraries\drivers\inc\at32f403a_407_def.h)(0x6203961D)
I (..\inc\at32f403a_407_conf.h)(0x61CD8ABA)
I (..\..\..\libraries\drivers\inc\at32f403a_407_crm.h)(0x62039619)
I (..\..\..\libraries\drivers\inc\at32f403a_407_tmr.h)(0x6203962C)
I (..\..\..\libraries\drivers\inc\at32f403a_407_rtc.h)(0x62039628)
I (..\..\..\libraries\drivers\inc\at32f403a_407_bpr.h)(0x62039616)
I (..\..\..\libraries\drivers\inc\at32f403a_407_gpio.h)(0x62039623)
I (..\..\..\libraries\drivers\inc\at32f403a_407_i2c.h)(0x62039625)
I (..\..\..\libraries\drivers\inc\at32f403a_407_usart.h)(0x6203962E)
I (..\..\..\libraries\drivers\inc\at32f403a_407_pwc.h)(0x62039627)
I (..\..\..\libraries\drivers\inc\at32f403a_407_can.h)(0x62039617)
I (..\..\..\libraries\drivers\inc\at32f403a_407_adc.h)(0x62039615)
I (..\..\..\libraries\drivers\inc\at32f403a_407_dac.h)(0x6203961A)
I (..\..\..\libraries\drivers\inc\at32f403a_407_spi.h)(0x6203962B)
I (..\..\..\libraries\drivers\inc\at32f403a_407_dma.h)(0x6203961E)
I (..\..\..\libraries\drivers\inc\at32f403a_407_debug.h)(0x6203961C)
I (..\..\..\libraries\drivers\inc\at32f403a_407_flash.h)(0x62039622)
I (..\..\..\libraries\drivers\inc\at32f403a_407_crc.h)(0x62039618)
I (..\..\..\libraries\drivers\inc\at32f403a_407_wwdt.h)(0x62039632)
I (..\..\..\libraries\drivers\inc\at32f403a_407_wdt.h)(0x62039630)
I (..\..\..\libraries\drivers\inc\at32f403a_407_exint.h)(0x62039621)
I (..\..\..\libraries\drivers\inc\at32f403a_407_sdio.h)(0x6203962A)
I (..\..\..\libraries\drivers\inc\at32f403a_407_xmc.h)(0x62039633)
I (..\..\..\libraries\drivers\inc\at32f403a_407_acc.h)(0x62039613)
I (..\..\..\libraries\drivers\inc\at32f403a_407_misc.h)(0x62039626)
I (..\..\..\libraries\drivers\inc\at32f403a_407_usb.h)(0x6203962F)
I (..\..\..\libraries\drivers\inc\at32f403a_407_emac.h)(0x6203961F)
I (..\inc\at32_selftest_param.h)(0x624D0D9A)
I (..\..\..\middlewares\classb_lib\inc\at32_selftest_lib.h)(0x623AD2D2)
I (..\..\..\middlewares\classb_lib\inc\at32_selftest_classbvar.h)(0x623AD2D2)
I (..\..\..\middlewares\classb_lib\inc\at32_selftest_startup.h)(0x6225F4AB)
I (..\..\..\middlewares\classb_lib\inc\at32_selftest_runtime.h)(0x6225F3FE)
I (..\..\..\middlewares\classb_lib\inc\at32_selftest_cpu.h)(0x6225B254)
I (..\..\..\middlewares\classb_lib\inc\at32_selftest_clock.h)(0x6225F0E9)
I (..\..\..\middlewares\classb_lib\inc\at32_selftest_crc.h)(0x6225C13C)
I (..\..\..\middlewares\classb_lib\inc\at32_selftest_ram.h)(0x6225F3FE)
F (..\..\..\middlewares\classb_lib\src\at32_selftest_clock.c)(0x623ADBB8)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\inc -I ..\..\..\libraries\drivers\inc -I ..\..\..\project\at32f403a_407_board -I ..\..\..\libraries\cmsis\cm4\device_support -I ..\..\..\libraries\cmsis\cm4\core_support -I ..\..\..\middlewares\classb_lib\inc -I ..\mdk_v5

-ID:\keil5\ARM\Pack\ArteryTek\AT32F403A_407_DFP\2.1.6\Device\Include

-D__UVISION_VERSION="538" -DAT32F403AVGT7 -DAT32F403AVGT7 -DUSE_STDPERIPH_DRIVER -DAT_START_F403A_V1

-o .\objects\at32_selftest_clock.o --omf_browse .\objects\at32_selftest_clock.crf --depend .\objects\at32_selftest_clock.d)
I (..\inc\main.h)(0x62329F1B)
I (..\..\..\project\at32f403a_407_board\at32f403a_407_board.h)(0x620396A5)
I (D:\keil5\ARM\ARMCC\include\stdio.h)(0x5475F300)
I (..\..\..\libraries\cmsis\cm4\device_support\at32f403a_407.h)(0x6203960D)
I (..\..\..\libraries\cmsis\cm4\core_support\core_cm4.h)(0x620395CA)
I (D:\keil5\ARM\ARMCC\include\stdint.h)(0x5475F300)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_version.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_compiler.h)(0x622B0D2A)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_armcc.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\core_support\mpu_armv7.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\device_support\system_at32f403a_407.h)(0x62039612)
I (..\..\..\libraries\drivers\inc\at32f403a_407_def.h)(0x6203961D)
I (..\inc\at32f403a_407_conf.h)(0x61CD8ABA)
I (..\..\..\libraries\drivers\inc\at32f403a_407_crm.h)(0x62039619)
I (..\..\..\libraries\drivers\inc\at32f403a_407_tmr.h)(0x6203962C)
I (..\..\..\libraries\drivers\inc\at32f403a_407_rtc.h)(0x62039628)
I (..\..\..\libraries\drivers\inc\at32f403a_407_bpr.h)(0x62039616)
I (..\..\..\libraries\drivers\inc\at32f403a_407_gpio.h)(0x62039623)
I (..\..\..\libraries\drivers\inc\at32f403a_407_i2c.h)(0x62039625)
I (..\..\..\libraries\drivers\inc\at32f403a_407_usart.h)(0x6203962E)
I (..\..\..\libraries\drivers\inc\at32f403a_407_pwc.h)(0x62039627)
I (..\..\..\libraries\drivers\inc\at32f403a_407_can.h)(0x62039617)
I (..\..\..\libraries\drivers\inc\at32f403a_407_adc.h)(0x62039615)
I (..\..\..\libraries\drivers\inc\at32f403a_407_dac.h)(0x6203961A)
I (..\..\..\libraries\drivers\inc\at32f403a_407_spi.h)(0x6203962B)
I (..\..\..\libraries\drivers\inc\at32f403a_407_dma.h)(0x6203961E)
I (..\..\..\libraries\drivers\inc\at32f403a_407_debug.h)(0x6203961C)
I (..\..\..\libraries\drivers\inc\at32f403a_407_flash.h)(0x62039622)
I (..\..\..\libraries\drivers\inc\at32f403a_407_crc.h)(0x62039618)
I (..\..\..\libraries\drivers\inc\at32f403a_407_wwdt.h)(0x62039632)
I (..\..\..\libraries\drivers\inc\at32f403a_407_wdt.h)(0x62039630)
I (..\..\..\libraries\drivers\inc\at32f403a_407_exint.h)(0x62039621)
I (..\..\..\libraries\drivers\inc\at32f403a_407_sdio.h)(0x6203962A)
I (..\..\..\libraries\drivers\inc\at32f403a_407_xmc.h)(0x62039633)
I (..\..\..\libraries\drivers\inc\at32f403a_407_acc.h)(0x62039613)
I (..\..\..\libraries\drivers\inc\at32f403a_407_misc.h)(0x62039626)
I (..\..\..\libraries\drivers\inc\at32f403a_407_usb.h)(0x6203962F)
I (..\..\..\libraries\drivers\inc\at32f403a_407_emac.h)(0x6203961F)
I (..\..\..\middlewares\classb_lib\inc\at32_selftest_lib.h)(0x623AD2D2)
I (..\inc\at32_selftest_param.h)(0x624D0D9A)
I (..\..\..\middlewares\classb_lib\inc\at32_selftest_classbvar.h)(0x623AD2D2)
I (..\..\..\middlewares\classb_lib\inc\at32_selftest_startup.h)(0x6225F4AB)
I (..\..\..\middlewares\classb_lib\inc\at32_selftest_runtime.h)(0x6225F3FE)
I (..\..\..\middlewares\classb_lib\inc\at32_selftest_cpu.h)(0x6225B254)
I (..\..\..\middlewares\classb_lib\inc\at32_selftest_clock.h)(0x6225F0E9)
I (..\..\..\middlewares\classb_lib\inc\at32_selftest_crc.h)(0x6225C13C)
I (..\..\..\middlewares\classb_lib\inc\at32_selftest_ram.h)(0x6225F3FE)
F (..\..\..\libraries\cmsis\cm4\device_support\system_at32f403a_407.c)(0x62039611)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\inc -I ..\..\..\libraries\drivers\inc -I ..\..\..\project\at32f403a_407_board -I ..\..\..\libraries\cmsis\cm4\device_support -I ..\..\..\libraries\cmsis\cm4\core_support -I ..\..\..\middlewares\classb_lib\inc -I ..\mdk_v5

-ID:\keil5\ARM\Pack\ArteryTek\AT32F403A_407_DFP\2.1.6\Device\Include

-D__UVISION_VERSION="538" -DAT32F403AVGT7 -DAT32F403AVGT7 -DUSE_STDPERIPH_DRIVER -DAT_START_F403A_V1

-o .\objects\system_at32f403a_407.o --omf_browse .\objects\system_at32f403a_407.crf --depend .\objects\system_at32f403a_407.d)
I (..\..\..\libraries\cmsis\cm4\device_support\at32f403a_407.h)(0x6203960D)
I (..\..\..\libraries\cmsis\cm4\core_support\core_cm4.h)(0x620395CA)
I (D:\keil5\ARM\ARMCC\include\stdint.h)(0x5475F300)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_version.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_compiler.h)(0x622B0D2A)
I (..\..\..\libraries\cmsis\cm4\core_support\cmsis_armcc.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\core_support\mpu_armv7.h)(0x620395CA)
I (..\..\..\libraries\cmsis\cm4\device_support\system_at32f403a_407.h)(0x62039612)
I (..\..\..\libraries\drivers\inc\at32f403a_407_def.h)(0x6203961D)
I (..\inc\at32f403a_407_conf.h)(0x61CD8ABA)
I (..\..\..\libraries\drivers\inc\at32f403a_407_crm.h)(0x62039619)
I (..\..\..\libraries\drivers\inc\at32f403a_407_tmr.h)(0x6203962C)
I (..\..\..\libraries\drivers\inc\at32f403a_407_rtc.h)(0x62039628)
I (..\..\..\libraries\drivers\inc\at32f403a_407_bpr.h)(0x62039616)
I (..\..\..\libraries\drivers\inc\at32f403a_407_gpio.h)(0x62039623)
I (..\..\..\libraries\drivers\inc\at32f403a_407_i2c.h)(0x62039625)
I (..\..\..\libraries\drivers\inc\at32f403a_407_usart.h)(0x6203962E)
I (..\..\..\libraries\drivers\inc\at32f403a_407_pwc.h)(0x62039627)
I (..\..\..\libraries\drivers\inc\at32f403a_407_can.h)(0x62039617)
I (..\..\..\libraries\drivers\inc\at32f403a_407_adc.h)(0x62039615)
I (..\..\..\libraries\drivers\inc\at32f403a_407_dac.h)(0x6203961A)
I (..\..\..\libraries\drivers\inc\at32f403a_407_spi.h)(0x6203962B)
I (..\..\..\libraries\drivers\inc\at32f403a_407_dma.h)(0x6203961E)
I (..\..\..\libraries\drivers\inc\at32f403a_407_debug.h)(0x6203961C)
I (..\..\..\libraries\drivers\inc\at32f403a_407_flash.h)(0x62039622)
I (..\..\..\libraries\drivers\inc\at32f403a_407_crc.h)(0x62039618)
I (..\..\..\libraries\drivers\inc\at32f403a_407_wwdt.h)(0x62039632)
I (..\..\..\libraries\drivers\inc\at32f403a_407_wdt.h)(0x62039630)
I (..\..\..\libraries\drivers\inc\at32f403a_407_exint.h)(0x62039621)
I (..\..\..\libraries\drivers\inc\at32f403a_407_sdio.h)(0x6203962A)
I (..\..\..\libraries\drivers\inc\at32f403a_407_xmc.h)(0x62039633)
I (..\..\..\libraries\drivers\inc\at32f403a_407_acc.h)(0x62039613)
I (..\..\..\libraries\drivers\inc\at32f403a_407_misc.h)(0x62039626)
I (..\..\..\libraries\drivers\inc\at32f403a_407_usb.h)(0x6203962F)
I (..\..\..\libraries\drivers\inc\at32f403a_407_emac.h)(0x6203961F)
F (..\..\..\libraries\cmsis\cm4\device_support\startup\mdk\startup_at32f403a_407.s)(0x6203B179)(--cpu Cortex-M4.fp.sp -g --apcs=interwork 

-ID:\keil5\ARM\Pack\ArteryTek\AT32F403A_407_DFP\2.1.6\Device\Include

--pd "__UVISION_VERSION SETA 538" --pd "AT32F403AVGT7 SETA 1"

--list .\listings\startup_at32f403a_407.lst --xref -o .\objects\startup_at32f403a_407.o --depend .\objects\startup_at32f403a_407.d)
F (..\readme.txt)(0x623BFE63)()
