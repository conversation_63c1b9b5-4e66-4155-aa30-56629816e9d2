/**
  **************************************************************************
  * @file     at32_selftest_crc.c
  * @version  v2.0.0
  * @date     2021-12-31
  * @brief    contains self-test crc test.
  **************************************************************************
  */

#include "main.h"
#include "at32_selftest_param.h"
#include "at32_selftest_lib.h"
#ifdef __CC_ARM  /* keil compiler */
#include "crc32.h"
#endif

/**
  * @brief  crc test in multiple steps
  * @param  none
  * @retval crc test status result
  */
crc_status_type selftest_crc_runtime_test(void)
{
  crc_status_type result = CRC_TEST_SUCCESS;

  control_flow_call(CRC_TEST_CALLEE);

  /* check class b var integrity */
  if((uint32_t)p_runtime_crc_chk == ~(uint32_t)p_runtime_crc_chk_inv)
  {
    if(p_runtime_crc_chk < (uint32_t *)ROM_END_ADDR)
    {
      #ifdef __CC_ARM                    /* keil compiler */
      if(p_runtime_crc_chk == (uint32_t *)ROM_START_ADDR)
      {
        crc32_fsl_continuous(0, (void *)p_runtime_crc_chk, ROM_ONCE_VERIYF_SIZE, 0);
      }else
      {
        crc32_fsl_continuous(0, (void *)p_runtime_crc_chk, ROM_ONCE_VERIYF_SIZE, 1);
      }
      #endif
      #ifdef __IAR_SYSTEMS_ICC__         /* iar compiler */
      uint32_t index;
      for(index = 0; index < (uint32_t)(ROM_ONCE_VERIYF_SIZE / sizeof(uint32_t)); index++)
      {
        CRC->dt = __REV(*(p_runtime_crc_chk + index));
      }
      #endif
      result = CRC_TEST_CONTINUE;
      p_runtime_crc_chk += (ROM_ONCE_VERIYF_SIZE / sizeof(uint32_t));     /* increment pointer to next block */
      p_runtime_crc_chk_inv = ((uint32_t *)~((uint32_t)p_runtime_crc_chk));
    }
    else
    { /* not crc calculate step, only get final crc value */
      if(reference_crc == ~reference_crc_inv)
      {
        #ifdef __CC_ARM                  /* keil compiler */
        if(crc32_fsl_continuous(0, (void *)p_runtime_crc_chk, ROM_ONCE_VERIYF_SIZE, 2) != STORED_ROM_CRC)
        {
          result = CRC_TEST_ERROR;
        }
        #endif
        #ifdef __IAR_SYSTEMS_ICC__       /* iar compiler */
        if(CRC->dt != STORED_ROM_CRC)
        {
          result = CRC_TEST_ERROR;
        }
        crc_data_reset();
        #endif
        p_runtime_crc_chk = (uint32_t *)ROM_START_ADDR;
        p_runtime_crc_chk_inv = ((uint32_t *)~((uint32_t)p_runtime_crc_chk));
      }
      else
      {
        result = CRC_TEST_ERROR;
      }
    }
  }
  else
  {
    result =  CRC_TEST_ERROR;
  }

  control_flow_resume(CRC_TEST_CALLEE);

  return result;
}
