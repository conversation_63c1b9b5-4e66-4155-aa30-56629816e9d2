/**
  **************************************************************************
  * @file     at32_selftest_classbvar.h
  * @version  v2.0.0
  * @date     2021-12-31
  * @brief    contains all safety critical variables; they must have
  *           predefined addresses and inverse redundant storage
  **************************************************************************
  */

#ifndef __AT32_SELFTEST_CLASSBVAR_H
#define __AT32_SELFTEST_CLASSBVAR_H

#include "stdint.h"
#include "at32_selftest_param.h"

#ifdef __IAR_SYSTEMS_ICC__  /* iar compiler */
  extern __no_init uint32_t runtime_ram_buf[];
  extern __no_init uint32_t *p_runtime_ram_chk;
  extern __no_init uint32_t *p_runtime_ram_chk_inv;
  extern __no_init uint32_t gap_for_ram_test_overlay[];
  extern __no_init uint32_t ctrl_flow_cnt;
  extern __no_init uint32_t ctrl_flow_cnt_inv;
  extern __no_init __IO uint32_t isr_ctrl_flow_cnt;
  extern __no_init __IO uint32_t isr_ctrl_flow_cnt_inv;
  extern __no_init __IO uint32_t period_val;
  extern __no_init __IO uint32_t period_val_inv;
  extern __no_init uint32_t systick_cnt;
  extern __no_init uint32_t systick_cnt_inv;
  extern __no_init __IO uint32_t time_base_flag;
  extern __no_init __IO uint32_t time_base_flag_inv;
  extern __no_init __IO uint32_t lick_period_flag;
  extern __no_init __IO uint32_t lick_period_flag_inv;
  extern __no_init uint32_t last_ctrl_flow_cnt;
  extern __no_init uint32_t last_ctrl_flow_cnt_inv;
  extern __no_init uint32_t *p_runtime_crc_chk;
  extern __no_init uint32_t *p_runtime_crc_chk_inv;
  extern __no_init uint32_t reference_crc;
  extern __no_init uint32_t reference_crc_inv;
  extern __no_init __IO uint32_t stack_overflow_buf[];
#endif

#ifdef __CC_ARM   /* keil compiler */
  extern uint32_t runtime_ram_buf[];
  extern uint32_t *p_runtime_ram_chk;
  extern uint32_t *p_runtime_ram_chk_inv;
  extern uint32_t gap_for_ram_test_overlay[];                                      
  extern uint32_t ctrl_flow_cnt;
  extern uint32_t ctrl_flow_cnt_inv;
  extern uint32_t isr_ctrl_flow_cnt;
  extern uint32_t isr_ctrl_flow_cnt_inv;
  extern uint32_t period_val;
  extern uint32_t period_val_inv;
  extern uint32_t systick_cnt;
  extern uint32_t systick_cnt_inv;
  extern __IO uint32_t time_base_flag;
  extern __IO uint32_t time_base_flag_inv;
  extern __IO uint32_t lick_period_flag;
  extern __IO uint32_t lick_period_flag_inv;
  extern uint32_t last_ctrl_flow_cnt;
  extern uint32_t last_ctrl_flow_cnt_inv;
  extern uint32_t *p_runtime_crc_chk;
  extern uint32_t *p_runtime_crc_chk_inv;
  extern uint32_t reference_crc;
  extern uint32_t reference_crc_inv;
  extern __IO uint32_t stack_overflow_buf[];
#endif

#endif
