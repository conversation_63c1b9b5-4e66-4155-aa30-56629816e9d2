; *************************************************************
; *** Scatter-Loading Description File generated by uVision ***
; *************************************************************

LR_IROM1 0x08000000 0x00100000  {    ; load region size_region
  ER_IROM1 0x08000000 0x00100000  {  ; load address = execution address
   *.o (RESET, +First)
   *(InRoot$$Sections)
   .ANY (+RO)
   .ANY (+XO)
  }
	
  RAM_BUF 0x20000000 UNINIT 0x20 {			; runtime ram test buffer
    at32_selftest_startup.o (RUNTIME_RAM_BUF)
  }

  RAM_POINTER 0x20000020	UNINIT 0x10 {			; runtime ram test pointer
    at32_selftest_startup.o (RUNTIME_RAM_POINTER)
  }

  CLASSB 0x20000030 UNINIT 0x24 {		  ; class b variables
    at32_selftest_startup.o (CLASS_B_RAM)
  }

  CLASSB_INV 0x20000054 UNINIT 0x24 {   ; class b inverted variables
    at32_selftest_startup.o (CLASS_B_RAM_REV)
  }

  CLASSA 0x20000078 UNINIT 0x1000 {   ; rw data (class a variables)
    .ANY (+RW +ZI)
  }
  
  STACK_NO_HEAP 0x20001078 UNINIT 0x410  {  ; stack and magic pattern for stack overflow detection
    at32_selftest_startup.o (STACK_BOTTOM)
    startup_at32f403a_407.o (STACK, +Last)
  }
	
}