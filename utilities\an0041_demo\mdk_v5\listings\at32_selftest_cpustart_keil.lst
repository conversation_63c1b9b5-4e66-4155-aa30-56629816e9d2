


ARM Macro Assembler    Page 1 


    1 00000000         ;/**
    2 00000000         ;  *****************************************************
                       *********************
    3 00000000         ;  * @file     at32_selftest_cpustart_keil.s
    4 00000000         ;  * @version  v2.0.0
    5 00000000         ;  * @date     2021-12-31
    6 00000000         ;  * @brief    contains cpu self-test in startup phase.
    7 00000000         ;  *****************************************************
                       *********************
    8 00000000         ;  */
    9 00000000         
   10 00000000                 THUMB
   11 00000000                 REQUIRE8
   12 00000000                 PRESERVE8
   13 00000000         
   14 00000000                 AREA             |.text|, CODE, READONLY, ALIGN=
2
   15 00000000         
   16 00000000         ; reference to the selftest_fail_handle routine to be ex
                       ecuted in case of non-recoverable
   17 00000000         ; failure         
   18 00000000                 IMPORT           selftest_fail_handle
   19 00000000         
   20 00000000         ; c variables for control flow monitoring
   21 00000000                 IMPORT           ctrl_flow_cnt
   22 00000000                 IMPORT           ctrl_flow_cnt_inv
   23 00000000         
   24 00000000         ;/**
   25 00000000         ;  * @brief  cpu selftest in startup.
   26 00000000         ;  * @note   all registers destroyed when exiting this f
                       unction (including preserved
   27 00000000         ;  *         registers r4 to r11) and excluding stack po
                       int r13)
   28 00000000         ;  *         when possible, branch are 16-bit only (depe
                       nding on relative offset to 
   29 00000000         ;  *         final bl instruction
   30 00000000         ;  * @param  none
   31 00000000         ;  * @retval none
   32 00000000         ;  */
   33 00000000         selftest_cpu_startup_test
                               PROC
   34 00000000                 EXPORT           selftest_cpu_startup_test
   35 00000000 2000            MOVS             R0, #0x00000000 
                                                            ; set z(ero) flag
   36 00000002 F47F AFFE       BNE.W            selftest_fail_handle 
                                                            ; fails if z clear
   37 00000006 3801            SUBS             R0,#1       ; set n(egative) fl
                                                            ag
   38 00000008 F57F AFFE       BPL.W            selftest_fail_handle 
                                                            ; fails if n clear
   39 0000000C 3002            ADDS             R0,#2       ; set c(arry) flag 
                                                            and do not set z
   40 0000000E F4FF AFFE       BCC.W            selftest_fail_handle 
                                                            ; fails if c clear
   41 00000012 F05F 4000       MOVS             R0, #0x80000000 ; prepares over
                                                            flow test
   42 00000016 1800            ADDS             R0, R0, R0  ; set v(overflow) f
                                                            lag
   43 00000018 F5FF AFFE       BVC.W            selftest_fail_handle 



ARM Macro Assembler    Page 2 


                                                            ; fails if v clear
   44 0000001C F05F 30FF       MOVS             R0, #0xFFFFFFFF ; prepares satu
                                                            ration test
   45 00000020 F380 010A       USAT             R1,#10,R0   ; set q(saturation)
                                                             flag
   46 00000024 F3EF 8000       MRS              R0, APSR    ; get status regist
                                                            er
   47 00000028 F1B0 4F38       CMP              R0, #0xB8000000 ; verifies that
                                                             n=c=v=q=1
   48 0000002C F47F AFFE       BNE.W            selftest_fail_handle ; fails if
                                                             q+n+c=v clear
   49 00000030         
   50 00000030         ; register r0 (holds value returned by the function)
   51 00000030 F05F 30AA       MOVS             R0, #0xAAAAAAAA
   52 00000034 F1B0 3FAA       CMP              R0, #0xAAAAAAAA
   53 00000038 F47F AFFE       BNE.W            selftest_fail_handle
   54 0000003C F05F 3055       MOVS             R0, #0x55555555
   55 00000040 F1B0 3F55       CMP              R0, #0x55555555
   56 00000044 F47F AFFE       BNE.W            selftest_fail_handle
   57 00000048         
   58 00000048         ; this is for control flow test (entry point)
   59 00000048 488D            LDR              R0,=ctrl_flow_cnt
   60 0000004A         ; assumes r1 ok; if not, error will be detected by r1 te
                       st and ctrl flow test later on
   61 0000004A 6801            LDR              R1,[R0]
   62 0000004C 1CC9            ADDS             R1,R1,#0x3  ; ctrl_flow_cnt += 
                                                            OxO3
   63 0000004E 6001            STR              R1,[R0]
   64 00000050         
   65 00000050         ; link register (register r14)
   66 00000050 EA5F 000E       MOVS             R0, R14     ; contains the retu
                                                            rn address and must
                                                             be saved
   67 00000054 F05F 3EAA       MOVS             R14, #0xAAAAAAAA
   68 00000058 F1BE 3FAA       CMP              R14, #0xAAAAAAAA
   69 0000005C F47F AFFE       BNE.W            selftest_fail_handle
   70 00000060 F05F 3E55       MOVS             R14, #0x55555555
   71 00000064 F1BE 3F55       CMP              R14, #0x55555555
   72 00000068 F47F AFFE       BNE.W            selftest_fail_handle
   73 0000006C EA5F 0E00       MOVS             R14, R0
   74 00000070 2000            MOVS             R0, #0x0    ; for ramp test
   75 00000072         
   76 00000072         ; Register R1
   77 00000072 F05F 31AA       MOVS             R1, #0xAAAAAAAA
   78 00000076 F1B1 3FAA       CMP              R1, #0xAAAAAAAA
   79 0000007A F47F AFFE       BNE.W            selftest_fail_handle
   80 0000007E F05F 3155       MOVS             R1, #0x55555555
   81 00000082 F1B1 3F55       CMP              R1, #0x55555555
   82 00000086 F47F AFFE       BNE.W            selftest_fail_handle
   83 0000008A 2101            MOVS             R1, #0x01   ; for ramp test
   84 0000008C         
   85 0000008C         ; Register R2
   86 0000008C F05F 32AA       MOVS             R2, #0xAAAAAAAA
   87 00000090 F1B2 3FAA       CMP              R2, #0xAAAAAAAA
   88 00000094 F47F AFFE       BNE.W            selftest_fail_handle
   89 00000098 F05F 3255       MOVS             R2, #0x55555555
   90 0000009C F1B2 3F55       CMP              R2, #0x55555555
   91 000000A0 F47F AFFE       BNE.W            selftest_fail_handle
   92 000000A4 2202            MOVS             R2, #0x02   ; for ramp test



ARM Macro Assembler    Page 3 


   93 000000A6         
   94 000000A6         ; Register R3
   95 000000A6 F05F 33AA       MOVS             R3, #0xAAAAAAAA
   96 000000AA F1B3 3FAA       CMP              R3, #0xAAAAAAAA
   97 000000AE F47F AFFE       BNE.W            selftest_fail_handle
   98 000000B2 F05F 3355       MOVS             R3, #0x55555555
   99 000000B6 F1B3 3F55       CMP              R3, #0x55555555
  100 000000BA F47F AFFE       BNE.W            selftest_fail_handle
  101 000000BE 2303            MOVS             R3, #0x03   ; for ramp test
  102 000000C0         
  103 000000C0         ; Register R4
  104 000000C0 F05F 34AA       MOVS             R4, #0xAAAAAAAA
  105 000000C4 F1B4 3FAA       CMP              R4, #0xAAAAAAAA
  106 000000C8 F47F AFFE       BNE.W            selftest_fail_handle
  107 000000CC F05F 3455       MOVS             R4, #0x55555555
  108 000000D0 F1B4 3F55       CMP              R4, #0x55555555
  109 000000D4 F47F AFFE       BNE.W            selftest_fail_handle
  110 000000D8 2404            MOVS             R4, #0x04   ; for ramp test
  111 000000DA         
  112 000000DA         ; Register R5
  113 000000DA F05F 35AA       MOVS             R5, #0xAAAAAAAA
  114 000000DE F1B5 3FAA       CMP              R5, #0xAAAAAAAA
  115 000000E2 F47F AFFE       BNE.W            selftest_fail_handle
  116 000000E6 F05F 3555       MOVS             R5, #0x55555555
  117 000000EA F1B5 3F55       CMP              R5, #0x55555555
  118 000000EE F47F AFFE       BNE.W            selftest_fail_handle
  119 000000F2 2505            MOVS             R5, #0x05   ; for ramp test
  120 000000F4         
  121 000000F4         ; Register R6
  122 000000F4 F05F 36AA       MOVS             R6, #0xAAAAAAAA
  123 000000F8 F1B6 3FAA       CMP              R6, #0xAAAAAAAA
  124 000000FC F47F AFFE       BNE.W            selftest_fail_handle
  125 00000100 F05F 3655       MOVS             R6, #0x55555555
  126 00000104 F1B6 3F55       CMP              R6, #0x55555555
  127 00000108 F47F AFFE       BNE.W            selftest_fail_handle
  128 0000010C 2606            MOVS             R6, #0x06   ; for ramp test
  129 0000010E         
  130 0000010E         ; Register R7
  131 0000010E F05F 37AA       MOVS             R7, #0xAAAAAAAA
  132 00000112 F1B7 3FAA       CMP              R7, #0xAAAAAAAA
  133 00000116 F47F AFFE       BNE.W            selftest_fail_handle
  134 0000011A F05F 3755       MOVS             R7, #0x55555555
  135 0000011E F1B7 3F55       CMP              R7, #0x55555555
  136 00000122 F47F AFFE       BNE.W            selftest_fail_handle
  137 00000126 2707            MOVS             R7, #0x07   ; for ramp test
  138 00000128         
  139 00000128         ; Register R8
  140 00000128 F05F 38AA       MOVS             R8, #0xAAAAAAAA
  141 0000012C F1B8 3FAA       CMP              R8, #0xAAAAAAAA
  142 00000130 F47F AFFE       BNE.W            selftest_fail_handle
  143 00000134 F05F 3855       MOVS             R8, #0x55555555
  144 00000138 F1B8 3F55       CMP              R8, #0x55555555
  145 0000013C F47F AFFE       BNE.W            selftest_fail_handle
  146 00000140 F05F 0808       MOVS             R8, #0x08   ; for ramp test
  147 00000144         
  148 00000144         ; Register R9
  149 00000144 F05F 39AA       MOVS             R9, #0xAAAAAAAA
  150 00000148 F1B9 3FAA       CMP              R9, #0xAAAAAAAA
  151 0000014C F47F AFFE       BNE.W            selftest_fail_handle



ARM Macro Assembler    Page 4 


  152 00000150 F05F 3955       MOVS             R9, #0x55555555
  153 00000154 F1B9 3F55       CMP              R9, #0x55555555
  154 00000158 F47F AFFE       BNE.W            selftest_fail_handle
  155 0000015C F05F 0909       MOVS             R9, #0x09   ; for ramp test
  156 00000160         
  157 00000160         ; Register R10
  158 00000160 F05F 3AAA       MOVS             R10, #0xAAAAAAAA
  159 00000164 F1BA 3FAA       CMP              R10, #0xAAAAAAAA
  160 00000168 F47F AFFE       BNE              selftest_fail_handle
  161 0000016C F05F 3A55       MOVS             R10, #0x55555555
  162 00000170 F1BA 3F55       CMP              R10, #0x55555555
  163 00000174 F47F AFFE       BNE              selftest_fail_handle
  164 00000178 F05F 0A0A       MOVS             R10, #0x0A  ; for ramp test
  165 0000017C         
  166 0000017C         ; Register R11
  167 0000017C F05F 3BAA       MOVS             R11, #0xAAAAAAAA
  168 00000180 F1BB 3FAA       CMP              R11, #0xAAAAAAAA
  169 00000184 F47F AFFE       BNE              selftest_fail_handle
  170 00000188 F05F 3B55       MOVS             R11, #0x55555555
  171 0000018C F1BB 3F55       CMP              R11, #0x55555555
  172 00000190 F47F AFFE       BNE              selftest_fail_handle
  173 00000194 F05F 0B0B       MOVS             R11, #0x0B  ; for ramp test
  174 00000198         
  175 00000198         ; Register R12
  176 00000198 F05F 3CAA       MOVS             R12, #0xAAAAAAAA
  177 0000019C F1BC 3FAA       CMP              R12, #0xAAAAAAAA
  178 000001A0 F47F AFFE       BNE              selftest_fail_handle
  179 000001A4 F05F 3C55       MOVS             R12, #0x55555555
  180 000001A8 F1BC 3F55       CMP              R12, #0x55555555
  181 000001AC F47F AFFE       BNE              selftest_fail_handle
  182 000001B0 F05F 0C0C       MOVS             R12, #0x0C  ; for ramp test
  183 000001B4         
  184 000001B4         ; Ramp pattern verification
  185 000001B4 2800            CMP              R0, #0x00
  186 000001B6 F47F AFFE       BNE              selftest_fail_handle
  187 000001BA 2901            CMP              R1, #0x01
  188 000001BC F47F AFFE       BNE              selftest_fail_handle
  189 000001C0 2A02            CMP              R2, #0x02
  190 000001C2 F47F AFFE       BNE              selftest_fail_handle
  191 000001C6 2B03            CMP              R3, #0x03
  192 000001C8 F47F AFFE       BNE              selftest_fail_handle
  193 000001CC 2C04            CMP              R4, #0x04
  194 000001CE F47F AFFE       BNE              selftest_fail_handle
  195 000001D2 2D05            CMP              R5, #0x05
  196 000001D4 F47F AFFE       BNE              selftest_fail_handle
  197 000001D8 2E06            CMP              R6, #0x06
  198 000001DA F47F AFFE       BNE              selftest_fail_handle
  199 000001DE 2F07            CMP              R7, #0x07
  200 000001E0 F47F AFFE       BNE              selftest_fail_handle
  201 000001E4 F1B8 0F08       CMP              R8, #0x08
  202 000001E8 F47F AFFE       BNE              selftest_fail_handle
  203 000001EC F1B9 0F09       CMP              R9, #0x09
  204 000001F0 F47F AFFE       BNE              selftest_fail_handle
  205 000001F4 F1BA 0F0A       CMP              R10, #0x0A
  206 000001F8 F47F AFFE       BNE              selftest_fail_handle
  207 000001FC F1BB 0F0B       CMP              R11, #0x0B
  208 ******** F47F AFFE       BNE              selftest_fail_handle
  209 ******** F1BC 0F0C       CMP              R12, #0x0C
  210 ******** F47F AFFE       BNE              selftest_fail_handle



ARM Macro Assembler    Page 5 


  211 0000020C         
  212 0000020C         ; process stack pointer (banked register r13)
  213 0000020C F3EF 8009       MRS              R0, PSP     ; save process stac
                                                            k value
  214 ******** F64A 21A8       MOV              R1, #0xAAA8 ; test is different
                                                             (psp is word align
                                                            ed)
  215 ******** F6CA 21AA       MOVT             R1, #0xAAAA ; load in two times
                                                             and 2 least signif
                                                            icant bits cleared
  216 ******** F381 8809       MSR              PSP, R1     ; load process stac
                                                            k value
  217 0000021C F3EF 8209       MRS              R2, PSP     ; get back process 
                                                            stack value
  218 ******** 428A            CMP              R2, R1      ; verify value
  219 00000222 F47F AFFE       BNE              selftest_fail_handle
  220 00000226 F245 5154       MOV              R1, #0x5554 ; test is different
                                                             (psp is word align
                                                            ed)
  221 0000022A F2C5 5155       MOVT             R1, #0x5555 ; load in two times
                                                             and 2 least signif
                                                            icant bits cleared
  222 0000022E F381 8809       MSR              PSP, R1     ; load process stac
                                                            k value
  223 00000232 F3EF 8209       MRS              R2, PSP     ; get back process 
                                                            stack value
  224 00000236 428A            CMP              R2, R1      ; verify value
  225 00000238 F47F AFFE       BNE              selftest_fail_handle
  226 0000023C F380 8809       MSR              PSP, R0     ; restore process s
                                                            tack value
  227 00000240         
  228 00000240         ; stack pointer (register r13)
  229 00000240 F3EF 8008       MRS              R0, MSP     ; save process stac
                                                            k value
  230 00000244 F64A 21A8       MOV              R1, #0xAAA8 ; test is different
                                                             (msp is word align
                                                            ed)
  231 00000248 F6CA 21AA       MOVT             R1, #0xAAAA ; load in two times
                                                             and 2 least signif
                                                            icant bits cleared
  232 0000024C F381 8808       MSR              MSP, R1     ; load process stac
                                                            k value
  233 00000250 F3EF 8208       MRS              R2, MSP     ; get back process 
                                                            stack value
  234 00000254 428A            CMP              R2, R1      ; verify value
  235 00000256 F47F AFFE       BNE              selftest_fail_handle
  236 0000025A F245 5154       MOV              R1, #0x5554 ; test is different
                                                             (msp is word align
                                                            ed)
  237 0000025E F2C5 5155       MOVT             R1, #0x5555 ; load in two times
                                                             and 2 least signif
                                                            icant bits cleared
  238 00000262 F381 8808       MSR              MSP, R1     ; load process stac
                                                            k value
  239 00000266 F3EF 8208       MRS              R2, MSP     ; get back process 
                                                            stack value
  240 0000026A 428A            CMP              R2, R1      ; verify value
  241 0000026C F47F AFFE       BNE              selftest_fail_handle
  242 00000270 F380 8808       MSR              MSP, R0     ; restore process s



ARM Macro Assembler    Page 6 


                                                            tack value
  243 00000274         
  244 00000274         ; control flow test (exit point)
  245 00000274 4803            LDR              R0,=ctrl_flow_cnt_inv
  246 00000276 6801            LDR              R1,[R0]
  247 00000278 1EC9            SUBS             R1,R1,#0x3  ; ctrl_flow_cnt_inv
                                                             -= OxO3
  248 0000027A 6001            STR              R1,[R0]
  249 0000027C         
  250 0000027C         ; if next instruction is not executed, r0 will hold a va
                       lue different from 0
  251 0000027C 2001            MOVS             R0, #0x1    ; cpu_test_success
  252 0000027E 4770            BX               LR          ; return to the cal
                                                            ler
  253 00000280                 ENDP
  254 00000280         
  255 00000280                 END
              00000000 
              00000000 
Command Line: --debug --xref --diag_suppress=9931 --cpu=Cortex-M4.fp.sp --apcs=
interwork --depend=.\objects\at32_selftest_cpustart_keil.d -o.\objects\at32_sel
ftest_cpustart_keil.o -ID:\keil5\ARM\Pack\ArteryTek\AT32F403A_407_DFP\2.1.6\Dev
ice\Include --predefine="__UVISION_VERSION SETA 538" --predefine="AT32F403AVGT7
 SETA 1" --list=.\listings\at32_selftest_cpustart_keil.lst at32_selftest_cpusta
rt_keil.s



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

.text 00000000

Symbol: .text
   Definitions
      At line 14 in file at32_selftest_cpustart_keil.s
   Uses
      None
Comment: .text unused
selftest_cpu_startup_test 00000000

Symbol: selftest_cpu_startup_test
   Definitions
      At line 33 in file at32_selftest_cpustart_keil.s
   Uses
      At line 34 in file at32_selftest_cpustart_keil.s
Comment: selftest_cpu_startup_test used once
2 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
External symbols

ctrl_flow_cnt 00000000

Symbol: ctrl_flow_cnt
   Definitions
      At line 21 in file at32_selftest_cpustart_keil.s
   Uses
      At line 59 in file at32_selftest_cpustart_keil.s
Comment: ctrl_flow_cnt used once
ctrl_flow_cnt_inv 00000000

Symbol: ctrl_flow_cnt_inv
   Definitions
      At line 22 in file at32_selftest_cpustart_keil.s
   Uses
      At line 245 in file at32_selftest_cpustart_keil.s
Comment: ctrl_flow_cnt_inv used once
selftest_fail_handle 00000000

Symbol: selftest_fail_handle
   Definitions
      At line 18 in file at32_selftest_cpustart_keil.s
   Uses
      At line 36 in file at32_selftest_cpustart_keil.s
      At line 38 in file at32_selftest_cpustart_keil.s
      At line 40 in file at32_selftest_cpustart_keil.s
      At line 43 in file at32_selftest_cpustart_keil.s
      At line 48 in file at32_selftest_cpustart_keil.s
      At line 53 in file at32_selftest_cpustart_keil.s
      At line 56 in file at32_selftest_cpustart_keil.s
      At line 69 in file at32_selftest_cpustart_keil.s
      At line 72 in file at32_selftest_cpustart_keil.s
      At line 79 in file at32_selftest_cpustart_keil.s
      At line 82 in file at32_selftest_cpustart_keil.s
      At line 88 in file at32_selftest_cpustart_keil.s
      At line 91 in file at32_selftest_cpustart_keil.s
      At line 97 in file at32_selftest_cpustart_keil.s
      At line 100 in file at32_selftest_cpustart_keil.s
      At line 106 in file at32_selftest_cpustart_keil.s
      At line 109 in file at32_selftest_cpustart_keil.s
      At line 115 in file at32_selftest_cpustart_keil.s
      At line 118 in file at32_selftest_cpustart_keil.s
      At line 124 in file at32_selftest_cpustart_keil.s
      At line 127 in file at32_selftest_cpustart_keil.s
      At line 133 in file at32_selftest_cpustart_keil.s
      At line 136 in file at32_selftest_cpustart_keil.s
      At line 142 in file at32_selftest_cpustart_keil.s
      At line 145 in file at32_selftest_cpustart_keil.s
      At line 151 in file at32_selftest_cpustart_keil.s
      At line 154 in file at32_selftest_cpustart_keil.s
      At line 160 in file at32_selftest_cpustart_keil.s
      At line 163 in file at32_selftest_cpustart_keil.s
      At line 169 in file at32_selftest_cpustart_keil.s
      At line 172 in file at32_selftest_cpustart_keil.s
      At line 178 in file at32_selftest_cpustart_keil.s
      At line 181 in file at32_selftest_cpustart_keil.s
      At line 186 in file at32_selftest_cpustart_keil.s
      At line 188 in file at32_selftest_cpustart_keil.s
      At line 190 in file at32_selftest_cpustart_keil.s
      At line 192 in file at32_selftest_cpustart_keil.s



ARM Macro Assembler    Page 2 Alphabetic symbol ordering
External symbols

      At line 194 in file at32_selftest_cpustart_keil.s
      At line 196 in file at32_selftest_cpustart_keil.s
      At line 198 in file at32_selftest_cpustart_keil.s
      At line 200 in file at32_selftest_cpustart_keil.s
      At line 202 in file at32_selftest_cpustart_keil.s
      At line 204 in file at32_selftest_cpustart_keil.s
      At line 206 in file at32_selftest_cpustart_keil.s
      At line 208 in file at32_selftest_cpustart_keil.s
      At line 210 in file at32_selftest_cpustart_keil.s
      At line 219 in file at32_selftest_cpustart_keil.s
      At line 225 in file at32_selftest_cpustart_keil.s
      At line 235 in file at32_selftest_cpustart_keil.s
      At line 241 in file at32_selftest_cpustart_keil.s

3 symbols
340 symbols in table
