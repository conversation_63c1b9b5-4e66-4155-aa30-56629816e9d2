


ARM Macro Assembler    Page 1 


    1 00000000         ;/**
    2 00000000         ;  *****************************************************
                       *********************
    3 00000000         ;  * @file     at32_selftest_cpurun_keil.s
    4 00000000         ;  * @version  v2.0.0
    5 00000000         ;  * @date     2021-12-31
    6 00000000         ;  * @brief    contains cpu self-test in runtime phase.
    7 00000000         ;  *****************************************************
                       *********************
    8 00000000         ;  */
    9 00000000         
   10 00000000                 THUMB
   11 00000000                 REQUIRE8
   12 00000000                 PRESERVE8
   13 00000000         
   14 00000000                 AREA             |.text|, CODE, READONLY, ALIGN=
2
   15 00000000         
   16 00000000         ; reference to the selftest_fail_handle routine to be ex
                       ecuted in case of non-recoverable
   17 00000000         ; failure
   18 00000000                 IMPORT           selftest_fail_handle
   19 00000000         
   20 00000000         ; c variables for control flow monitoring
   21 00000000                 IMPORT           ctrl_flow_cnt
   22 00000000                 IMPORT           ctrl_flow_cnt_inv
   23 00000000         
   24 00000000         ;/**
   25 00000000         ;  * @brief  cpu selftest in runtime.
   26 00000000         ;  * @note   when possible, branch are 16-bit only (depe
                       nding on relative offset to final bl instruction)
   27 00000000         ;  * @param  input: none.
   28 00000000         ;  *         output: branch directly to a fail safe rout
                       ine in case of failure
   29 00000000         ;  * @retval cpu_test_success (=1) if test is ok
   30 00000000         ;  */
   31 00000000         selftest_cpu_runtime_test
                               PROC
   32 00000000                 EXPORT           selftest_cpu_runtime_test
   33 00000000         
   34 00000000 E92D 0FF0       STMDB            SP!, {R4, R5, R6, R7, R8, R9, R
10, R11}
   35 00000004         ; register r0 (holds value returned by the function)
   36 00000004 F05F 30AA       MOVS             R0, #0xAAAAAAAA
   37 00000008 F1B0 3FAA       CMP              R0, #0xAAAAAAAA
   38 0000000C F47F AFFE       BNE.W            selftest_fail_handle
   39 00000010 F05F 3055       MOVS             R0, #0x55555555
   40 00000014 F1B0 3F55       CMP              R0, #0x55555555
   41 00000018 F47F AFFE       BNE.W            selftest_fail_handle
   42 0000001C         
   43 0000001C         ; this is for control flow test (entry point)
   44 0000001C 486C            LDR              R0,=ctrl_flow_cnt
   45 0000001E         ; assumes r1 ok; if not, error will be detected by r1 te
                       st and ctrl flow test later on
   46 0000001E 6801            LDR              R1,[R0]
   47 00000020 1CC9            ADDS             R1,R1,#0x3  ; ctrl_flow_cnt += 
                                                            OxO3
   48 00000022 6001            STR              R1,[R0]
   49 00000024         



ARM Macro Assembler    Page 2 


   50 00000024 2000            MOVS             R0, #0x0    ; for ramp test
   51 00000026         
   52 00000026         ; register r1
   53 00000026 F05F 31AA       MOVS             R1, #0xAAAAAAAA
   54 0000002A F1B1 3FAA       CMP              R1, #0xAAAAAAAA
   55 0000002E F47F AFFE       BNE.W            selftest_fail_handle
   56 00000032 F05F 3155       MOVS             R1, #0x55555555
   57 00000036 F1B1 3F55       CMP              R1, #0x55555555
   58 0000003A F47F AFFE       BNE.W            selftest_fail_handle
   59 0000003E 2101            MOVS             R1, #0x01   ; for ramp test
   60 00000040         
   61 00000040         ; register r2
   62 00000040 F05F 32AA       MOVS             R2, #0xAAAAAAAA
   63 00000044 F1B2 3FAA       CMP              R2, #0xAAAAAAAA
   64 00000048 F47F AFFE       BNE.W            selftest_fail_handle
   65 0000004C F05F 3255       MOVS             R2, #0x55555555
   66 00000050 F1B2 3F55       CMP              R2, #0x55555555
   67 00000054 F47F AFFE       BNE.W            selftest_fail_handle
   68 00000058 2202            MOVS             R2, #0x02   ; for ramp test
   69 0000005A         
   70 0000005A         ; register r3
   71 0000005A F05F 33AA       MOVS             R3, #0xAAAAAAAA
   72 0000005E F1B3 3FAA       CMP              R3, #0xAAAAAAAA
   73 00000062 F47F AFFE       BNE.W            selftest_fail_handle
   74 00000066 F05F 3355       MOVS             R3, #0x55555555
   75 0000006A F1B3 3F55       CMP              R3, #0x55555555
   76 0000006E F47F AFFE       BNE.W            selftest_fail_handle
   77 00000072 2303            MOVS             R3, #0x03   ; for ramp test
   78 00000074         
   79 00000074         ; register r4
   80 00000074 F05F 34AA       MOVS             R4, #0xAAAAAAAA
   81 00000078 F1B4 3FAA       CMP              R4, #0xAAAAAAAA
   82 0000007C F47F AFFE       BNE.W            selftest_fail_handle
   83 00000080 F05F 3455       MOVS             R4, #0x55555555
   84 00000084 F1B4 3F55       CMP              R4, #0x55555555
   85 00000088 F47F AFFE       BNE.W            selftest_fail_handle
   86 0000008C 2404            MOVS             R4, #0x04   ; for ramp test
   87 0000008E         
   88 0000008E         ; register r5
   89 0000008E F05F 35AA       MOVS             R5, #0xAAAAAAAA
   90 00000092 F1B5 3FAA       CMP              R5, #0xAAAAAAAA
   91 00000096 F47F AFFE       BNE.W            selftest_fail_handle
   92 0000009A F05F 3555       MOVS             R5, #0x55555555
   93 0000009E F1B5 3F55       CMP              R5, #0x55555555
   94 000000A2 F47F AFFE       BNE.W            selftest_fail_handle
   95 000000A6 2505            MOVS             R5, #0x05   ; for ramp test
   96 000000A8         
   97 000000A8         ; register r6
   98 000000A8 F05F 36AA       MOVS             R6, #0xAAAAAAAA
   99 000000AC F1B6 3FAA       CMP              R6, #0xAAAAAAAA
  100 000000B0 F47F AFFE       BNE.W            selftest_fail_handle
  101 000000B4 F05F 3655       MOVS             R6, #0x55555555
  102 000000B8 F1B6 3F55       CMP              R6, #0x55555555
  103 000000BC F47F AFFE       BNE.W            selftest_fail_handle
  104 000000C0 2606            MOVS             R6, #0x06   ; for ramp test
  105 000000C2         
  106 000000C2         ; register r7
  107 000000C2 F05F 37AA       MOVS             R7, #0xAAAAAAAA
  108 000000C6 F1B7 3FAA       CMP              R7, #0xAAAAAAAA



ARM Macro Assembler    Page 3 


  109 000000CA F47F AFFE       BNE.W            selftest_fail_handle
  110 000000CE F05F 3755       MOVS             R7, #0x55555555
  111 000000D2 F1B7 3F55       CMP              R7, #0x55555555
  112 000000D6 F47F AFFE       BNE.W            selftest_fail_handle
  113 000000DA 2707            MOVS             R7, #0x07   ; for ramp test
  114 000000DC         
  115 000000DC         ; register r8
  116 000000DC F05F 38AA       MOVS             R8, #0xAAAAAAAA
  117 000000E0 F1B8 3FAA       CMP              R8, #0xAAAAAAAA
  118 000000E4 F47F AFFE       BNE.W            selftest_fail_handle
  119 000000E8 F05F 3855       MOVS             R8, #0x55555555
  120 000000EC F1B8 3F55       CMP              R8, #0x55555555
  121 000000F0 F47F AFFE       BNE.W            selftest_fail_handle
  122 000000F4 F05F 0808       MOVS             R8, #0x08   ; for ramp test
  123 000000F8         
  124 000000F8         ; register r9
  125 000000F8 F05F 39AA       MOVS             R9, #0xAAAAAAAA
  126 000000FC F1B9 3FAA       CMP              R9, #0xAAAAAAAA
  127 00000100 F47F AFFE       BNE.W            selftest_fail_handle
  128 00000104 F05F 3955       MOVS             R9, #0x55555555
  129 00000108 F1B9 3F55       CMP              R9, #0x55555555
  130 0000010C F47F AFFE       BNE.W            selftest_fail_handle
  131 00000110 F05F 0909       MOVS             R9, #0x09   ; for ramp test
  132 00000114         
  133 00000114         ; register r10
  134 00000114 F05F 3AAA       MOVS             R10, #0xAAAAAAAA
  135 00000118 F1BA 3FAA       CMP              R10, #0xAAAAAAAA
  136 0000011C F47F AFFE       BNE              selftest_fail_handle
  137 00000120 F05F 3A55       MOVS             R10, #0x55555555
  138 00000124 F1BA 3F55       CMP              R10, #0x55555555
  139 00000128 F47F AFFE       BNE              selftest_fail_handle
  140 0000012C F05F 0A0A       MOVS             R10, #0x0A  ; for ramp test
  141 00000130         
  142 00000130         ; register r11
  143 00000130 F05F 3BAA       MOVS             R11, #0xAAAAAAAA
  144 00000134 F1BB 3FAA       CMP              R11, #0xAAAAAAAA
  145 00000138 F47F AFFE       BNE              selftest_fail_handle
  146 0000013C F05F 3B55       MOVS             R11, #0x55555555
  147 00000140 F1BB 3F55       CMP              R11, #0x55555555
  148 00000144 F47F AFFE       BNE              selftest_fail_handle
  149 00000148 F05F 0B0B       MOVS             R11, #0x0B  ; for ramp test
  150 0000014C         
  151 0000014C         ; register r12
  152 0000014C F05F 3CAA       MOVS             R12, #0xAAAAAAAA
  153 00000150 F1BC 3FAA       CMP              R12, #0xAAAAAAAA
  154 00000154 F47F AFFE       BNE              selftest_fail_handle
  155 00000158 F05F 3C55       MOVS             R12, #0x55555555
  156 0000015C F1BC 3F55       CMP              R12, #0x55555555
  157 00000160 F47F AFFE       BNE              selftest_fail_handle
  158 00000164 F05F 0C0C       MOVS             R12, #0x0C  ; for ramp test
  159 00000168         
  160 00000168         ; ramp pattern verification
  161 00000168 2800            CMP              R0, #0x00
  162 0000016A F47F AFFE       BNE              selftest_fail_handle
  163 0000016E 2901            CMP              R1, #0x01
  164 00000170 F47F AFFE       BNE              selftest_fail_handle
  165 00000174 2A02            CMP              R2, #0x02
  166 00000176 F47F AFFE       BNE              selftest_fail_handle
  167 0000017A 2B03            CMP              R3, #0x03



ARM Macro Assembler    Page 4 


  168 0000017C F47F AFFE       BNE              selftest_fail_handle
  169 00000180 2C04            CMP              R4, #0x04
  170 00000182 F47F AFFE       BNE              selftest_fail_handle
  171 00000186 2D05            CMP              R5, #0x05
  172 00000188 F47F AFFE       BNE              selftest_fail_handle
  173 0000018C 2E06            CMP              R6, #0x06
  174 0000018E F47F AFFE       BNE              selftest_fail_handle
  175 00000192 2F07            CMP              R7, #0x07
  176 00000194 F47F AFFE       BNE              selftest_fail_handle
  177 00000198 F1B8 0F08       CMP              R8, #0x08
  178 0000019C F47F AFFE       BNE              selftest_fail_handle
  179 000001A0 F1B9 0F09       CMP              R9, #0x09
  180 000001A4 F47F AFFE       BNE              selftest_fail_handle
  181 000001A8 F1BA 0F0A       CMP              R10, #0x0A
  182 000001AC F47F AFFE       BNE              selftest_fail_handle
  183 000001B0 F1BB 0F0B       CMP              R11, #0x0B
  184 000001B4 F47F AFFE       BNE              selftest_fail_handle
  185 000001B8 F1BC 0F0C       CMP              R12, #0x0C
  186 000001BC F47F AFFE       BNE              selftest_fail_handle
  187 000001C0         
  188 000001C0 E8BD 0FF0       LDMIA            SP!, {R4, R5, R6, R7, R8, R9, R
10, R11}
  189 000001C4         
  190 000001C4         ; control flow test (exit point)
  191 000001C4 4803            LDR              R0,=ctrl_flow_cnt_inv
  192 000001C6 6801            LDR              R1,[R0]
  193 000001C8 1EC9            SUBS             R1,R1,#0x3  ; ctrl_flow_cnt_inv
                                                             -= OxO3
  194 000001CA 6001            STR              R1,[R0]
  195 000001CC         
  196 000001CC 2001            MOVS             R0, #0x1    ; cpu_test_success
  197 000001CE 4770            BX               LR          ; return to the cal
                                                            ler
  198 000001D0                 ENDP                         ; routine (unrecove
                                                            rable fault)
  199 000001D0         
  200 000001D0                 END
              00000000 
              00000000 
Command Line: --debug --xref --diag_suppress=9931 --cpu=Cortex-M4.fp.sp --apcs=
interwork --depend=.\objects\at32_selftest_cpurun_keil.d -o.\objects\at32_selft
est_cpurun_keil.o -ID:\keil5\ARM\Pack\ArteryTek\AT32F403A_407_DFP\2.1.6\Device\
Include --predefine="__UVISION_VERSION SETA 538" --predefine="AT32F403AVGT7 SET
A 1" --list=.\listings\at32_selftest_cpurun_keil.lst at32_selftest_cpurun_keil.
s



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

.text 00000000

Symbol: .text
   Definitions
      At line 14 in file at32_selftest_cpurun_keil.s
   Uses
      None
Comment: .text unused
selftest_cpu_runtime_test 00000000

Symbol: selftest_cpu_runtime_test
   Definitions
      At line 31 in file at32_selftest_cpurun_keil.s
   Uses
      At line 32 in file at32_selftest_cpurun_keil.s
Comment: selftest_cpu_runtime_test used once
2 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
External symbols

ctrl_flow_cnt 00000000

Symbol: ctrl_flow_cnt
   Definitions
      At line 21 in file at32_selftest_cpurun_keil.s
   Uses
      At line 44 in file at32_selftest_cpurun_keil.s
Comment: ctrl_flow_cnt used once
ctrl_flow_cnt_inv 00000000

Symbol: ctrl_flow_cnt_inv
   Definitions
      At line 22 in file at32_selftest_cpurun_keil.s
   Uses
      At line 191 in file at32_selftest_cpurun_keil.s
Comment: ctrl_flow_cnt_inv used once
selftest_fail_handle 00000000

Symbol: selftest_fail_handle
   Definitions
      At line 18 in file at32_selftest_cpurun_keil.s
   Uses
      At line 38 in file at32_selftest_cpurun_keil.s
      At line 41 in file at32_selftest_cpurun_keil.s
      At line 55 in file at32_selftest_cpurun_keil.s
      At line 58 in file at32_selftest_cpurun_keil.s
      At line 64 in file at32_selftest_cpurun_keil.s
      At line 67 in file at32_selftest_cpurun_keil.s
      At line 73 in file at32_selftest_cpurun_keil.s
      At line 76 in file at32_selftest_cpurun_keil.s
      At line 82 in file at32_selftest_cpurun_keil.s
      At line 85 in file at32_selftest_cpurun_keil.s
      At line 91 in file at32_selftest_cpurun_keil.s
      At line 94 in file at32_selftest_cpurun_keil.s
      At line 100 in file at32_selftest_cpurun_keil.s
      At line 103 in file at32_selftest_cpurun_keil.s
      At line 109 in file at32_selftest_cpurun_keil.s
      At line 112 in file at32_selftest_cpurun_keil.s
      At line 118 in file at32_selftest_cpurun_keil.s
      At line 121 in file at32_selftest_cpurun_keil.s
      At line 127 in file at32_selftest_cpurun_keil.s
      At line 130 in file at32_selftest_cpurun_keil.s
      At line 136 in file at32_selftest_cpurun_keil.s
      At line 139 in file at32_selftest_cpurun_keil.s
      At line 145 in file at32_selftest_cpurun_keil.s
      At line 148 in file at32_selftest_cpurun_keil.s
      At line 154 in file at32_selftest_cpurun_keil.s
      At line 157 in file at32_selftest_cpurun_keil.s
      At line 162 in file at32_selftest_cpurun_keil.s
      At line 164 in file at32_selftest_cpurun_keil.s
      At line 166 in file at32_selftest_cpurun_keil.s
      At line 168 in file at32_selftest_cpurun_keil.s
      At line 170 in file at32_selftest_cpurun_keil.s
      At line 172 in file at32_selftest_cpurun_keil.s
      At line 174 in file at32_selftest_cpurun_keil.s
      At line 176 in file at32_selftest_cpurun_keil.s
      At line 178 in file at32_selftest_cpurun_keil.s
      At line 180 in file at32_selftest_cpurun_keil.s
      At line 182 in file at32_selftest_cpurun_keil.s



ARM Macro Assembler    Page 2 Alphabetic symbol ordering
External symbols

      At line 184 in file at32_selftest_cpurun_keil.s
      At line 186 in file at32_selftest_cpurun_keil.s

3 symbols
340 symbols in table
