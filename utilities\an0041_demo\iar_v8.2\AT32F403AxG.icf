/*###ICF### Section handled by ICF editor, don't touch! ****/
/*-Editor annotation file-*/
/* IcfEditorFile="$TOOLKIT_DIR$\config\ide\IcfEditor\cortex_v1_0.xml" */
/*-Specials-*/
define symbol __ICFEDIT_intvec_start__ = 0x08000000;
/*-Memory Regions-*/
define symbol __ICFEDIT_region_ROM_start__ = 0x08000000;
define symbol __ICFEDIT_region_ROM_end__   = 0x08006003;
define symbol __ICFEDIT_region_RAM_start__ = 0x20000000;
define symbol __ICFEDIT_region_RAM_end__   = 0x20017FFF;
/* leave gaps at begin and and of class B region due to run time test overlap */
define symbol __ICFEDIT_region_CLASSB_start__   = 0x20000030;
define symbol __ICFEDIT_region_CLASSB_midd__    = 0x20000054;
define symbol __ICFEDIT_region_CLASSB_end__     = 0x20000078;
define symbol __ICFEDIT_region_user_RAM_start__ = 0x20000080;
/*-Sizes-*/
define symbol __ICFEDIT_size_cstack__ = 0x1000;
define symbol __ICFEDIT_size_heap__   = 0x1000;
define symbol __ICFEDIT_size_stack_bottom__ = 0x10;
/**** End of ICF editor section. ###ICF###*/

export symbol __ICFEDIT_region_ROM_start__;
export symbol __ICFEDIT_region_ROM_end__;
export symbol __ICFEDIT_region_RAM_start__;
export symbol __ICFEDIT_region_RAM_end__;
export symbol __ICFEDIT_region_CLASSB_start__;
export symbol __ICFEDIT_region_CLASSB_end__;
define memory mem with size = 4G;
define region ROM_region   = mem:[from __ICFEDIT_region_ROM_start__   to __ICFEDIT_region_ROM_end__];
define region RAM_region   = mem:[from __ICFEDIT_region_RAM_start__   to __ICFEDIT_region_RAM_end__];

// regions definition ######################
define region RUN_TIME_RAM_BUF_region = mem:[from 0x20000000 to 0x2000001F];
define region RUN_TIME_RAM_PNT_region = mem:[from 0x20000020 to 0x20000028];

define region CLASS_B_RAM_region = mem:[from __ICFEDIT_region_CLASSB_start__ to __ICFEDIT_region_CLASSB_end__];
define region CLASS_B_RAM_REV_region = mem:[from __ICFEDIT_region_CLASSB_midd__ to __ICFEDIT_region_CLASSB_end__];

// blocks definition #######################

define block CSTACK    with alignment = 8, size = __ICFEDIT_size_cstack__   { };
define block HEAP      with alignment = 8, size = __ICFEDIT_size_heap__     { };

define block STACK_BOTTOM_B  with alignment = 8, size = __ICFEDIT_size_stack_bottom__  { section STACK_BOTTOM };
define block INTVEC{readonly section .intvec};                     
define block stack_order with fixed order { block STACK_BOTTOM_B, block CSTACK, block HEAP };

// initialization setting #################
initialize by copy { readwrite };
do not initialize  { section .noinit, section STACK_BOTTOM, section RUNTIME_RAM_BUF, section RUNTIME_RAM_POINTER,
                     section CLASS_B_RAM, section CLASS_B_RAM_REV};

// placements #############################
place in CLASS_B_RAM_region
                      { readwrite data section CLASS_B_RAM  };
                      
place in CLASS_B_RAM_REV_region
                      { readwrite data section CLASS_B_RAM_REV  };

place in RUN_TIME_RAM_BUF_region
                      { readwrite data section RUNTIME_RAM_BUF  };
                      
place in RUN_TIME_RAM_PNT_region
                      { readwrite data section RUNTIME_RAM_POINTER  };             


place in ROM_region   { first  block INTVEC, readonly };
place at end of ROM_region { ro section .checksum };
place in RAM_region   { readwrite }; 
place at end of RAM_region  { block stack_order };