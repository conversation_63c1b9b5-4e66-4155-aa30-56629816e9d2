/**
  **************************************************************************
  * @file     main.h
  * @version  v2.0.0
  * @date     2021-12-31
  * @brief    header for main.c module
  **************************************************************************
  */

#ifndef __MAIN_H
#define __MAIN_H

#include "at32f403a_407_board.h"

#define USARTx                           USART1

#define CLKSWITCH_TIMEOUT_VALUE          5000   /* 5 s    */
#define HEXT_TIMEOUT_VALUE               10000
#define HSI_TIMEOUT_VALUE                3      /* 3 ms */
#define LICK_TIMEOUT_VALUE               3      /* 3 ms */
#define PLL_TIMEOUT_VALUE                3      /* 3 ms */                                       

void selftest_system_clock_config(void);
void usart_reconfigure(void);
void selftest_clock_cross_measurement_config(void);
void systick_inc(void);
uint32_t systick_get(void);
#ifdef __IAR_SYSTEMS_ICC__  /* iar compiler */
void __iar_data_init3(void);
#endif
#ifdef __CC_ARM             /* keil compiler */
extern void $Super$$main(void);  
#endif

#endif
