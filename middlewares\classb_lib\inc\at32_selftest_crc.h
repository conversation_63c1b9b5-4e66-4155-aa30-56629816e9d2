/**
  **************************************************************************
  * @file     at32_selftest_crc.h
  * @version  v2.0.0
  * @date     2021-12-31
  * @brief    at32 selftest crc header file
  **************************************************************************
  */

#ifndef __AT32_SELFTEST_CRC_H
#define __AT32_SELFTEST_CRC_H

typedef enum 
{
  CRC_TEST_ERROR = 0,
  CRC_TEST_SUCCESS = 1,
  CRC_TEST_CONTINUE
} crc_status_type;

crc_status_type selftest_crc_runtime_test(void);

#endif
