/**
  **************************************************************************
  * @file     at32_selftest_lib.h
  * @version  v2.0.0
  * @date     2021-12-31
  * @brief    references all header files of the self test library
  **************************************************************************
  */

#ifndef __AT32_SELFTEST_LIB_H
#define __AT32_SELFTEST_LIB_H

#include "at32_selftest_param.h"
#include "at32_selftest_classbvar.h"
#include "at32_selftest_startup.h"
#include "at32_selftest_runtime.h"
#include "at32_selftest_cpu.h"
#include "at32_selftest_clock.h"
#include "at32_selftest_crc.h"
#include "at32_selftest_ram.h"

#endif
