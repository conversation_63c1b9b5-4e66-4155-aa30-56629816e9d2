/**
  **************************************************************************
  * @file     at32_selftest_clock_startup.c
  * @version  v2.0.0
  * @date     2021-12-31
  * @brief    contains all the functions for the clock self-test in startup
  *           phase
  **************************************************************************  
  */

#include "main.h"
#include "at32_selftest_lib.h"

/**
  * @brief  clock self-test in startup phase
  * @param  none
  * @retval clock test status
  */
clock_status_type selftest_clock_startup_test(void)
{
  uint32_t tick_val;
  clock_status_type result = CLOCK_TEST_SUCCESS; 
 
  control_flow_call(CLOCK_TEST_CALLEE);

  /* enable lick */
  crm_clock_source_enable(CRM_CLOCK_SOURCE_LICK, TRUE);

  tick_val = systick_get();
 
  while(crm_flag_get(CRM_LICK_STABLE_FLAG) != SET)
  {
    if((systick_get() - tick_val ) > LICK_TIMEOUT_VALUE)
    {
      result = CLOCK_TEST_ERROR;
      break;
    }
  }
  
#ifdef HEXT_CLOCK_USED  
  crm_clock_source_enable(CRM_CLOCK_SOURCE_HEXT, TRUE);
  tick_val = systick_get();
  while(crm_flag_get(CRM_HEXT_STABLE_FLAG) != SET)
  {
    if((systick_get() - tick_val) > HEXT_TIMEOUT_VALUE)
    {
      result = CLOCK_TEST_ERROR;
      break;
    }
  }
  
  crm_clock_failure_detection_enable(TRUE);
  /* select hext as system clock source */
  crm_sysclk_switch(CRM_SCLK_HEXT);

  tick_val = systick_get();
  while(crm_sysclk_switch_status_get() != CRM_SCLK_HEXT)
  {
    if((systick_get() - tick_val) > CLKSWITCH_TIMEOUT_VALUE)
    {
      result = CLOCK_TEST_ERROR;
      break;
    }
  }

  /* start reference measurement */
  /* configure dedicated timer to measure lick period */
  selftest_clock_cross_measurement_config();

  /* wait for two subsequent lick periods measurements */
  lick_period_flag = 0;
  while(lick_period_flag == 0);
  lick_period_flag = 0;
  while(lick_period_flag == 0);

  /* hext measurement check */
  if((period_val < FREQ_LIMIT_LOW(HEXT_VALUE)) || (period_val > FREQ_LIMIT_HIGH(HEXT_VALUE)))/* above or below expected */
  {
    result = CLOCK_TEST_ERROR;
  }
  
#else

  /* select hick as system clock source */
  crm_sysclk_switch(CRM_SCLK_HICK);

  tick_val = systick_get();
  while(crm_sysclk_switch_status_get() != CRM_SCLK_HICK)
  {
    if((systick_get() - tick_val) > CLKSWITCH_TIMEOUT_VALUE)
    {
      result = CLOCK_TEST_ERROR;
      break;
    }
  }

  /* configure dedicated timer to measure lick period */
  selftest_clock_cross_measurement_config();

  /* wait for two subsequent lick periods measurements */
  lick_period_flag = 0;
  while (lick_period_flag == 0);
  lick_period_flag = 0;
  while (lick_period_flag == 0);

  /* hick measurement check */
  if((period_val < FREQ_LIMIT_LOW(HICK_VALUE)) || (period_val > FREQ_LIMIT_HIGH(HICK_VALUE))) /* above or below expected */
  {
    result = CLOCK_TEST_ERROR;
  }
#endif
  
  /* switch back to hick, used as system clock*/
  crm_sysclk_switch(CRM_SCLK_HICK);
  
  tick_val = systick_get();
  while(crm_sysclk_switch_status_get() != CRM_SCLK_HICK)
  {
    if((systick_get() - tick_val) > CLKSWITCH_TIMEOUT_VALUE)
    {
      result = CLOCK_TEST_ERROR;
      break;
    }
  }

  control_flow_resume(CLOCK_TEST_CALLEE);
  return result;
}

/**
  * @brief  clock self-test in run time phase
  * @param  none
  * @retval clock test status
  */  
clock_status_type selftest_clock_runtime_test(void)
{
  clock_status_type result = CLOCK_TEST_SUCCESS;
  uint32_t temp_period_val;
  uint32_t temp_lick_period_flag;
  
  control_flow_call(CLOCK_PERIOD_TEST_CALLEE);

  if(lick_period_flag == 0xAAAAAAAA)
  {
    temp_period_val = ~period_val_inv;
    temp_lick_period_flag = ~lick_period_flag_inv;
    if((period_val == temp_period_val) && (lick_period_flag == temp_lick_period_flag))
    {
      if((period_val < FREQ_LIMIT_LOW(SYSCLK_RUNTIME_FREQ)) || (period_val > FREQ_LIMIT_HIGH(SYSCLK_RUNTIME_FREQ))) /* above or below expected */
      {
        /* switch back to internal clock */
        crm_sysclk_switch(CRM_SCLK_HICK);
        result = CLOCK_TEST_ERROR; 
      }
      else
      {       
        lick_period_flag = 0;
      }
    }
    else /* control flow error */
    {
      result = CLOCK_TEST_ERROR;
    }
  }
  
  control_flow_resume(CLOCK_PERIOD_TEST_CALLEE);
  return result;
}
