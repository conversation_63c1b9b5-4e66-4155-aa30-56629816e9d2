/**
  **************************************************************************
  * @file     main.c
  * @version  v2.0.0
  * @date     2021-12-31
  * @brief    main program
  **************************************************************************
  *                       Copyright notice & Disclaimer
  *
  * The software Board Support Package (BSP) that is made available to 
  * download from Artery official website is the copyrighted work of Artery. 
  * Artery authorizes customers to use, copy, and distribute the BSP 
  * software and its related documentation for the purpose of design and 
  * development in conjunction with Artery microcontrollers. Use of the 
  * software is governed by this copyright notice and the following disclaimer.
  *
  * THIS SOFTWARE IS PROVIDED ON "AS IS" BASIS WITHOUT WARRANTIES,
  * GUARANTEES OR R<PERSON>RESENTATIONS OF ANY KIND. ARTERY EXPRESSLY DISCLAIMS,
  * TO THE FULLEST EXTENT PERMITTED BY LAW, ALL EXPRESS, IMPLIED OR
  * STATUTORY OR OTHER WARRANTIES, GUARANTEES OR REPRESENTATIONS,
  * INCLUDING BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY,
  * FITNESS FOR A PARTICULAR PURPOSE, OR NON-INFRINGEMENT.
  *
  **************************************************************************
  */

#include "at32f403a_407_board.h"
#include "at32f403a_407_clock.h"
#include "main.h"
#include "at32_selftest_param.h"
#include "at32_selftest_lib.h"

__IO uint32_t tick_cnt_val;

void systick_inc(void)
{
  tick_cnt_val++;
}

uint32_t systick_get(void)
{
  return tick_cnt_val;
}

void usart_reconfigure(void)
{
  usart_init(PRINT_UART, 115200, USART_DATA_8BITS, USART_STOP_1_BIT);
  usart_transmitter_enable(PRINT_UART, TRUE);
  usart_enable(PRINT_UART, TRUE);
}

/**
  * @brief  configure timer to measure lick period
  * @param  none
  * @retval none
  */
void selftest_clock_cross_measurement_config(void)
{
  tmr_input_config_type tmr_input_struct;
 
  /* tmrx peripheral clock enable */
  crm_periph_clock_enable(CRM_TMR5_PERIPH_CLOCK, TRUE);

  nvic_irq_enable(TMR5_GLOBAL_IRQn, 4, 0);
 
  /* tmrx init configuration */
  tmr_base_init(TMR5, 0xFFFF, 8);
  tmr_cnt_dir_set(TMR5, TMR_COUNT_UP);
  tmr_clock_source_div_set(TMR5, TMR_CLOCK_DIV1);
  tmr_repetition_counter_set(TMR5, 0);

  /* connect internally the tmr5_ch4 input capture to the lick clock output */
  crm_periph_clock_enable(CRM_IOMUX_PERIPH_CLOCK, TRUE);
  gpio_pin_remap_config(TMR5CH4_GMUX, TRUE);
  
  /* configure the tmr5 input capture of channel 4 */
  tmr_input_default_para_init(&tmr_input_struct);
  tmr_input_struct.input_channel_select = TMR_SELECT_CHANNEL_4;
  tmr_input_struct.input_polarity_select = TMR_INPUT_RISING_EDGE;
  tmr_input_struct.input_mapped_select = TMR_CC_CHANNEL_MAPPED_DIRECT;
  tmr_input_struct.input_filter_value = 0x00;
  tmr_input_channel_init(TMR5, &tmr_input_struct, TMR_CHANNEL_INPUT_DIV_8);

  /* reset the flags */
  lick_period_flag = 0;
  
  /* start the tmr input capture measurement in interrupt mode */
  tmr_interrupt_enable(TMR5, TMR_C4_INT, TRUE);
  
  tmr_counter_enable(TMR5, TRUE);
}

void selftest_system_clock_config(void)
{
  uint32_t wait_counter; 

#ifdef HEXT_CLOCK_USED
 
  /* reset crm */
  crm_reset();

  crm_clock_source_enable(CRM_CLOCK_SOURCE_HEXT, TRUE);

   /* wait till hext is ready */
  if(crm_hext_stable_wait() == ERROR)
  {
    /* If HSE fails to start-up, the application will have wrong clock
         configuration. User can add here some code to deal with this error */
    STARTUP_PRINTF("PLL hse config failure\r\n");
    selftest_fail_handle();   
  }

  /* config pll clock resource */
  crm_pll_config(CRM_PLL_SOURCE_HEXT_DIV, CRM_PLL_MULT_60, CRM_PLL_OUTPUT_RANGE_GT72MHZ);

  /* config hext division */
  crm_hext_clock_div_set(CRM_HEXT_DIV_2);

  /* enable pll */
  crm_clock_source_enable(CRM_CLOCK_SOURCE_PLL, TRUE);

  /* wait till pll is ready */
  wait_counter = 0;
  while(crm_flag_get(CRM_PLL_STABLE_FLAG) != SET)
  {
    if(wait_counter++ > PLL_STABLE_TIMEOUT)
      break;
  }
  if(crm_flag_get(CRM_PLL_STABLE_FLAG) != SET)
  {
    /* If HSE fails to start-up, the application will have wrong clock
         configuration. User can add here some code to deal with this error */
    STARTUP_PRINTF("PLL hse config failure\r\n");
    selftest_fail_handle();   
  }

  /* config ahbclk */
  crm_ahb_div_set(CRM_AHB_DIV_1);

  /* config apb2clk */
  crm_apb2_div_set(CRM_APB2_DIV_2);

  /* config apb1clk */
  crm_apb1_div_set(CRM_APB1_DIV_2);

  /* enable auto step mode */
  crm_auto_step_mode_enable(TRUE);

  /* select pll as system clock source */
  crm_sysclk_switch(CRM_SCLK_PLL);

  /* wait till pll is used as system clock source */
  wait_counter = 0;
  while(crm_sysclk_switch_status_get() != CRM_SCLK_PLL)
  {
    if(wait_counter++ > CLOCK_SWITCH_TIMEOUT)
      break;
  }
  if(crm_sysclk_switch_status_get() != CRM_SCLK_PLL)
  {
    /* If HSE fails to start-up, the application will have wrong clock
         configuration. User can add here some code to deal with this error */
    STARTUP_PRINTF("PLL hse config failure\r\n");
    selftest_fail_handle();   
  }
  
  /* disable auto step mode */
  crm_auto_step_mode_enable(FALSE);

  /* update system_core_clock global variable */
  system_core_clock_update();

#else
   
  /* reset crm */
  crm_reset();

  crm_clock_source_enable(CRM_CLOCK_SOURCE_HICK, TRUE);

   /* wait till hick is ready */
  wait_counter = 0;
  while(crm_flag_get(CRM_HICK_STABLE_FLAG) != SET)
  {
    if(wait_counter++ > HICK_STABLE_TIMEOUT)
      break;
  }
  if(crm_flag_get(CRM_HICK_STABLE_FLAG) != SET)
  {
    /* If hick fails to start-up, the application will have wrong clock
         configuration. User can add here some code to deal with this error */
    STARTUP_PRINTF("PLL hick config failure\r\n");
    selftest_fail_handle();   
  }

  /* config pll clock resource */
  crm_pll_config(CRM_PLL_SOURCE_HICK, CRM_PLL_MULT_60, CRM_PLL_OUTPUT_RANGE_GT72MHZ);

  /* enable pll */
  crm_clock_source_enable(CRM_CLOCK_SOURCE_PLL, TRUE);

  /* wait till pll is ready */
  wait_counter = 0;
  while(crm_flag_get(CRM_PLL_STABLE_FLAG) != SET)
  {
    if(wait_counter++ > PLL_STABLE_TIMEOUT)
      break;
  }
  if(crm_flag_get(CRM_PLL_STABLE_FLAG) != SET)
  {
    /* If HSE fails to start-up, the application will have wrong clock
         configuration. User can add here some code to deal with this error */
    STARTUP_PRINTF("PLL hick config failure\r\n");
    selftest_fail_handle();   
  }

  /* config ahbclk */
  crm_ahb_div_set(CRM_AHB_DIV_1);

  /* config apb2clk */
  crm_apb2_div_set(CRM_APB2_DIV_2);

  /* config apb1clk */
  crm_apb1_div_set(CRM_APB1_DIV_2);

  /* enable auto step mode */
  crm_auto_step_mode_enable(TRUE);

  /* select pll as system clock source */
  crm_sysclk_switch(CRM_SCLK_PLL);

  /* wait till pll is used as system clock source */
  while(crm_sysclk_switch_status_get() != CRM_SCLK_PLL)
  {
    if(wait_counter++ > CLOCK_SWITCH_TIMEOUT)
      break;
  }
  if(crm_sysclk_switch_status_get() != CRM_SCLK_PLL)
  {
    /* If HSE fails to start-up, the application will have wrong clock
         configuration. User can add here some code to deal with this error */
    STARTUP_PRINTF("PLL hick config failure\r\n");
    selftest_fail_handle();   
  }
  
  /* disable auto step mode */
  crm_auto_step_mode_enable(FALSE);

  /* update system_core_clock global variable */
  system_core_clock_update();
#endif  
}

/**
  * @brief  main function.
  * @param  none
  * @retval none
  */
int main(void)
{
  nvic_priority_group_config(NVIC_PRIORITY_GROUP_4);
  selftest_system_clock_config();

  #ifdef DEBUG_MESSAGE_RUNTIME
  uart_print_init(115200);
  #endif
  RUNTIME_PRINTF("\r\n******************* self-test in runtime phase *******************\r\n");
  selftest_runtime_init();
  while(1)
  {
    selftest_runtime_check();
  }
}
