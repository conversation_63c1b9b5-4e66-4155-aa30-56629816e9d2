/**
  **************************************************************************
  * @file     at32_selftest_ram.c
  * @version  v2.0.0
  * @date     2021-12-31
  * @brief    contains self-test ram test in runtime phase.
  **************************************************************************
  */
  
#include "main.h"
#include "at32_selftest_param.h"
#include "at32_selftest_lib.h"

/**
  * @brief  sampling ram test using the march c- or march x algorithm.
  * @param  none
  * @retval test status result
  */
ram_status_type selftest_sampling_ram_test(void)
{
  ram_status_type result = RAM_TEST_CONTINUE;
  /* check class b var integrity */
  if((uint32_t)p_runtime_ram_chk == ~(uint32_t)p_runtime_ram_chk_inv)
  {
    if(p_runtime_ram_chk >= (uint32_t *)CLASS_B_END_ADDR)
    {
      if(selftest_ram_step_implement(&runtime_ram_buf[0], &runtime_ram_buf[0], BACKGROUND_PATTERN) == RAM_TEST_SUCCESS)         
      {
        /* all the ram test is completed successfully */
        result = RAM_TEST_SUCCESS;
      }
      else
      {
        result = RAM_TEST_ERROR;
      }
      p_runtime_ram_chk = (uint32_t *)CLASS_B_START_ADDR;
      p_runtime_ram_chk_inv = ((uint32_t *)~(CLASS_B_START_ADDR));
    }
    else
    {
      if(selftest_ram_step_implement(p_runtime_ram_chk, &runtime_ram_buf[1], BACKGROUND_PATTERN) == RAM_TEST_SUCCESS)         
      {
        p_runtime_ram_chk += RUNTIME_RAM_BLOCK_SIZE - (2 * RUNTIME_RAM_BLOCK_OVERLAP);
        p_runtime_ram_chk_inv = (uint32_t *)(uint32_t)(~(uint32_t)p_runtime_ram_chk);
      }
      else
      {
        result = RAM_TEST_ERROR;
      }
    }
  }
  else
  {
    result = RAM_TEST_ERROR;
  }
  return result;
}
