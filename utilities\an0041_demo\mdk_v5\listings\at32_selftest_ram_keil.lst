


ARM Macro Assembler    Page 1 


    1 00000000         ;/**
    2 00000000         ;  *****************************************************
                       *********************
    3 00000000         ;  * @file     at32_selftest_ram_keil.s
    4 00000000         ;  * @version  v2.0.0
    5 00000000         ;  * @date     2021-12-31
    6 00000000         ;  * @brief    contains procedures written in assembler 
                       for full and partial 
    7 00000000         ;  *           marching ram tests to be called during st
                       artup and runtime.
    8 00000000         ;  *****************************************************
                       *********************
    9 00000000         ;  */
   10 00000000         
   11 00000000                 THUMB
   12 00000000         ;  REQUIRES
   13 00000000                 PRESERVE8
   14 00000000         
   15 00000000                 AREA             |.text|, CODE, READONLY, ALIGN=
2
   16 00000000         
   17 00000000         ; tables with offsets of physical order of address in ra
                       m
   18 00000000         __STANDARD_RAM_ORDER
   19 00000000 FFFFFFFC        DCD              -4
   20 00000004 00000000        DCD              0
   21 00000008 00000004        DCD              4
   22 0000000C 00000008        DCD              8
   23 00000010 0000000C        DCD              12
   24 00000014 00000010        DCD              16
   25 00000018 00000014        DCD              20
   26 0000001C 00000018        DCD              24
   27 00000020 0000001C        DCD              28
   28 00000024         __ARTISAN_RAM_ORDER
   29 00000024 FFFFFFF8        DCD              -8
   30 00000028 00000000        DCD              0
   31 0000002C 00000004        DCD              4
   32 00000030 0000000C        DCD              12
   33 00000034 00000008        DCD              8
   34 00000038 00000010        DCD              16
   35 0000003C 00000014        DCD              20
   36 00000040 0000001C        DCD              28
   37 00000044 00000018        DCD              24
   38 00000048         
   39 00000048                 EXTERN           isr_ctrl_flow_cnt
   40 00000048                 EXTERN           isr_ctrl_flow_cnt_inv
   41 00000048         
   42 00000048                 EXPORT           selftest_full_ram_test
   43 00000048                 EXPORT           selftest_ram_step_implement
   44 00000048         
   45 00000048         ;/**
   46 00000048         ;  * @brief  full ram marchc test for startup
   47 00000048         ;  * @note   all the ram area including stack is destroy
                       ed during this test
   48 00000048         ;  * @param  
   49 00000048         ;  *         input: r0 .. ram begin (first address to ch
                       eck), 
   50 00000048         ;  *                r1 .. ram end (last address to check
                       )



ARM Macro Assembler    Page 2 


   51 00000048         ;  *                r2 .. background pattern
   52 00000048         ;  *         local: r3 .. inverted background pattern
   53 00000048         ;  *                r4 .. keeps test result status
   54 00000048         ;  *                r5 .. pointer to ram
   55 00000048         ;  *                r6 .. content ram to compare  
   56 00000048         ;  * @retval test successfull (=1)
   57 00000048         ;  */
   58 00000048         selftest_full_ram_test
   59 00000048 2401            MOVS             R4, #0x1    ; test success stat
                                                            us by default
   60 0000004A         
   61 0000004A 0013            MOVS             R3,R2       ; setup inverted ba
                                                            ckground pattern
   62 0000004C 425B            RSBS             R3, R3, #0
   63 0000004E 1E5B            SUBS             R3,R3, #1
   64 00000050         
   65 00000050         ; *** step 1 *** 
   66 00000050         ; write background pattern with addresses increasing
   67 00000050 0005            MOVS             R5,R0
   68 00000052         __FULL1_LOOP
   69 00000052 428D            CMP              R5,R1
   70 00000054 D802            BHI              __FULLSTEP_2
   71 00000056 602A            STR              R2,[R5, #+0]
   72 00000058 1D2D            ADDS             R5,R5,#+4
   73 0000005A E7FA            B                __FULL1_LOOP
   74 0000005C         
   75 0000005C         ; *** step 2 ***
   76 0000005C         ; verify background and write inverted background with a
                       ddresses increasing
   77 0000005C         __FULLSTEP_2
   78 0000005C 0005            MOVS             R5,R0
   79 0000005E         __FULL2_LOOP
   80 0000005E 428D            CMP              R5,R1
   81 00000060 D811            BHI              __FULLSTEP_3
   82 00000062 682E            LDR              R6,[R5,#+0]
   83 00000064 4296            CMP              R6,R2
   84 00000066 D157            BNE              __FULL_ERR
   85 00000068 602B            STR              R3,[R5,#+0]
   86 0000006A 686E            LDR              R6,[R5,#+4]
   87 0000006C 4296            CMP              R6,R2
   88 0000006E D153            BNE              __FULL_ERR
   89 00000070 606B            STR              R3,[R5,#+4]
   90 00000072                 IF               :DEF:ARTISAN
  100 00000072 68AE            LDR              R6,[R5,#+8]
  101 00000074 4296            CMP              R6,R2
  102 00000076 D14F            BNE              __FULL_ERR
  103 00000078 60AB            STR              R3,[R5,#+8]
  104 0000007A 68EE            LDR              R6,[R5,#+12]
  105 0000007C 4296            CMP              R6,R2
  106 0000007E D14B            BNE              __FULL_ERR
  107 00000080 60EB            STR              R3,[R5,#+12]
  108 00000082                 ENDIF
  109 00000082 3510            ADDS             R5,R5,#+16
  110 00000084 E7EB            B                __FULL2_LOOP
  111 00000086         
  112 00000086         ; *** step 3 ***
  113 00000086         ; verify inverted background and write background with a
                       ddresses increasing  
  114 00000086         __FULLSTEP_3



ARM Macro Assembler    Page 3 


  115 00000086 0005            MOVS             R5,R0
  116 00000088         __FULL3_LOOP
  117 00000088 428D            CMP              R5,R1
  118 0000008A D811            BHI              __FULLSTEP_4
  119 0000008C 682E            LDR              R6,[R5,#+0]
  120 0000008E 429E            CMP              R6,R3
  121 00000090 D142            BNE              __FULL_ERR
  122 00000092 602A            STR              R2,[R5,#+0]
  123 00000094 686E            LDR              R6,[R5,#+4]
  124 00000096 429E            CMP              R6,R3
  125 00000098 D13E            BNE              __FULL_ERR
  126 0000009A 606A            STR              R2,[R5,#+4]
  127 0000009C                 IF               :DEF:ARTISAN
  137 0000009C 68AE            LDR              R6,[R5,#+8]
  138 0000009E 429E            CMP              R6,R3
  139 000000A0 D13A            BNE              __FULL_ERR
  140 000000A2 60AA            STR              R2,[R5,#+8]
  141 000000A4 68EE            LDR              R6,[R5,#+12]
  142 000000A6 429E            CMP              R6,R3
  143 000000A8 D136            BNE              __FULL_ERR
  144 000000AA 60EA            STR              R2,[R5,#+12]
  145 000000AC                 ENDIF
  146 000000AC 3510            ADDS             R5,R5,#+16
  147 000000AE E7EB            B                __FULL3_LOOP
  148 000000B0         
  149 000000B0         ; *** step 4 ***
  150 000000B0         ; verify background and write inverted background with a
                       ddresses decreasing  
  151 000000B0         __FULLSTEP_4
  152 000000B0 000D            MOVS             R5,R1
  153 000000B2 3D0F            SUBS             R5,R5,#+15
  154 000000B4         __FULL4_LOOP
  155 000000B4 4285            CMP              R5,R0
  156 000000B6 D311            BLO              __FULLSTEP_5
  157 000000B8                 IF               :DEF:ARTISAN
  167 000000B8 68EE            LDR              R6,[R5,#+12]
  168 000000BA 4296            CMP              R6,R2
  169 000000BC D12C            BNE              __FULL_ERR
  170 000000BE 60EB            STR              R3,[R5,#+12]
  171 000000C0 68AE            LDR              R6,[R5,#+8]
  172 000000C2 4296            CMP              R6,R2
  173 000000C4 D128            BNE              __FULL_ERR
  174 000000C6 60AB            STR              R3,[R5,#+8]
  175 000000C8                 ENDIF
  176 000000C8 686E            LDR              R6,[R5,#+4]
  177 000000CA 4296            CMP              R6,R2
  178 000000CC D124            BNE              __FULL_ERR
  179 000000CE 606B            STR              R3,[R5,#+4]
  180 000000D0 682E            LDR              R6,[R5,#+0]
  181 000000D2 4296            CMP              R6,R2
  182 000000D4 D120            BNE              __FULL_ERR
  183 000000D6 602B            STR              R3,[R5,#+0]
  184 000000D8 3D10            SUBS             R5,R5,#+16
  185 000000DA E7EB            B                __FULL4_LOOP
  186 000000DC         
  187 000000DC         ; *** step 5 ***
  188 000000DC         ; verify inverted background and write background with a
                       ddresses decreasing 
  189 000000DC         __FULLSTEP_5



ARM Macro Assembler    Page 4 


  190 000000DC 000D            MOVS             R5,R1
  191 000000DE 3D0F            SUBS             R5,R5,#+15
  192 000000E0         __FULL5_LOOP
  193 000000E0 4285            CMP              R5,R0
  194 000000E2 D311            BLO              __FULLSTEP_6
  195 000000E4                 IF               :DEF:ARTISAN
  205 000000E4 68EE            LDR              R6,[R5,#+12]
  206 000000E6 429E            CMP              R6,R3
  207 000000E8 D116            BNE              __FULL_ERR
  208 000000EA 60EA            STR              R2,[R5,#+12]
  209 000000EC 68AE            LDR              R6,[R5,#+8]
  210 000000EE 429E            CMP              R6,R3
  211 000000F0 D112            BNE              __FULL_ERR
  212 000000F2 60AA            STR              R2,[R5,#+8]
  213 000000F4                 ENDIF
  214 000000F4 686E            LDR              R6,[R5,#+4]
  215 000000F6 429E            CMP              R6,R3
  216 000000F8 D10E            BNE              __FULL_ERR
  217 000000FA 606A            STR              R2,[R5,#+4]
  218 000000FC 682E            LDR              R6,[R5,#+0]
  219 000000FE 429E            CMP              R6,R3
  220 00000100 D10A            BNE              __FULL_ERR
  221 00000102 602A            STR              R2,[R5,#+0]
  222 00000104 3D10            SUBS             R5,R5,#+16
  223 00000106 E7EB            B                __FULL5_LOOP
  224 00000108         
  225 00000108         ; *** step 6 ***
  226 00000108         ; verify background with addresses increasing
  227 00000108         __FULLSTEP_6
  228 00000108 0005            MOVS             R5,R0
  229 0000010A         __FULL6_LOOP
  230 0000010A 428D            CMP              R5,R1
  231 0000010C D805            BHI              __FULL_RET
  232 0000010E 682E            LDR              R6,[R5,#+0]
  233 00000110 4296            CMP              R6,R2
  234 00000112 D101            BNE              __FULL_ERR
  235 00000114 1D2D            ADDS             R5,R5,#+4
  236 00000116 E7F8            B                __FULL6_LOOP
  237 00000118         
  238 00000118         __FULL_ERR
  239 00000118 2400            MOVS             R4,#0       ; error result
  240 0000011A         
  241 0000011A         __FULL_RET
  242 0000011A 0020            MOVS             R0,R4
  243 0000011C 4770            BX               LR          ; return to the cal
                                                            ler
  244 0000011E         
  245 0000011E         ;/**
  246 0000011E         ;  * @brief  ram marchc-/march x test for runtime
  247 0000011E         ;  * @note   the ram area under test is out of original 
                       content during this test!
  248 0000011E         ;  *         neighbour addresses (first-1 or -2 and last
                       +1) are tested, too.
  249 0000011E         ;  * @param  
  250 0000011E         ;  *         input: r0 .. ram begin (first address to te
                       st), 
  251 0000011E         ;  *                r1 .. buffer begin (first address of
                        backup buffer)
  252 0000011E         ;  *                r2 .. background pattern



ARM Macro Assembler    Page 5 


  253 0000011E         ;  * @retval test successfull (=1)
  254 0000011E         ;  * compilation paramaters : ARTISAN - changes order of
                        the sequence of tested
  255 0000011E         ;  *                                    addresses to res
                       pect their physical order
  256 0000011E         ;  *                  USE_MARCHX_TEST - skip step 3 and 
                       4 of march c- to make the test
  257 0000011E         ;  *                                    shorter and fast
                       er overall
  258 0000011E         ;  */
  259 0000011E         selftest_ram_step_implement
  260 0000011E B4F0            PUSH             {R4-R7}
  261 00000120         
  262 00000120 4D48            LDR              R5,=isr_ctrl_flow_cnt ; control
                                                             flow control
  263 00000122 682E            LDR              R6,[R5]
  264 00000124 1CF6            ADDS             R6,R6,#3
  265 00000126 602E            STR              R6,[R5]
  266 00000128         
  267 00000128 0013            MOVS             R3,R2       ; setup inverted ba
                                                            ckground pattern (r
                                                            3)
  268 0000012A 425B            RSBS             R3, R3, #0
  269 0000012C 1E5B            SUBS             R3,R3, #1
  270 0000012E         
  271 0000012E                 IF               :DEF:ARTISAN
  274 0000012E 4C46            LDR              R4, =__STANDARD_RAM_ORDER
  275 00000130                 ENDIF
  276 00000130         
  277 00000130 0005            MOVS             R5,R0       ; backup buffer to 
                                                            be tested
  278 00000132 428D            CMP              R5,R1
  279 00000134 D046            BEQ              __BUFF_TEST
  280 00000136         
  281 00000136         ; ***************** test of the ram slice **************
                       *******
  282 00000136 2500            MOVS             R5, #0      ; no - save content
                                                             of the ram slice i
                                                            nto the backup buff
                                                            er
  283 00000138         __SAVE_LOOP
  284 00000138 5966            LDR              R6,[R4, R5] ; load data offset
  285 0000013A 5987            LDR              R7,[R0, R6] ; load data from ra
                                                            m
  286 0000013C F105 0504       ADD              R5,R5,#4    ; original data are
                                                             stored starting fr
                                                            om second item of t
                                                            he buffer
  287 00000140 514F            STR              R7,[R1, R5] ; (first and last i
                                                            tems are used for t
                                                            esting purpose excl
                                                            usively)
  288 00000142 2D14            CMP              R5, #20
  289 00000144 DDF8            BLE              __SAVE_LOOP
  290 00000146         
  291 00000146         ; *** step 1 *** 
  292 00000146         ; write background pattern with addresses increasing
  293 00000146 2500            MOVS             R5, #0
  294 00000148         __STEP1_LOOP



ARM Macro Assembler    Page 6 


  295 00000148 5966            LDR              R6,[R4, R5] ; load data offset
  296 0000014A 5182            STR              R2,[R0, R6] ; store background 
                                                            pattern
  297 0000014C F105 0504       ADD              R5,R5,#4
  298 00000150 2D14            CMP              R5, #20
  299 00000152 DDF9            BLE              __STEP1_LOOP
  300 00000154         
  301 00000154         ; *** step 2 ***
  302 00000154         ; verify background and write inverted background with a
                       ddresses increasing
  303 00000154 2500            MOVS             R5, #0
  304 00000156         __STEP2_LOOP
  305 00000156 5966            LDR              R6,[R4, R5] ; load data offset
  306 00000158 5987            LDR              R7,[R0, R6] ; verify background
                                                             pattern
  307 0000015A 4297            CMP              R7, R2
  308 0000015C D16E            BNE              __STEP_ERR
  309 0000015E 5183            STR              R3,[R0, R6] ; store inverted ba
                                                            ckground pattern
  310 00000160 F105 0504       ADD              R5,R5,#4
  311 00000164 2D14            CMP              R5, #20
  312 00000166 DDF6            BLE              __STEP2_LOOP
  313 00000168         
  314 00000168                 IF               :DEF:USE_MARCHX_TEST
  316 00000168         ; *** step 3 *** (not used at march-x test)
  317 00000168         ; verify inverted background and write background with a
                       ddresses increasing  
  318 00000168 2500            MOVS             R5, #0
  319 0000016A         __STEP3_LOOP
  320 0000016A 5966            LDR              R6,[R4, R5] ; load data offset
  321 0000016C 5987            LDR              R7,[R0, R6] ; verify inverted b
                                                            ackground pattern
  322 0000016E 429F            CMP              R7, R3
  323 00000170 D164            BNE              __STEP_ERR
  324 00000172 5182            STR              R2,[R0, R6] ; store background 
                                                            pattrern
  325 00000174 F105 0504       ADD              R5,R5,#4
  326 00000178 2D14            CMP              R5, #20
  327 0000017A DDF6            BLE              __STEP3_LOOP
  328 0000017C         
  329 0000017C         ; *** step 4 *** (not used at march-x test)
  330 0000017C         ; verify background and write inverted background with a
                       ddresses decreasing  
  331 0000017C 2518            MOVS             R5, #24
  332 0000017E         __STEP4_LOOP
  333 0000017E 1F2D            SUBS             R5,R5,#4
  334 00000180 5966            LDR              R6,[R4, R5] ; load data offset
  335 00000182 5987            LDR              R7,[R0, R6] ; verify background
                                                             pattern
  336 00000184 4297            CMP              R7, R2
  337 00000186 D159            BNE              __STEP_ERR
  338 00000188 5183            STR              R3,[R0, R6] ; store inverted ba
                                                            ckground pattrern
  339 0000018A 2D00            CMP              R5, #0
  340 0000018C D8F7            BHI              __STEP4_LOOP
  341 0000018E                 ENDIF                        ; march-x
  342 0000018E         
  343 0000018E         ; *** step 5 ***
  344 0000018E         ; verify inverted background and write background with a



ARM Macro Assembler    Page 7 


                       ddresses decreasing 
  345 0000018E 2518            MOVS             R5, #24
  346 00000190         __STEP5_LOOP
  347 00000190 1F2D            SUBS             R5,R5,#4
  348 00000192 5966            LDR              R6,[R4, R5] ; load data offset
  349 00000194 5987            LDR              R7,[R0, R6] ; verify inverted b
                                                            ackground pattern
  350 00000196 429F            CMP              R7, R3
  351 00000198 D150            BNE              __STEP_ERR
  352 0000019A 5182            STR              R2,[R0, R6] ; store background 
                                                            pattrern
  353 0000019C 2D00            CMP              R5, #0
  354 0000019E D8F7            BHI              __STEP5_LOOP
  355 000001A0         
  356 000001A0         ; *** step 6 ***
  357 000001A0         ; verify background with addresses increasing
  358 000001A0 2500            MOVS             R5, #0
  359 000001A2         __STEP6_LOOP
  360 000001A2 5966            LDR              R6,[R4, R5] ; load data offset
  361 000001A4 5987            LDR              R7,[R0, R6] ; verify background
                                                             pattern
  362 000001A6 4297            CMP              R7, R2
  363 000001A8 D148            BNE              __STEP_ERR
  364 000001AA F105 0504       ADD              R5,R5,#4
  365 000001AE 2D14            CMP              R5, #20
  366 000001B0 DDF7            BLE              __STEP6_LOOP
  367 000001B2         
  368 000001B2 2518            MOVS             R5, #24     ; restore content o
                                                            f the ram slice bac
                                                            k from the backup b
                                                            uffer
  369 000001B4         __RESTORE_LOOP
  370 000001B4 594F            LDR              R7,[R1, R5] ; (first and last i
                                                            tems are used for t
                                                            esting purpose excl
                                                            usively)
  371 000001B6 F1A5 0504       SUB              R5,R5,#4    ; original data are
                                                             stored starting fr
                                                            om second item of t
                                                            he buffer
  372 000001BA 5966            LDR              R6,[R4, R5] ; load data offset
  373 000001BC 5187            STR              R7,[R0, R6] ; load data from ra
                                                            m
  374 000001BE 2D00            CMP              R5, #0
  375 000001C0 D8F8            BHI              __RESTORE_LOOP
  376 000001C2         
  377 000001C2 E035            B                __MARCH_RET
  378 000001C4         
  379 000001C4         ; ************** test of the buffer itself *************
                       *******
  380 000001C4         __BUFF_TEST
  381 000001C4         ; *** step 1 *** 
  382 000001C4         ; write background pattern with addresses increasing
  383 000001C4 2504            MOVS             R5, #4
  384 000001C6         __BUFF1_LOOP
  385 000001C6 5966            LDR              R6,[R4, R5] ; load data offset
  386 000001C8 5182            STR              R2,[R0, R6] ; store background 
                                                            pattern
  387 000001CA F105 0504       ADD              R5,R5,#4



ARM Macro Assembler    Page 8 


  388 000001CE 2D20            CMP              R5, #32
  389 000001D0 DDF9            BLE              __BUFF1_LOOP
  390 000001D2         
  391 000001D2         ; *** step 2 ***
  392 000001D2         ; verify background and write inverted background with a
                       ddresses increasing
  393 000001D2 2504            MOVS             R5, #4
  394 000001D4         __BUFF2_LOOP
  395 000001D4 5966            LDR              R6,[R4, R5] ; load data offset
  396 000001D6 5987            LDR              R7,[R0, R6] ; verify background
                                                             pattern
  397 000001D8 4297            CMP              R7, R2
  398 000001DA D12F            BNE              __STEP_ERR
  399 000001DC 5183            STR              R3,[R0, R6] ; store inverted ba
                                                            ckground pattern
  400 000001DE F105 0504       ADD              R5,R5,#4
  401 000001E2 2D20            CMP              R5, #32
  402 000001E4 DDF6            BLE              __BUFF2_LOOP
  403 000001E6         
  404 000001E6                 IF               :DEF:USE_MARCHX_TEST
  406 000001E6         ; *** step 3 *** (not used at march-x test)
  407 000001E6         ; verify inverted background and write background with a
                       ddresses increasing  
  408 000001E6 2504            MOVS             R5, #4
  409 000001E8         __BUFF3_LOOP
  410 000001E8 5966            LDR              R6,[R4, R5] ; load data offset
  411 000001EA 5987            LDR              R7,[R0, R6] ; verify inverted b
                                                            ackground pattern
  412 000001EC 429F            CMP              R7, R3
  413 000001EE D125            BNE              __STEP_ERR
  414 000001F0 5182            STR              R2,[R0, R6] ; store  background
                                                             pattern
  415 000001F2 F105 0504       ADD              R5,R5,#4
  416 000001F6 2D20            CMP              R5, #32
  417 000001F8 DDF6            BLE              __BUFF3_LOOP
  418 000001FA         
  419 000001FA         ; *** step 4 *** (not used at march-x test)
  420 000001FA         ; verify background and write inverted background with a
                       ddresses decreasing  
  421 000001FA 2524            MOVS             R5, #36
  422 000001FC         __BUFF4_LOOP
  423 000001FC 1F2D            SUBS             R5,R5,#4
  424 000001FE 5966            LDR              R6,[R4, R5] ; load data offset
  425 00000200 5987            LDR              R7,[R0, R6] ; verify background
                                                             pattern
  426 00000202 4297            CMP              R7, R2
  427 00000204 D11A            BNE              __STEP_ERR
  428 00000206 5183            STR              R3,[R0, R6] ; store inverted ba
                                                            ckground pattrern
  429 00000208 2D04            CMP              R5, #4
  430 0000020A D8F7            BHI              __BUFF4_LOOP
  431 0000020C                 ENDIF                        ; march-x
  432 0000020C         
  433 0000020C         ; *** step 5 ***
  434 0000020C         ; verify inverted background and write background with a
                       ddresses decreasing 
  435 0000020C 2524            MOVS             R5, #36
  436 0000020E         __BUFF5_LOOP
  437 0000020E 1F2D            SUBS             R5,R5,#4



ARM Macro Assembler    Page 9 


  438 00000210 5966            LDR              R6,[R4, R5] ; load data offset
  439 00000212 5987            LDR              R7,[R0, R6] ; verify inverted b
                                                            ackground pattern
  440 00000214 429F            CMP              R7, R3
  441 00000216 D111            BNE              __STEP_ERR
  442 00000218 5182            STR              R2,[R0, R6] ; store background 
                                                            pattrern
  443 0000021A 2D04            CMP              R5, #4
  444 0000021C D8F7            BHI              __BUFF5_LOOP
  445 0000021E         
  446 0000021E         ; *** step 6 ***
  447 0000021E         ; verify background with addresses increasing
  448 0000021E 2504            MOVS             R5, #4
  449 00000220         __BUFF6_LOOP
  450 00000220 5966            LDR              R6,[R4, R5] ; load data offset
  451 00000222 5987            LDR              R7,[R0, R6] ; verify background
                                                             pattern
  452 00000224 4297            CMP              R7, R2
  453 00000226 D109            BNE              __STEP_ERR
  454 00000228 F105 0504       ADD              R5,R5,#4
  455 0000022C 2D20            CMP              R5, #32
  456 0000022E DDF7            BLE              __BUFF6_LOOP
  457 00000230         
  458 00000230         __MARCH_RET
  459 00000230 4C06            LDR              R4,=isr_ctrl_flow_cnt_inv ; con
                                                            trol flow control
  460 00000232 6825            LDR              R5,[R4]
  461 00000234 1EED            SUBS             R5,R5,#3
  462 00000236 6025            STR              R5,[R4]
  463 00000238         
  464 00000238 2001            MOVS             R0, #1      ; correct return
  465 0000023A E000            B                __STEP_RET
  466 0000023C         
  467 0000023C         __STEP_ERR
  468 0000023C 2000            MOVS             R0, #0      ; error result
  469 0000023E         
  470 0000023E         __STEP_RET
  471 0000023E BCF0            POP              {R4-R7}
  472 00000240 4770            BX               LR          ; return to the cal
                                                            ler
  473 00000242 BF00            NOP
  474 00000244                 END
              00000000 
              00000000 
              00000000 
Command Line: --debug --xref --diag_suppress=9931 --cpu=Cortex-M4.fp.sp --apcs=
interwork --depend=.\objects\at32_selftest_ram_keil.d -o.\objects\at32_selftest
_ram_keil.o -ID:\keil5\ARM\Pack\ArteryTek\AT32F403A_407_DFP\2.1.6\Device\Includ
e --predefine="__UVISION_VERSION SETA 538" --predefine="AT32F403AVGT7 SETA 1" -
-list=.\listings\at32_selftest_ram_keil.lst at32_selftest_ram_keil.s



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

.text 00000000

Symbol: .text
   Definitions
      At line 15 in file at32_selftest_ram_keil.s
   Uses
      None
Comment: .text unused
__ARTISAN_RAM_ORDER 00000024

Symbol: __ARTISAN_RAM_ORDER
   Definitions
      At line 28 in file at32_selftest_ram_keil.s
   Uses
      None
Comment: __ARTISAN_RAM_ORDER unused
__BUFF1_LOOP 000001C6

Symbol: __BUFF1_LOOP
   Definitions
      At line 384 in file at32_selftest_ram_keil.s
   Uses
      At line 389 in file at32_selftest_ram_keil.s
Comment: __BUFF1_LOOP used once
__BUFF2_LOOP 000001D4

Symbol: __BUFF2_LOOP
   Definitions
      At line 394 in file at32_selftest_ram_keil.s
   Uses
      At line 402 in file at32_selftest_ram_keil.s
Comment: __BUFF2_LOOP used once
__BUFF3_LOOP 000001E8

Symbol: __BUFF3_LOOP
   Definitions
      At line 409 in file at32_selftest_ram_keil.s
   Uses
      At line 417 in file at32_selftest_ram_keil.s
Comment: __BUFF3_LOOP used once
__BUFF4_LOOP 000001FC

Symbol: __BUFF4_LOOP
   Definitions
      At line 422 in file at32_selftest_ram_keil.s
   Uses
      At line 430 in file at32_selftest_ram_keil.s
Comment: __BUFF4_LOOP used once
__BUFF5_LOOP 0000020E

Symbol: __BUFF5_LOOP
   Definitions
      At line 436 in file at32_selftest_ram_keil.s
   Uses
      At line 444 in file at32_selftest_ram_keil.s
Comment: __BUFF5_LOOP used once
__BUFF6_LOOP 00000220

Symbol: __BUFF6_LOOP



ARM Macro Assembler    Page 2 Alphabetic symbol ordering
Relocatable symbols

   Definitions
      At line 449 in file at32_selftest_ram_keil.s
   Uses
      At line 456 in file at32_selftest_ram_keil.s
Comment: __BUFF6_LOOP used once
__BUFF_TEST 000001C4

Symbol: __BUFF_TEST
   Definitions
      At line 380 in file at32_selftest_ram_keil.s
   Uses
      At line 279 in file at32_selftest_ram_keil.s
Comment: __BUFF_TEST used once
__FULL1_LOOP 00000052

Symbol: __FULL1_LOOP
   Definitions
      At line 68 in file at32_selftest_ram_keil.s
   Uses
      At line 73 in file at32_selftest_ram_keil.s
Comment: __FULL1_LOOP used once
__FULL2_LOOP 0000005E

Symbol: __FULL2_LOOP
   Definitions
      At line 79 in file at32_selftest_ram_keil.s
   Uses
      At line 110 in file at32_selftest_ram_keil.s
Comment: __FULL2_LOOP used once
__FULL3_LOOP 00000088

Symbol: __FULL3_LOOP
   Definitions
      At line 116 in file at32_selftest_ram_keil.s
   Uses
      At line 147 in file at32_selftest_ram_keil.s
Comment: __FULL3_LOOP used once
__FULL4_LOOP 000000B4

Symbol: __FULL4_LOOP
   Definitions
      At line 154 in file at32_selftest_ram_keil.s
   Uses
      At line 185 in file at32_selftest_ram_keil.s
Comment: __FULL4_LOOP used once
__FULL5_LOOP 000000E0

Symbol: __FULL5_LOOP
   Definitions
      At line 192 in file at32_selftest_ram_keil.s
   Uses
      At line 223 in file at32_selftest_ram_keil.s
Comment: __FULL5_LOOP used once
__FULL6_LOOP 0000010A

Symbol: __FULL6_LOOP
   Definitions
      At line 229 in file at32_selftest_ram_keil.s
   Uses



ARM Macro Assembler    Page 3 Alphabetic symbol ordering
Relocatable symbols

      At line 236 in file at32_selftest_ram_keil.s
Comment: __FULL6_LOOP used once
__FULLSTEP_2 0000005C

Symbol: __FULLSTEP_2
   Definitions
      At line 77 in file at32_selftest_ram_keil.s
   Uses
      At line 70 in file at32_selftest_ram_keil.s
Comment: __FULLSTEP_2 used once
__FULLSTEP_3 00000086

Symbol: __FULLSTEP_3
   Definitions
      At line 114 in file at32_selftest_ram_keil.s
   Uses
      At line 81 in file at32_selftest_ram_keil.s
Comment: __FULLSTEP_3 used once
__FULLSTEP_4 000000B0

Symbol: __FULLSTEP_4
   Definitions
      At line 151 in file at32_selftest_ram_keil.s
   Uses
      At line 118 in file at32_selftest_ram_keil.s
Comment: __FULLSTEP_4 used once
__FULLSTEP_5 000000DC

Symbol: __FULLSTEP_5
   Definitions
      At line 189 in file at32_selftest_ram_keil.s
   Uses
      At line 156 in file at32_selftest_ram_keil.s
Comment: __FULLSTEP_5 used once
__FULLSTEP_6 00000108

Symbol: __FULLSTEP_6
   Definitions
      At line 227 in file at32_selftest_ram_keil.s
   Uses
      At line 194 in file at32_selftest_ram_keil.s
Comment: __FULLSTEP_6 used once
__FULL_ERR 00000118

Symbol: __FULL_ERR
   Definitions
      At line 238 in file at32_selftest_ram_keil.s
   Uses
      At line 84 in file at32_selftest_ram_keil.s
      At line 88 in file at32_selftest_ram_keil.s
      At line 102 in file at32_selftest_ram_keil.s
      At line 106 in file at32_selftest_ram_keil.s
      At line 121 in file at32_selftest_ram_keil.s
      At line 125 in file at32_selftest_ram_keil.s
      At line 139 in file at32_selftest_ram_keil.s
      At line 143 in file at32_selftest_ram_keil.s
      At line 169 in file at32_selftest_ram_keil.s
      At line 173 in file at32_selftest_ram_keil.s
      At line 178 in file at32_selftest_ram_keil.s



ARM Macro Assembler    Page 4 Alphabetic symbol ordering
Relocatable symbols

      At line 182 in file at32_selftest_ram_keil.s
      At line 207 in file at32_selftest_ram_keil.s
      At line 211 in file at32_selftest_ram_keil.s
      At line 216 in file at32_selftest_ram_keil.s
      At line 220 in file at32_selftest_ram_keil.s
      At line 234 in file at32_selftest_ram_keil.s

__FULL_RET 0000011A

Symbol: __FULL_RET
   Definitions
      At line 241 in file at32_selftest_ram_keil.s
   Uses
      At line 231 in file at32_selftest_ram_keil.s
Comment: __FULL_RET used once
__MARCH_RET 00000230

Symbol: __MARCH_RET
   Definitions
      At line 458 in file at32_selftest_ram_keil.s
   Uses
      At line 377 in file at32_selftest_ram_keil.s
Comment: __MARCH_RET used once
__RESTORE_LOOP 000001B4

Symbol: __RESTORE_LOOP
   Definitions
      At line 369 in file at32_selftest_ram_keil.s
   Uses
      At line 375 in file at32_selftest_ram_keil.s
Comment: __RESTORE_LOOP used once
__SAVE_LOOP 00000138

Symbol: __SAVE_LOOP
   Definitions
      At line 283 in file at32_selftest_ram_keil.s
   Uses
      At line 289 in file at32_selftest_ram_keil.s
Comment: __SAVE_LOOP used once
__STANDARD_RAM_ORDER 00000000

Symbol: __STANDARD_RAM_ORDER
   Definitions
      At line 18 in file at32_selftest_ram_keil.s
   Uses
      At line 274 in file at32_selftest_ram_keil.s
Comment: __STANDARD_RAM_ORDER used once
__STEP1_LOOP 00000148

Symbol: __STEP1_LOOP
   Definitions
      At line 294 in file at32_selftest_ram_keil.s
   Uses
      At line 299 in file at32_selftest_ram_keil.s
Comment: __STEP1_LOOP used once
__STEP2_LOOP 00000156

Symbol: __STEP2_LOOP
   Definitions



ARM Macro Assembler    Page 5 Alphabetic symbol ordering
Relocatable symbols

      At line 304 in file at32_selftest_ram_keil.s
   Uses
      At line 312 in file at32_selftest_ram_keil.s
Comment: __STEP2_LOOP used once
__STEP3_LOOP 0000016A

Symbol: __STEP3_LOOP
   Definitions
      At line 319 in file at32_selftest_ram_keil.s
   Uses
      At line 327 in file at32_selftest_ram_keil.s
Comment: __STEP3_LOOP used once
__STEP4_LOOP 0000017E

Symbol: __STEP4_LOOP
   Definitions
      At line 332 in file at32_selftest_ram_keil.s
   Uses
      At line 340 in file at32_selftest_ram_keil.s
Comment: __STEP4_LOOP used once
__STEP5_LOOP 00000190

Symbol: __STEP5_LOOP
   Definitions
      At line 346 in file at32_selftest_ram_keil.s
   Uses
      At line 354 in file at32_selftest_ram_keil.s
Comment: __STEP5_LOOP used once
__STEP6_LOOP 000001A2

Symbol: __STEP6_LOOP
   Definitions
      At line 359 in file at32_selftest_ram_keil.s
   Uses
      At line 366 in file at32_selftest_ram_keil.s
Comment: __STEP6_LOOP used once
__STEP_ERR 0000023C

Symbol: __STEP_ERR
   Definitions
      At line 467 in file at32_selftest_ram_keil.s
   Uses
      At line 308 in file at32_selftest_ram_keil.s
      At line 323 in file at32_selftest_ram_keil.s
      At line 337 in file at32_selftest_ram_keil.s
      At line 351 in file at32_selftest_ram_keil.s
      At line 363 in file at32_selftest_ram_keil.s
      At line 398 in file at32_selftest_ram_keil.s
      At line 413 in file at32_selftest_ram_keil.s
      At line 427 in file at32_selftest_ram_keil.s
      At line 441 in file at32_selftest_ram_keil.s
      At line 453 in file at32_selftest_ram_keil.s

__STEP_RET 0000023E

Symbol: __STEP_RET
   Definitions
      At line 470 in file at32_selftest_ram_keil.s
   Uses



ARM Macro Assembler    Page 6 Alphabetic symbol ordering
Relocatable symbols

      At line 465 in file at32_selftest_ram_keil.s
Comment: __STEP_RET used once
selftest_full_ram_test 00000048

Symbol: selftest_full_ram_test
   Definitions
      At line 58 in file at32_selftest_ram_keil.s
   Uses
      At line 42 in file at32_selftest_ram_keil.s
Comment: selftest_full_ram_test used once
selftest_ram_step_implement 0000011E

Symbol: selftest_ram_step_implement
   Definitions
      At line 259 in file at32_selftest_ram_keil.s
   Uses
      At line 43 in file at32_selftest_ram_keil.s
Comment: selftest_ram_step_implement used once
36 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
External symbols

isr_ctrl_flow_cnt 00000000

Symbol: isr_ctrl_flow_cnt
   Definitions
      At line 39 in file at32_selftest_ram_keil.s
   Uses
      At line 262 in file at32_selftest_ram_keil.s
Comment: isr_ctrl_flow_cnt used once
isr_ctrl_flow_cnt_inv 00000000

Symbol: isr_ctrl_flow_cnt_inv
   Definitions
      At line 40 in file at32_selftest_ram_keil.s
   Uses
      At line 459 in file at32_selftest_ram_keil.s
Comment: isr_ctrl_flow_cnt_inv used once
2 symbols
387 symbols in table
