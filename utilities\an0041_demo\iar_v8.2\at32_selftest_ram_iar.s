;/**
;  **************************************************************************
;  * @file     at32_selftest_ram_iar.s
;  * @version  v2.0.0
;  * @date     2021-12-31
;  * @brief    contains procedures written in assembler for full and partial 
;  *           marching ram tests to be called during startup and runtime.
;  **************************************************************************
;  */

  SECTION mydata:CONST(2)

; tables with offsets of physical order of address in RAM
__STANDARD_RAM_ORDER
        DCD    -4
        DCD     0
        DCD     4
        DCD     8
        DCD     12
        DCD     16
        DCD     20
        DCD     24
        DCD     28
__ARTISAN_RAM_ORDER
        DCD    -8
        DCD     0
        DCD     4
        DCD     12
        DCD     8
        DCD     16
        DCD     20
        DCD     28
        DCD     24

  SECTION text:CODE(2)
 
  EXTERN isr_ctrl_flow_cnt
  EXTERN isr_ctrl_flow_cnt_inv
  
  EXPORT selftest_full_ram_test
  EXPORT selftest_ram_step_implement

;/**
;  * @brief  full ram marchc test for startup
;  * @note   all the ram area including stack is destroyed during this test
;  * @param  
;  *         input: r0 .. ram begin (first address to check), 
;  *                r1 .. ram end (last address to check)
;  *                r2 .. background pattern
;  *         local: r3 .. inverted background pattern
;  *                r4 .. keeps test result status
;  *                r5 .. pointer to ram
;  *                r6 .. content ram to compare  
;  * @retval test successfull (=1)
;  */
selftest_full_ram_test:
  MOVS  R4, #0x1       ; Test success status by default
  
  MOVS  R3,R2          ; setup inverted background pattern
  RSBS  R3, R3, #0
  SUBS  R3,R3, #1
  
; *** step 1 *** 
; write background pattern with addresses increasing
  MOVS  R5,R0
  FULL1_LOOP:
  CMP   R5,R1
  BHI   FULLstep_2
  STR   R2,[R5, #+0]
  ADDS  R5,R5,#+4
  B     FULL1_LOOP
    
; *** step 2 ***
; verify background and write inverted background with addresses increasing
  FULLstep_2:
  MOVS  R5,R0
  FULL2_LOOP:
  CMP   R5,R1
  BHI   FULLstep_3
  LDR   R6,[R5,#+0]
  CMP   R6,R2
  BNE   FULL_ERR
  STR   R3,[R5,#+0]
  LDR   R6,[R5,#+4]
  CMP   R6,R2
  BNE   FULL_ERR
  STR   R3,[R5,#+4]
#ifdef ARTISAN
  LDR   R6,[R5,#+12]
  CMP   R6,R2
  BNE   FULL_ERR
  STR   R3,[R5,#+12]
  LDR   R6,[R5,#+8]
  CMP   R6,R2
  BNE   FULL_ERR
  STR   R3,[R5,#+8]
#else
  LDR   R6,[R5,#+8]
  CMP   R6,R2
  BNE   FULL_ERR
  STR   R3,[R5,#+8]
  LDR   R6,[R5,#+12]
  CMP   R6,R2
  BNE   FULL_ERR
  STR   R3,[R5,#+12]
#endif
  ADDS  R5,R5,#+16
  B     FULL2_LOOP
  
; *** step 3 ***
; verify inverted background and write background with addresses increasing  
  FULLstep_3:
  MOVS  R5,R0
  FULL3_LOOP:
  CMP   R5,R1
  BHI   FULLstep_4  
  LDR   R6,[R5,#+0]
  CMP   R6,R3
  BNE   FULL_ERR
  STR   R2,[R5,#+0]
  LDR   R6,[R5,#+4]
  CMP   R6,R3
  BNE   FULL_ERR
  STR   R2,[R5,#+4]
#ifdef ARTISAN
  LDR   R6,[R5,#+12]
  CMP   R6,R3
  BNE   FULL_ERR
  STR   R2,[R5,#+12]
  LDR   R6,[R5,#+8]
  CMP   R6,R3
  BNE   FULL_ERR
  STR   R2,[R5,#+8]
#else
  LDR   R6,[R5,#+8]
  CMP   R6,R3
  BNE   FULL_ERR
  STR   R2,[R5,#+8]
  LDR   R6,[R5,#+12]
  CMP   R6,R3
  BNE   FULL_ERR
  STR   R2,[R5,#+12]
#endif
  ADDS  R5,R5,#+16
  B     FULL3_LOOP

; *** step 4 ***
; verify background and write inverted background with addresses decreasing  
  FULLstep_4:
  MOVS  R5,R1
  SUBS  R5,R5,#+15
  FULL4_LOOP:
  CMP   R5,R0
  BLO   FULLstep_5
#ifdef ARTISAN
  LDR   R6,[R5,#+8]
  CMP   R6,R2
  BNE   FULL_ERR
  STR   R3,[R5,#+8]
  LDR   R6,[R5,#+12]
  CMP   R6,R2
  BNE   FULL_ERR
  STR   R3,[R5,#+12]
#else
  LDR   R6,[R5,#+12]
  CMP   R6,R2
  BNE   FULL_ERR
  STR   R3,[R5,#+12]
  LDR   R6,[R5,#+8]
  CMP   R6,R2
  BNE   FULL_ERR
  STR   R3,[R5,#+8]
#endif
  LDR   R6,[R5,#+4]
  CMP   R6,R2
  BNE   FULL_ERR
  STR   R3,[R5,#+4]
  LDR   R6,[R5,#+0]
  CMP   R6,R2
  BNE   FULL_ERR
  STR   R3,[R5,#+0]
  SUBS  R5,R5,#+16
  B     FULL4_LOOP
  
; *** step 5 ***
; verify inverted background and write background with addresses decreasing 
  FULLstep_5:
  MOVS  R5,R1
  SUBS  R5,R5,#+15
  FULL5_LOOP:
  CMP   R5,R0
  BLO   FULLstep_6
#ifdef ARTISAN
  LDR   R6,[R5,#+8]
  CMP   R6,R3
  BNE   FULL_ERR
  STR   R2,[R5,#+8]
  LDR   R6,[R5,#+12]
  CMP   R6,R3
  BNE   FULL_ERR
  STR   R2,[R5,#+12]
#else
  LDR   R6,[R5,#+12]
  CMP   R6,R3
  BNE   FULL_ERR
  STR   R2,[R5,#+12]
  LDR   R6,[R5,#+8]
  CMP   R6,R3
  BNE   FULL_ERR
  STR   R2,[R5,#+8]
#endif
  LDR   R6,[R5,#+4]
  CMP   R6,R3
  BNE   FULL_ERR
  STR   R2,[R5,#+4]
  LDR   R6,[R5,#+0]
  CMP   R6,R3
  BNE   FULL_ERR
  STR   R2,[R5,#+0]
  SUBS  R5,R5,#+16
  B     FULL5_LOOP

; *** step 6 ***
; verify background with addresses increasing
  FULLstep_6:
  MOVS  R5,R0
  FULL6_LOOP:
  CMP   R5,R1
  BHI   FULL_RET
  LDR   R6,[R5,#+0]
  CMP   R6,R2
  BNE   FULL_ERR
  ADDS  R5,R5,#+4
  B     FULL6_LOOP

  FULL_ERR:
  MOVS  R4,#0       ; error result

  FULL_RET:
  MOVS  R0,R4
  BX    LR          ; return to the caller
  
;/**
;  * @brief  ram marchc-/march x test for runtime
;  * @note   the ram area under test is out of original content during this test!
;  *         neighbour addresses (first-1 or -2 and last+1) are tested, too.
;  * @param  
;  *         input: r0 .. ram begin (first address to test), 
;  *                r1 .. buffer begin (first address of backup buffer)
;  *                r2 .. background pattern
;  * @retval test successfull (=1)
;  * compilation paramaters : ARTISAN - changes order of the sequence of tested
;  *                                    addresses to respect their physical order
;  *                  USE_MARCHX_TEST - skip step 3 and 4 of march c- to make the test
;  *                                    shorter and faster overall
;  */
selftest_ram_step_implement:
  PUSH  {R4-R7}

  LDR   R5,=isr_ctrl_flow_cnt  ; Control flow control
  LDR   R6,[R5]
  ADDS  R6,R6,#3
  STR   R6,[R5]
  
  MOVS  R3,R2          ; setup inverted background pattern (R3)
  RSBS  R3, R3, #0
  SUBS  R3,R3, #1  

#ifdef ARTISAN
  LDR   R4, =__ARTISAN_RAM_ORDER ;setup pointer to physical order of the addresses (R4)
#else
  LDR   R4, =__STANDARD_RAM_ORDER
#endif

  MOVS  R5,R0       ; backup buffer to be tested
  CMP   R5,R1
  BEQ   BUFF_TEST
  
; ***************** test of the ram slice *********************
  MOVS  R5, #0       ; no - save content of the ram slice into the backup buffer
  SAVE_LOOP:
  LDR   R6,[R4, R5]  ; load data offset
  LDR   R7,[R0, R6]  ; load data from ram
  ADD   R5,R5,#4     ; original data are stored starting from second item of the buffer
  STR   R7,[R1, R5]  ; (first and last items are used for testing purpose exclusively)
  CMP   R5, #20
  BLE   SAVE_LOOP
  
; *** step 1 *** 
; write background pattern with addresses increasing
  MOVS  R5, #0
  step1_LOOP:
  LDR   R6,[R4, R5]  ; load data offset
  STR   R2,[R0, R6]  ; store background pattern
  ADD   R5,R5,#4
  CMP   R5, #20
  BLE   step1_LOOP
  
; *** step 2 ***
; verify background and write inverted background with addresses increasing
  MOVS  R5, #0
  step2_LOOP:
  LDR   R6,[R4, R5]  ; load data offset
  LDR   R7,[R0, R6]  ; verify background pattern
  CMP   R7, R2
  BNE   step_ERR
  STR   R3,[R0, R6]  ; store inverted background pattern
  ADD   R5,R5,#4
  CMP   R5, #20
  BLE   step2_LOOP

#ifndef USE_MARCHX_TEST   
; *** step 3 *** (not used at March-X test)
; verify inverted background and write background with addresses increasing  
  MOVS  R5, #0
  step3_LOOP:
  LDR   R6,[R4, R5]  ; load data offset
  LDR   R7,[R0, R6]  ; verify inverted background pattern
  CMP   R7, R3
  BNE   step_ERR
  STR   R2,[R0, R6]  ; store background pattrern
  ADD   R5,R5,#4
  CMP   R5, #20
  BLE   step3_LOOP
  
; *** step 4 *** (not used at March-X test)
; verify background and write inverted background with addresses decreasing  
  MOVS  R5, #24
  step4_LOOP:
  SUBS  R5,R5,#4
  LDR   R6,[R4, R5]  ; load data offset
  LDR   R7,[R0, R6]  ; verify background pattern
  CMP   R7, R2
  BNE   step_ERR
  STR   R3,[R0, R6]  ; store inverted background pattrern
  CMP   R5, #0
  BHI   step4_LOOP
#endif                ; March-X
  
; *** step 5 ***
; verify inverted background and write background with addresses decreasing 
  MOVS  R5, #24
  step5_LOOP:
  SUBS  R5,R5,#4
  LDR   R6,[R4, R5]  ; load data offset
  LDR   R7,[R0, R6]  ; verify inverted background pattern
  CMP   R7, R3
  BNE   step_ERR
  STR   R2,[R0, R6]  ; store background pattrern
  CMP   R5, #0
  BHI   step5_LOOP

; *** step 6 ***
; verify background with addresses increasing
  MOVS  R5, #0
  step6_LOOP:
  LDR   R6,[R4, R5]  ; load data offset
  LDR   R7,[R0, R6]  ; verify background pattern
  CMP   R7, R2
  BNE   step_ERR
  ADD   R5,R5,#4
  CMP   R5, #20
  BLE   step6_LOOP

  MOVS  R5, #24      ; restore content of the RAM slice back from the backup buffer
  RESTORE_LOOP:
  LDR   R7,[R1, R5]  ; (first and last items are used for testing purpose exclusively)
  SUB   R5,R5,#4     ; original data are stored starting from second item of the buffer
  LDR   R6,[R4, R5]  ; load data offset
  STR   R7,[R0, R6]  ; load data from RAM
  CMP   R5, #0
  BHI   RESTORE_LOOP
  
  B     MARCH_RET

; ************** test of the buffer itself ********************
  BUFF_TEST:
; *** step 1 *** 
; Write background pattern with addresses increasing
  MOVS  R5, #4
  BUFF1_LOOP:
  LDR   R6,[R4, R5]  ; load data offset
  STR   R2,[R0, R6]  ; store background pattern
  ADD   R5,R5,#4
  CMP   R5, #32
  BLE   BUFF1_LOOP
  
; *** step 2 ***
; verify background and write inverted background with addresses increasing
  MOVS  R5, #4
  BUFF2_LOOP:
  LDR   R6,[R4, R5]  ; load data offset
  LDR   R7,[R0, R6]  ; verify background pattern
  CMP   R7, R2
  BNE   step_ERR
  STR   R3,[R0, R6]  ; store inverted background pattern
  ADD   R5,R5,#4
  CMP   R5, #32
  BLE   BUFF2_LOOP
  
#ifndef USE_MARCHX_TEST   
; *** step 3 *** (not used at March-X test)
; verify inverted background and write background with addresses increasing  
  MOVS  R5, #4
  BUFF3_LOOP:
  LDR   R6,[R4, R5]  ; load data offset
  LDR   R7,[R0, R6]  ; verify inverted background pattern
  CMP   R7, R3
  BNE   step_ERR
  STR   R2,[R0, R6]  ; store  background pattern
  ADD   R5,R5,#4
  CMP   R5, #32
  BLE   BUFF3_LOOP

; *** step 4 *** (not used at March-X test)
; verify background and write inverted background with addresses decreasing  
  MOVS  R5, #36
  BUFF4_LOOP:
  SUBS  R5,R5,#4
  LDR   R6,[R4, R5]  ; load data offset
  LDR   R7,[R0, R6]  ; verify background pattern
  CMP   R7, R2
  BNE   step_ERR
  STR   R3,[R0, R6]  ; store inverted background pattrern
  CMP   R5, #4
  BHI   BUFF4_LOOP
#endif                ; March-X

; *** step 5 ***
; verify inverted background and write background with addresses decreasing 
  MOVS  R5, #36
  BUFF5_LOOP:
  SUBS  R5,R5,#4
  LDR   R6,[R4, R5]  ; load data offset
  LDR   R7,[R0, R6]  ; verify inverted background pattern
  CMP   R7, R3
  BNE   step_ERR
  STR   R2,[R0, R6]  ; store background pattrern
  CMP   R5, #4
  BHI   BUFF5_LOOP

; *** step 6 ***
; verify background with addresses increasing
  MOVS  R5, #4
  BUFF6_LOOP:
  LDR   R6,[R4, R5]  ; load data offset
  LDR   R7,[R0, R6]  ; verify background pattern
  CMP   R7, R2
  BNE   step_ERR
  ADD   R5,R5,#4
  CMP   R5, #32
  BLE   BUFF6_LOOP

  MARCH_RET:
  LDR   R4,=isr_ctrl_flow_cnt_inv  ; Control flow control
  LDR   R5,[R4]
  SUBS  R5,R5,#3
  STR   R5,[R4]
  
  MOVS  R0, #1       ; Correct return
  B     step_RET
  
  step_ERR:
  MOVS  R0, #0       ; error result
  
  step_RET:
  POP   {R4-R7}
  BX    LR           ; return to the caller
  
  END


