/**
  **************************************************************************
  * @file     at32_selftest_runtime.c
  * @version  v2.0.0
  * @date     2021-12-31
  * @brief    contains self-test related routines in runtime phase.
  **************************************************************************
  */

#include "main.h"
#include "at32_selftest_param.h"
#include "at32_selftest_lib.h"

/**
  * @brief  this function verifies that stack didn't overflow
  * @param  none
  * @retval error_status
  */
static error_status selftest_stack_check(void)
{
  error_status result = SUCCESS;
  control_flow_call(STACK_OVERFLOW_CALLEE);
  if((stack_overflow_buf[0] != STACK_OVERFLOW_FLAG_0) || (stack_overflow_buf[1] != STACK_OVERFLOW_FLAG_1) || \
     (stack_overflow_buf[2] != STACK_OVERFLOW_FLAG_2) || (stack_overflow_buf[3] != STACK_OVERFLOW_FLAG_3))
  {
    result = ERROR;
  }
  control_flow_resume(STACK_OVERFLOW_CALLEE);
  return result;
}

/**
  * @brief  selftest init in runtime phase.
  * @param  none
  * @retval none
  */
void selftest_runtime_init(void)
{ 
  /* initialize variables for invariable memory check */
  p_runtime_ram_chk = (uint32_t *)CLASS_B_START_ADDR;
  p_runtime_ram_chk_inv = (uint32_t *)(~(uint32_t)p_runtime_ram_chk);

  /* initialize class b variables required in runtime */
 
  isr_ctrl_flow_cnt = 0;
  isr_ctrl_flow_cnt_inv = ~isr_ctrl_flow_cnt;
  
  systick_cnt = 0;
  systick_cnt_inv = ~systick_cnt;

  time_base_flag = 0;
  time_base_flag_inv = ~time_base_flag;

  lick_period_flag = 0;
  lick_period_flag_inv = ~lick_period_flag;
 
  last_ctrl_flow_cnt = 0;
  last_ctrl_flow_cnt_inv = ~last_ctrl_flow_cnt;
  
  control_flow_init();

  /* initialize variables for invariable memory check */
  p_runtime_crc_chk = (uint32_t *)ROM_START_ADDR;
  p_runtime_crc_chk_inv = (uint32_t *)(~(uint32_t)p_runtime_crc_chk);
  #ifdef __IAR_SYSTEMS_ICC__  /* iar compiler */
  crc_data_reset();
  #endif
  
  /* initialize systick */
  SysTick_Config(system_core_clock / 1000);

#ifdef WDT_USED
  /* initialize wdt */  
  debug_periph_mode_set(DEBUG_WDT_PAUSE, TRUE);
  wdt_divider_set(WDT_CLK_DIV_256);
  wdt_reload_value_set(8);
  wdt_counter_reload();
  wdt_enable();
#endif

#ifdef WWDT_USED
  /* initialize wwdt */  
  debug_periph_mode_set(DEBUG_WWDT_PAUSE, TRUE);
  crm_periph_clock_enable(CRM_WWDT_PERIPH_CLOCK, TRUE);
  wwdt_divider_set(WWDT_PCLK1_DIV_32768);
  wwdt_window_counter_set(127);
  wwdt_enable(127);
#endif
}

/**
  * @brief  selftest check in runtime phase.
  * @param  none
  * @retval none
  */
void selftest_runtime_check(void)
{
  crc_status_type crc_result;
  uint32_t temp_time_base_flag;
  
  if(time_base_flag == 0xAAAAAAAA)
  {
    temp_time_base_flag = ~time_base_flag_inv;
    if(time_base_flag == temp_time_base_flag)
    {
      time_base_flag = 0;
    
      /***********************************************************************
                     cpu registers and flags test 
      ***********************************************************************/
      control_flow_call(CPU_TEST_CALLER);
      
      if(selftest_cpu_runtime_test() != CPU_TEST_SUCCESS)
      {
        RUNTIME_PRINTF("cpu test fail in runtime.\r\n");
        selftest_fail_handle();
      }
      else
      {
        control_flow_resume(CPU_TEST_CALLER);
      }
      
      /***********************************************************************
                             clock monitoring test
      ***********************************************************************/
      control_flow_call(CLOCK_TEST_CALLER);
      
      if(selftest_clock_runtime_test() != CLOCK_TEST_SUCCESS)
      {
        #ifdef DEBUG_MESSAGE_RUNTIME
        /* clock err, usart may need reconfigure */
        while(usart_flag_get(USARTx, USART_TDC_FLAG) != SET);
        usart_reconfigure();
        #endif
        RUNTIME_PRINTF("clock test error.\r\n");
        selftest_fail_handle();
      }
      else
      {
        control_flow_resume(CLOCK_TEST_CALLER);
      }

      /***********************************************************************
                              flash crc test
      ***********************************************************************/
      control_flow_call(CRC_TEST_CALLER);
			
      crc_result = selftest_crc_runtime_test();
      switch(crc_result)
      {
        case CRC_TEST_CONTINUE:
          control_flow_resume(CRC_TEST_CALLER);
          break;
        case CRC_TEST_SUCCESS:
          #ifdef DEBUG_MESSAGE_RUNTIME
          putchar((int16_t)'*');
          #endif
          control_flow_resume(CRC_TEST_CALLER);
          break;
        case CRC_TEST_ERROR:
        default:
          RUNTIME_PRINTF("flash crc error in runtime.\r\n");
          selftest_fail_handle();
          break;
      }

      /***********************************************************************
                             stack overflow test
      ***********************************************************************/
      control_flow_call(STACK_OVERFLOW_CALLER);
      
      if (selftest_stack_check() != SUCCESS)
      {
        RUNTIME_PRINTF("stack overflow\r\n");
        selftest_fail_handle();
      }
      else
      {
        control_flow_resume(STACK_OVERFLOW_CALLER);
      }

      /***********************************************************************
                           control flow check
      ***********************************************************************/
      if((ctrl_flow_cnt == ~ctrl_flow_cnt_inv) && (last_ctrl_flow_cnt == ~last_ctrl_flow_cnt_inv))
      {
        if(crc_result == CRC_TEST_SUCCESS)
        {
          if((ctrl_flow_cnt == RUNTIME_WHOLE_CHECKED) && ((ctrl_flow_cnt - last_ctrl_flow_cnt) == RUNTIME_CIRCLE_ONCE))
          {
            ctrl_flow_cnt = 0;
            ctrl_flow_cnt_inv = ~ctrl_flow_cnt;
          }
          else
          {
            RUNTIME_PRINTF("control flow error in runtime.\r\n");
            selftest_fail_handle();
          }
        }
        else  /* flash test continued */
        {
          if((ctrl_flow_cnt - last_ctrl_flow_cnt) != RUNTIME_CIRCLE_ONCE)
          {
            RUNTIME_PRINTF("control flow error in runtime, flash crc continued phase.\r\n");
            selftest_fail_handle();
          }
        }
  
        last_ctrl_flow_cnt = ctrl_flow_cnt;
        last_ctrl_flow_cnt_inv = ctrl_flow_cnt_inv;
      }
      else
      {
        RUNTIME_PRINTF("class b variable error in runtime.\r\n");
        selftest_fail_handle();
      }
      /***********************************************************************
                           reload wdt and wwdt
      ***********************************************************************/
      #ifdef WWDT_USED
      wwdt_counter_set(127);
      #endif
      
      #ifdef WDT_USED
      wdt_counter_reload();
      #endif
    }
    else
    {
      RUNTIME_PRINTF("class b variable error in runtime.\r\n");
      selftest_fail_handle();
    }
  }
}
