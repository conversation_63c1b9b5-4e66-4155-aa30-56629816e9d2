/**
  **************************************************************************
  * @file     at32_selftest_cpu.h
  * @version  v2.0.0
  * @date     2021-12-31
  * @brief    at32 selftest cpu header file
  **************************************************************************
  */
  
#ifndef __AT32_SELFTEST_CPU_H
#define __AT32_SELFTEST_CPU_H

typedef enum
{
  CPU_TEST_ERROR = 0,
  CPU_TEST_SUCCESS = 1
} cpu_status_type;

cpu_status_type selftest_cpu_startup_test(void);
cpu_status_type selftest_cpu_runtime_test(void);

#endif
