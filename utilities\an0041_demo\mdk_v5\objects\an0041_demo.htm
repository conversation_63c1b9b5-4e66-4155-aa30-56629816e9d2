<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [.\objects\an0041_demo.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image .\objects\an0041_demo.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 5050106: Last Updated: Thu Aug 07 11:05:52 2025
<BR><P>
<H3>Maximum Stack Usage =        168 bytes + Unknown(Functions without stacksize, Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
__rt_entry_main &rArr; main &rArr; selftest_startup_check &rArr; selftest_system_clock_config &rArr; selftest_fail_handle &rArr; __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
<P>
<H3>
Functions with no stack information
</H3><UL>
 <LI><a href="#[b4]">selftest_full_ram_test</a>
 <LI><a href="#[b0]">selftest_ram_step_implement</a>
 <LI><a href="#[7d]">__user_initial_stackheap</a>
</UL>
</UL>
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[50]">ACC_IRQHandler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[50]">ACC_IRQHandler</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[50]">ACC_IRQHandler</a> from startup_at32f403a_407.o(.text) referenced from startup_at32f403a_407.o(RESET)
 <LI><a href="#[1e]">ADC1_2_IRQHandler</a> from startup_at32f403a_407.o(.text) referenced from startup_at32f403a_407.o(RESET)
 <LI><a href="#[3b]">ADC3_IRQHandler</a> from startup_at32f403a_407.o(.text) referenced from startup_at32f403a_407.o(RESET)
 <LI><a href="#[6]">BusFault_Handler</a> from at32f403a_407_int.o(i.BusFault_Handler) referenced from startup_at32f403a_407.o(RESET)
 <LI><a href="#[21]">CAN1_RX1_IRQHandler</a> from startup_at32f403a_407.o(.text) referenced from startup_at32f403a_407.o(RESET)
 <LI><a href="#[22]">CAN1_SE_IRQHandler</a> from startup_at32f403a_407.o(.text) referenced from startup_at32f403a_407.o(RESET)
 <LI><a href="#[4d]">CAN2_RX0_IRQHandler</a> from startup_at32f403a_407.o(.text) referenced from startup_at32f403a_407.o(RESET)
 <LI><a href="#[4e]">CAN2_RX1_IRQHandler</a> from startup_at32f403a_407.o(.text) referenced from startup_at32f403a_407.o(RESET)
 <LI><a href="#[4f]">CAN2_SE_IRQHandler</a> from startup_at32f403a_407.o(.text) referenced from startup_at32f403a_407.o(RESET)
 <LI><a href="#[4c]">CAN2_TX_IRQHandler</a> from startup_at32f403a_407.o(.text) referenced from startup_at32f403a_407.o(RESET)
 <LI><a href="#[11]">CRM_IRQHandler</a> from startup_at32f403a_407.o(.text) referenced from startup_at32f403a_407.o(RESET)
 <LI><a href="#[17]">DMA1_Channel1_IRQHandler</a> from startup_at32f403a_407.o(.text) referenced from startup_at32f403a_407.o(RESET)
 <LI><a href="#[18]">DMA1_Channel2_IRQHandler</a> from startup_at32f403a_407.o(.text) referenced from startup_at32f403a_407.o(RESET)
 <LI><a href="#[19]">DMA1_Channel3_IRQHandler</a> from startup_at32f403a_407.o(.text) referenced from startup_at32f403a_407.o(RESET)
 <LI><a href="#[1a]">DMA1_Channel4_IRQHandler</a> from startup_at32f403a_407.o(.text) referenced from startup_at32f403a_407.o(RESET)
 <LI><a href="#[1b]">DMA1_Channel5_IRQHandler</a> from startup_at32f403a_407.o(.text) referenced from startup_at32f403a_407.o(RESET)
 <LI><a href="#[1c]">DMA1_Channel6_IRQHandler</a> from startup_at32f403a_407.o(.text) referenced from startup_at32f403a_407.o(RESET)
 <LI><a href="#[1d]">DMA1_Channel7_IRQHandler</a> from startup_at32f403a_407.o(.text) referenced from startup_at32f403a_407.o(RESET)
 <LI><a href="#[44]">DMA2_Channel1_IRQHandler</a> from startup_at32f403a_407.o(.text) referenced from startup_at32f403a_407.o(RESET)
 <LI><a href="#[45]">DMA2_Channel2_IRQHandler</a> from startup_at32f403a_407.o(.text) referenced from startup_at32f403a_407.o(RESET)
 <LI><a href="#[46]">DMA2_Channel3_IRQHandler</a> from startup_at32f403a_407.o(.text) referenced from startup_at32f403a_407.o(RESET)
 <LI><a href="#[47]">DMA2_Channel4_5_IRQHandler</a> from startup_at32f403a_407.o(.text) referenced from startup_at32f403a_407.o(RESET)
 <LI><a href="#[53]">DMA2_Channel6_7_IRQHandler</a> from startup_at32f403a_407.o(.text) referenced from startup_at32f403a_407.o(RESET)
 <LI><a href="#[9]">DebugMon_Handler</a> from at32f403a_407_int.o(i.DebugMon_Handler) referenced from startup_at32f403a_407.o(RESET)
 <LI><a href="#[57]">EMAC_IRQHandler</a> from startup_at32f403a_407.o(.text) referenced from startup_at32f403a_407.o(RESET)
 <LI><a href="#[58]">EMAC_WKUP_IRQHandler</a> from startup_at32f403a_407.o(.text) referenced from startup_at32f403a_407.o(RESET)
 <LI><a href="#[12]">EXINT0_IRQHandler</a> from startup_at32f403a_407.o(.text) referenced from startup_at32f403a_407.o(RESET)
 <LI><a href="#[34]">EXINT15_10_IRQHandler</a> from startup_at32f403a_407.o(.text) referenced from startup_at32f403a_407.o(RESET)
 <LI><a href="#[13]">EXINT1_IRQHandler</a> from startup_at32f403a_407.o(.text) referenced from startup_at32f403a_407.o(RESET)
 <LI><a href="#[14]">EXINT2_IRQHandler</a> from startup_at32f403a_407.o(.text) referenced from startup_at32f403a_407.o(RESET)
 <LI><a href="#[15]">EXINT3_IRQHandler</a> from startup_at32f403a_407.o(.text) referenced from startup_at32f403a_407.o(RESET)
 <LI><a href="#[16]">EXINT4_IRQHandler</a> from startup_at32f403a_407.o(.text) referenced from startup_at32f403a_407.o(RESET)
 <LI><a href="#[23]">EXINT9_5_IRQHandler</a> from startup_at32f403a_407.o(.text) referenced from startup_at32f403a_407.o(RESET)
 <LI><a href="#[10]">FLASH_IRQHandler</a> from startup_at32f403a_407.o(.text) referenced from startup_at32f403a_407.o(RESET)
 <LI><a href="#[4]">HardFault_Handler</a> from at32f403a_407_int.o(i.HardFault_Handler) referenced from startup_at32f403a_407.o(RESET)
 <LI><a href="#[2c]">I2C1_ERR_IRQHandler</a> from startup_at32f403a_407.o(.text) referenced from startup_at32f403a_407.o(RESET)
 <LI><a href="#[2b]">I2C1_EVT_IRQHandler</a> from startup_at32f403a_407.o(.text) referenced from startup_at32f403a_407.o(RESET)
 <LI><a href="#[2e]">I2C2_ERR_IRQHandler</a> from startup_at32f403a_407.o(.text) referenced from startup_at32f403a_407.o(RESET)
 <LI><a href="#[2d]">I2C2_EVT_IRQHandler</a> from startup_at32f403a_407.o(.text) referenced from startup_at32f403a_407.o(RESET)
 <LI><a href="#[4a]">I2C3_ERR_IRQHandler</a> from startup_at32f403a_407.o(.text) referenced from startup_at32f403a_407.o(RESET)
 <LI><a href="#[49]">I2C3_EVT_IRQHandler</a> from startup_at32f403a_407.o(.text) referenced from startup_at32f403a_407.o(RESET)
 <LI><a href="#[5]">MemManage_Handler</a> from at32f403a_407_int.o(i.MemManage_Handler) referenced from startup_at32f403a_407.o(RESET)
 <LI><a href="#[3]">NMI_Handler</a> from at32f403a_407_int.o(i.NMI_Handler) referenced from startup_at32f403a_407.o(RESET)
 <LI><a href="#[d]">PVM_IRQHandler</a> from startup_at32f403a_407.o(.text) referenced from startup_at32f403a_407.o(RESET)
 <LI><a href="#[a]">PendSV_Handler</a> from at32f403a_407_int.o(i.PendSV_Handler) referenced from startup_at32f403a_407.o(RESET)
 <LI><a href="#[35]">RTCAlarm_IRQHandler</a> from startup_at32f403a_407.o(.text) referenced from startup_at32f403a_407.o(RESET)
 <LI><a href="#[f]">RTC_IRQHandler</a> from startup_at32f403a_407.o(.text) referenced from startup_at32f403a_407.o(RESET)
 <LI><a href="#[2]">Reset_Handler</a> from startup_at32f403a_407.o(.text) referenced from startup_at32f403a_407.o(RESET)
 <LI><a href="#[3d]">SDIO1_IRQHandler</a> from startup_at32f403a_407.o(.text) referenced from startup_at32f403a_407.o(RESET)
 <LI><a href="#[48]">SDIO2_IRQHandler</a> from startup_at32f403a_407.o(.text) referenced from startup_at32f403a_407.o(RESET)
 <LI><a href="#[2f]">SPI1_IRQHandler</a> from startup_at32f403a_407.o(.text) referenced from startup_at32f403a_407.o(RESET)
 <LI><a href="#[30]">SPI2_I2S2EXT_IRQHandler</a> from startup_at32f403a_407.o(.text) referenced from startup_at32f403a_407.o(RESET)
 <LI><a href="#[3f]">SPI3_I2S3EXT_IRQHandler</a> from startup_at32f403a_407.o(.text) referenced from startup_at32f403a_407.o(RESET)
 <LI><a href="#[4b]">SPI4_IRQHandler</a> from startup_at32f403a_407.o(.text) referenced from startup_at32f403a_407.o(RESET)
 <LI><a href="#[8]">SVC_Handler</a> from at32f403a_407_int.o(i.SVC_Handler) referenced from startup_at32f403a_407.o(RESET)
 <LI><a href="#[b]">SysTick_Handler</a> from at32f403a_407_int.o(i.SysTick_Handler) referenced from startup_at32f403a_407.o(RESET)
 <LI><a href="#[59]">SystemInit</a> from system_at32f403a_407.o(i.SystemInit) referenced from startup_at32f403a_407.o(.text)
 <LI><a href="#[e]">TAMPER_IRQHandler</a> from startup_at32f403a_407.o(.text) referenced from startup_at32f403a_407.o(RESET)
 <LI><a href="#[24]">TMR1_BRK_TMR9_IRQHandler</a> from startup_at32f403a_407.o(.text) referenced from startup_at32f403a_407.o(RESET)
 <LI><a href="#[27]">TMR1_CH_IRQHandler</a> from startup_at32f403a_407.o(.text) referenced from startup_at32f403a_407.o(RESET)
 <LI><a href="#[25]">TMR1_OVF_TMR10_IRQHandler</a> from startup_at32f403a_407.o(.text) referenced from startup_at32f403a_407.o(RESET)
 <LI><a href="#[26]">TMR1_TRG_HALL_TMR11_IRQHandler</a> from startup_at32f403a_407.o(.text) referenced from startup_at32f403a_407.o(RESET)
 <LI><a href="#[28]">TMR2_GLOBAL_IRQHandler</a> from startup_at32f403a_407.o(.text) referenced from startup_at32f403a_407.o(RESET)
 <LI><a href="#[29]">TMR3_GLOBAL_IRQHandler</a> from startup_at32f403a_407.o(.text) referenced from startup_at32f403a_407.o(RESET)
 <LI><a href="#[2a]">TMR4_GLOBAL_IRQHandler</a> from startup_at32f403a_407.o(.text) referenced from startup_at32f403a_407.o(RESET)
 <LI><a href="#[3e]">TMR5_GLOBAL_IRQHandler</a> from at32f403a_407_int.o(i.TMR5_GLOBAL_IRQHandler) referenced from startup_at32f403a_407.o(RESET)
 <LI><a href="#[42]">TMR6_GLOBAL_IRQHandler</a> from startup_at32f403a_407.o(.text) referenced from startup_at32f403a_407.o(RESET)
 <LI><a href="#[43]">TMR7_GLOBAL_IRQHandler</a> from startup_at32f403a_407.o(.text) referenced from startup_at32f403a_407.o(RESET)
 <LI><a href="#[37]">TMR8_BRK_TMR12_IRQHandler</a> from startup_at32f403a_407.o(.text) referenced from startup_at32f403a_407.o(RESET)
 <LI><a href="#[3a]">TMR8_CH_IRQHandler</a> from startup_at32f403a_407.o(.text) referenced from startup_at32f403a_407.o(RESET)
 <LI><a href="#[38]">TMR8_OVF_TMR13_IRQHandler</a> from startup_at32f403a_407.o(.text) referenced from startup_at32f403a_407.o(RESET)
 <LI><a href="#[39]">TMR8_TRG_HALL_TMR14_IRQHandler</a> from startup_at32f403a_407.o(.text) referenced from startup_at32f403a_407.o(RESET)
 <LI><a href="#[40]">UART4_IRQHandler</a> from startup_at32f403a_407.o(.text) referenced from startup_at32f403a_407.o(RESET)
 <LI><a href="#[41]">UART5_IRQHandler</a> from startup_at32f403a_407.o(.text) referenced from startup_at32f403a_407.o(RESET)
 <LI><a href="#[55]">UART7_IRQHandler</a> from startup_at32f403a_407.o(.text) referenced from startup_at32f403a_407.o(RESET)
 <LI><a href="#[56]">UART8_IRQHandler</a> from startup_at32f403a_407.o(.text) referenced from startup_at32f403a_407.o(RESET)
 <LI><a href="#[31]">USART1_IRQHandler</a> from startup_at32f403a_407.o(.text) referenced from startup_at32f403a_407.o(RESET)
 <LI><a href="#[32]">USART2_IRQHandler</a> from startup_at32f403a_407.o(.text) referenced from startup_at32f403a_407.o(RESET)
 <LI><a href="#[33]">USART3_IRQHandler</a> from startup_at32f403a_407.o(.text) referenced from startup_at32f403a_407.o(RESET)
 <LI><a href="#[54]">USART6_IRQHandler</a> from startup_at32f403a_407.o(.text) referenced from startup_at32f403a_407.o(RESET)
 <LI><a href="#[36]">USBFSWakeUp_IRQHandler</a> from startup_at32f403a_407.o(.text) referenced from startup_at32f403a_407.o(RESET)
 <LI><a href="#[1f]">USBFS_H_CAN1_TX_IRQHandler</a> from startup_at32f403a_407.o(.text) referenced from startup_at32f403a_407.o(RESET)
 <LI><a href="#[20]">USBFS_L_CAN1_RX0_IRQHandler</a> from startup_at32f403a_407.o(.text) referenced from startup_at32f403a_407.o(RESET)
 <LI><a href="#[51]">USBFS_MAPH_IRQHandler</a> from startup_at32f403a_407.o(.text) referenced from startup_at32f403a_407.o(RESET)
 <LI><a href="#[52]">USBFS_MAPL_IRQHandler</a> from startup_at32f403a_407.o(.text) referenced from startup_at32f403a_407.o(RESET)
 <LI><a href="#[7]">UsageFault_Handler</a> from at32f403a_407_int.o(i.UsageFault_Handler) referenced from startup_at32f403a_407.o(RESET)
 <LI><a href="#[c]">WWDT_IRQHandler</a> from startup_at32f403a_407.o(.text) referenced from startup_at32f403a_407.o(RESET)
 <LI><a href="#[3c]">XMC_IRQHandler</a> from startup_at32f403a_407.o(.text) referenced from startup_at32f403a_407.o(RESET)
 <LI><a href="#[5d]">__main</a> from __main.o(!!!main) referenced from startup_at32f403a_407.o(.text)
 <LI><a href="#[5c]">_printf_input_char</a> from _printf_char_common.o(.text) referenced from _printf_char_common.o(.text)
 <LI><a href="#[5b]">fputc</a> from at32f403a_407_board.o(i.fputc) referenced from _printf_char_file.o(.text)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[5d]"></a>__main</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, __main.o(!!!main))
<BR><BR>[Calls]<UL><LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry
</UL>
<BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_startup_check
</UL>

<P><STRONG><a name="[5e]"></a>__scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter))
<BR><BR>[Called By]<UL><LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main
</UL>

<P><STRONG><a name="[60]"></a>__scatterload_rt2</STRONG> (Thumb, 44 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry
</UL>

<P><STRONG><a name="[c3]"></a>__scatterload_rt2_thumb_only</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)

<P><STRONG><a name="[c4]"></a>__scatterload_null</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)

<P><STRONG><a name="[61]"></a>__scatterload_copy</STRONG> (Thumb, 26 bytes, Stack size unknown bytes, __scatter_copy.o(!!handler_copy), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload_copy
</UL>
<BR>[Called By]<UL><LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload_copy
</UL>

<P><STRONG><a name="[62]"></a>_printf_d</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_d.o(.ARM.Collect$$_printf_percent$$00000009))
<BR><BR>[Stack]<UL><LI>Max Depth = 56 + Unknown Stack Size
<LI>Call Chain = _printf_d &rArr; _printf_int_dec &rArr; _printf_int_common
</UL>
<BR>[Calls]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_dec
</UL>

<P><STRONG><a name="[77]"></a>_printf_percent</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__printf
</UL>

<P><STRONG><a name="[c5]"></a>_printf_percent_end</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017))

<P><STRONG><a name="[69]"></a>__rt_lib_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit.o(.ARM.Collect$$libinit$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_li
</UL>

<P><STRONG><a name="[64]"></a>__rt_lib_init_fp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000001))
<BR><BR>[Calls]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_init
</UL>

<P><STRONG><a name="[c6]"></a>__rt_lib_init_alloca_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000002E))

<P><STRONG><a name="[c7]"></a>__rt_lib_init_argv_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000002C))

<P><STRONG><a name="[c8]"></a>__rt_lib_init_atexit_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001B))

<P><STRONG><a name="[c9]"></a>__rt_lib_init_clock_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000021))

<P><STRONG><a name="[ca]"></a>__rt_lib_init_cpp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000032))

<P><STRONG><a name="[cb]"></a>__rt_lib_init_exceptions_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000030))

<P><STRONG><a name="[cc]"></a>__rt_lib_init_fp_trap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001F))

<P><STRONG><a name="[cd]"></a>__rt_lib_init_getenv_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000023))

<P><STRONG><a name="[ce]"></a>__rt_lib_init_heap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000A))

<P><STRONG><a name="[cf]"></a>__rt_lib_init_lc_collate_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000011))

<P><STRONG><a name="[d0]"></a>__rt_lib_init_lc_ctype_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000013))

<P><STRONG><a name="[d1]"></a>__rt_lib_init_lc_monetary_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000015))

<P><STRONG><a name="[d2]"></a>__rt_lib_init_lc_numeric_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000017))

<P><STRONG><a name="[d3]"></a>__rt_lib_init_lc_time_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000019))

<P><STRONG><a name="[d4]"></a>__rt_lib_init_preinit_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000004))

<P><STRONG><a name="[d5]"></a>__rt_lib_init_rand_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000E))

<P><STRONG><a name="[d6]"></a>__rt_lib_init_return</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000033))

<P><STRONG><a name="[d7]"></a>__rt_lib_init_signal_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001D))

<P><STRONG><a name="[d8]"></a>__rt_lib_init_stdio_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000025))

<P><STRONG><a name="[d9]"></a>__rt_lib_init_user_alloc_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000C))

<P><STRONG><a name="[6e]"></a>__rt_lib_shutdown</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown.o(.ARM.Collect$$libshutdown$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit_ls
</UL>

<P><STRONG><a name="[da]"></a>__rt_lib_shutdown_fp_trap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000006))

<P><STRONG><a name="[db]"></a>__rt_lib_shutdown_heap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E))

<P><STRONG><a name="[dc]"></a>__rt_lib_shutdown_return</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F))

<P><STRONG><a name="[dd]"></a>__rt_lib_shutdown_signal_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000009))

<P><STRONG><a name="[de]"></a>__rt_lib_shutdown_stdio_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000003))

<P><STRONG><a name="[df]"></a>__rt_lib_shutdown_user_alloc_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000B))

<P><STRONG><a name="[5f]"></a>__rt_entry</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry.o(.ARM.Collect$$rtentry$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload_rt2
<LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main
</UL>

<P><STRONG><a name="[e0]"></a>__rt_entry_presh_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$00000002))

<P><STRONG><a name="[66]"></a>__rt_entry_sh</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry4.o(.ARM.Collect$$rtentry$$00000004))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = __rt_entry_sh &rArr; __user_setup_stackheap
</UL>
<BR>[Calls]<UL><LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[68]"></a>__rt_entry_li</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000A))
<BR><BR>[Calls]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init
</UL>

<P><STRONG><a name="[e1]"></a>__rt_entry_postsh_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$00000009))

<P><STRONG><a name="[6a]"></a>__rt_entry_main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000D))
<BR><BR>[Stack]<UL><LI>Max Depth = 168 + Unknown Stack Size
<LI>Call Chain = __rt_entry_main &rArr; main &rArr; selftest_startup_check &rArr; selftest_system_clock_config &rArr; selftest_fail_handle &rArr; __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exit
</UL>

<P><STRONG><a name="[e2]"></a>__rt_entry_postli_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000C))

<P><STRONG><a name="[7e]"></a>__rt_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit.o(.ARM.Collect$$rtexit$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exit
</UL>

<P><STRONG><a name="[6d]"></a>__rt_exit_ls</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000003))
<BR><BR>[Calls]<UL><LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_shutdown
</UL>

<P><STRONG><a name="[e3]"></a>__rt_exit_prels_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000002))

<P><STRONG><a name="[6f]"></a>__rt_exit_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sys_exit
</UL>

<P><STRONG><a name="[71]"></a>selftest_cpu_runtime_test</STRONG> (Thumb, 464 bytes, Stack size 0 bytes, at32_selftest_cpurun_keil.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 128 + Unknown Stack Size
<LI>Call Chain = selftest_cpu_runtime_test &rArr; selftest_fail_handle &rArr; __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_fail_handle
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_runtime_check
</UL>

<P><STRONG><a name="[73]"></a>selftest_cpu_startup_test</STRONG> (Thumb, 640 bytes, Stack size 0 bytes, at32_selftest_cpustart_keil.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 128 + Unknown Stack Size
<LI>Call Chain = selftest_cpu_startup_test &rArr; selftest_fail_handle &rArr; __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_fail_handle
</UL>
<BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_startup_check
</UL>

<P><STRONG><a name="[b4]"></a>selftest_full_ram_test</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, at32_selftest_ram_keil.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_startup_check
</UL>

<P><STRONG><a name="[b0]"></a>selftest_ram_step_implement</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, at32_selftest_ram_keil.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_sampling_ram_test
</UL>

<P><STRONG><a name="[2]"></a>Reset_Handler</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, startup_at32f403a_407.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(RESET)
</UL>
<P><STRONG><a name="[50]"></a>ACC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32f403a_407.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ACC_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ACC_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(RESET)
</UL>
<P><STRONG><a name="[1e]"></a>ADC1_2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32f403a_407.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(RESET)
</UL>
<P><STRONG><a name="[3b]"></a>ADC3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32f403a_407.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(RESET)
</UL>
<P><STRONG><a name="[21]"></a>CAN1_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32f403a_407.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(RESET)
</UL>
<P><STRONG><a name="[22]"></a>CAN1_SE_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32f403a_407.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(RESET)
</UL>
<P><STRONG><a name="[4d]"></a>CAN2_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32f403a_407.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(RESET)
</UL>
<P><STRONG><a name="[4e]"></a>CAN2_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32f403a_407.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(RESET)
</UL>
<P><STRONG><a name="[4f]"></a>CAN2_SE_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32f403a_407.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(RESET)
</UL>
<P><STRONG><a name="[4c]"></a>CAN2_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32f403a_407.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(RESET)
</UL>
<P><STRONG><a name="[11]"></a>CRM_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32f403a_407.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(RESET)
</UL>
<P><STRONG><a name="[17]"></a>DMA1_Channel1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32f403a_407.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(RESET)
</UL>
<P><STRONG><a name="[18]"></a>DMA1_Channel2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32f403a_407.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(RESET)
</UL>
<P><STRONG><a name="[19]"></a>DMA1_Channel3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32f403a_407.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(RESET)
</UL>
<P><STRONG><a name="[1a]"></a>DMA1_Channel4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32f403a_407.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(RESET)
</UL>
<P><STRONG><a name="[1b]"></a>DMA1_Channel5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32f403a_407.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(RESET)
</UL>
<P><STRONG><a name="[1c]"></a>DMA1_Channel6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32f403a_407.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(RESET)
</UL>
<P><STRONG><a name="[1d]"></a>DMA1_Channel7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32f403a_407.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(RESET)
</UL>
<P><STRONG><a name="[44]"></a>DMA2_Channel1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32f403a_407.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(RESET)
</UL>
<P><STRONG><a name="[45]"></a>DMA2_Channel2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32f403a_407.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(RESET)
</UL>
<P><STRONG><a name="[46]"></a>DMA2_Channel3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32f403a_407.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(RESET)
</UL>
<P><STRONG><a name="[47]"></a>DMA2_Channel4_5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32f403a_407.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(RESET)
</UL>
<P><STRONG><a name="[53]"></a>DMA2_Channel6_7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32f403a_407.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(RESET)
</UL>
<P><STRONG><a name="[57]"></a>EMAC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32f403a_407.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(RESET)
</UL>
<P><STRONG><a name="[58]"></a>EMAC_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32f403a_407.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(RESET)
</UL>
<P><STRONG><a name="[12]"></a>EXINT0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32f403a_407.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(RESET)
</UL>
<P><STRONG><a name="[34]"></a>EXINT15_10_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32f403a_407.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(RESET)
</UL>
<P><STRONG><a name="[13]"></a>EXINT1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32f403a_407.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(RESET)
</UL>
<P><STRONG><a name="[14]"></a>EXINT2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32f403a_407.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(RESET)
</UL>
<P><STRONG><a name="[15]"></a>EXINT3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32f403a_407.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(RESET)
</UL>
<P><STRONG><a name="[16]"></a>EXINT4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32f403a_407.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(RESET)
</UL>
<P><STRONG><a name="[23]"></a>EXINT9_5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32f403a_407.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(RESET)
</UL>
<P><STRONG><a name="[10]"></a>FLASH_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32f403a_407.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(RESET)
</UL>
<P><STRONG><a name="[2c]"></a>I2C1_ERR_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32f403a_407.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(RESET)
</UL>
<P><STRONG><a name="[2b]"></a>I2C1_EVT_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32f403a_407.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(RESET)
</UL>
<P><STRONG><a name="[2e]"></a>I2C2_ERR_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32f403a_407.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(RESET)
</UL>
<P><STRONG><a name="[2d]"></a>I2C2_EVT_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32f403a_407.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(RESET)
</UL>
<P><STRONG><a name="[4a]"></a>I2C3_ERR_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32f403a_407.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(RESET)
</UL>
<P><STRONG><a name="[49]"></a>I2C3_EVT_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32f403a_407.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(RESET)
</UL>
<P><STRONG><a name="[d]"></a>PVM_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32f403a_407.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(RESET)
</UL>
<P><STRONG><a name="[35]"></a>RTCAlarm_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32f403a_407.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(RESET)
</UL>
<P><STRONG><a name="[f]"></a>RTC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32f403a_407.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(RESET)
</UL>
<P><STRONG><a name="[3d]"></a>SDIO1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32f403a_407.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(RESET)
</UL>
<P><STRONG><a name="[48]"></a>SDIO2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32f403a_407.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(RESET)
</UL>
<P><STRONG><a name="[2f]"></a>SPI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32f403a_407.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(RESET)
</UL>
<P><STRONG><a name="[30]"></a>SPI2_I2S2EXT_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32f403a_407.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(RESET)
</UL>
<P><STRONG><a name="[3f]"></a>SPI3_I2S3EXT_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32f403a_407.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(RESET)
</UL>
<P><STRONG><a name="[4b]"></a>SPI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32f403a_407.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(RESET)
</UL>
<P><STRONG><a name="[e]"></a>TAMPER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32f403a_407.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(RESET)
</UL>
<P><STRONG><a name="[24]"></a>TMR1_BRK_TMR9_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32f403a_407.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(RESET)
</UL>
<P><STRONG><a name="[27]"></a>TMR1_CH_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32f403a_407.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(RESET)
</UL>
<P><STRONG><a name="[25]"></a>TMR1_OVF_TMR10_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32f403a_407.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(RESET)
</UL>
<P><STRONG><a name="[26]"></a>TMR1_TRG_HALL_TMR11_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32f403a_407.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(RESET)
</UL>
<P><STRONG><a name="[28]"></a>TMR2_GLOBAL_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32f403a_407.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(RESET)
</UL>
<P><STRONG><a name="[29]"></a>TMR3_GLOBAL_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32f403a_407.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(RESET)
</UL>
<P><STRONG><a name="[2a]"></a>TMR4_GLOBAL_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32f403a_407.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(RESET)
</UL>
<P><STRONG><a name="[42]"></a>TMR6_GLOBAL_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32f403a_407.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(RESET)
</UL>
<P><STRONG><a name="[43]"></a>TMR7_GLOBAL_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32f403a_407.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(RESET)
</UL>
<P><STRONG><a name="[37]"></a>TMR8_BRK_TMR12_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32f403a_407.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(RESET)
</UL>
<P><STRONG><a name="[3a]"></a>TMR8_CH_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32f403a_407.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(RESET)
</UL>
<P><STRONG><a name="[38]"></a>TMR8_OVF_TMR13_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32f403a_407.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(RESET)
</UL>
<P><STRONG><a name="[39]"></a>TMR8_TRG_HALL_TMR14_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32f403a_407.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(RESET)
</UL>
<P><STRONG><a name="[40]"></a>UART4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32f403a_407.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(RESET)
</UL>
<P><STRONG><a name="[41]"></a>UART5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32f403a_407.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(RESET)
</UL>
<P><STRONG><a name="[55]"></a>UART7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32f403a_407.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(RESET)
</UL>
<P><STRONG><a name="[56]"></a>UART8_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32f403a_407.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(RESET)
</UL>
<P><STRONG><a name="[31]"></a>USART1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32f403a_407.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(RESET)
</UL>
<P><STRONG><a name="[32]"></a>USART2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32f403a_407.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(RESET)
</UL>
<P><STRONG><a name="[33]"></a>USART3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32f403a_407.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(RESET)
</UL>
<P><STRONG><a name="[54]"></a>USART6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32f403a_407.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(RESET)
</UL>
<P><STRONG><a name="[36]"></a>USBFSWakeUp_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32f403a_407.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(RESET)
</UL>
<P><STRONG><a name="[1f]"></a>USBFS_H_CAN1_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32f403a_407.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(RESET)
</UL>
<P><STRONG><a name="[20]"></a>USBFS_L_CAN1_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32f403a_407.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(RESET)
</UL>
<P><STRONG><a name="[51]"></a>USBFS_MAPH_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32f403a_407.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(RESET)
</UL>
<P><STRONG><a name="[52]"></a>USBFS_MAPL_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32f403a_407.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(RESET)
</UL>
<P><STRONG><a name="[c]"></a>WWDT_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32f403a_407.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(RESET)
</UL>
<P><STRONG><a name="[3c]"></a>XMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_at32f403a_407.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(RESET)
</UL>
<P><STRONG><a name="[7d]"></a>__user_initial_stackheap</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, startup_at32f403a_407.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[e4]"></a>__use_no_semihosting</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, use_no_semi_2.o(.text), UNUSED)

<P><STRONG><a name="[74]"></a>__2printf</STRONG> (Thumb, 20 bytes, Stack size 24 bytes, noretval__2printf.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 128 + Unknown Stack Size
<LI>Call Chain = __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char_file
</UL>
<BR>[Called By]<UL><LI><a href="#[b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_runtime_check
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_fail_handle
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_system_clock_config
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;$Super$$main
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_watchdog_test
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_startup_check
</UL>

<P><STRONG><a name="[76]"></a>__printf</STRONG> (Thumb, 104 bytes, Stack size 24 bytes, __printf.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24 + Unknown Stack Size
<LI>Call Chain = __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_percent
</UL>
<BR>[Called By]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char_common
</UL>

<P><STRONG><a name="[63]"></a>_printf_int_dec</STRONG> (Thumb, 104 bytes, Stack size 24 bytes, _printf_dec.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = _printf_int_dec &rArr; _printf_int_common
</UL>
<BR>[Calls]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_common
</UL>
<BR>[Called By]<UL><LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_d
</UL>

<P><STRONG><a name="[79]"></a>putc</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, putc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = putc &rArr; fputc
</UL>
<BR>[Calls]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fputc
</UL>
<BR>[Called By]<UL><LI><a href="#[b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_runtime_check
</UL>

<P><STRONG><a name="[e5]"></a>__use_two_region_memory</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[e6]"></a>__rt_heap_escrow$2region</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[e7]"></a>__rt_heap_expand$2region</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[e8]"></a>__I$use$semihosting</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, use_no_semi.o(.text), UNUSED)

<P><STRONG><a name="[e9]"></a>__use_no_semihosting_swi</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, use_no_semi.o(.text), UNUSED)

<P><STRONG><a name="[78]"></a>_printf_int_common</STRONG> (Thumb, 178 bytes, Stack size 32 bytes, _printf_intcommon.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = _printf_int_common
</UL>
<BR>[Called By]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_dec
</UL>

<P><STRONG><a name="[75]"></a>_printf_char_file</STRONG> (Thumb, 32 bytes, Stack size 16 bytes, _printf_char_file.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 104 + Unknown Stack Size
<LI>Call Chain = _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ferror
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char_common
</UL>
<BR>[Called By]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>

<P><STRONG><a name="[7a]"></a>_printf_char_common</STRONG> (Thumb, 32 bytes, Stack size 64 bytes, _printf_char_common.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88 + Unknown Stack Size
<LI>Call Chain = _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__printf
</UL>
<BR>[Called By]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char_file
</UL>

<P><STRONG><a name="[7b]"></a>ferror</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, ferror.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char_file
</UL>

<P><STRONG><a name="[67]"></a>__user_setup_stackheap</STRONG> (Thumb, 74 bytes, Stack size 8 bytes, sys_stackheap_outer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = __user_setup_stackheap
</UL>
<BR>[Calls]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_initial_stackheap
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_perproc_libspace
</UL>
<BR>[Called By]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_sh
</UL>

<P><STRONG><a name="[6c]"></a>exit</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, exit.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit
</UL>
<BR>[Called By]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_main
</UL>

<P><STRONG><a name="[ea]"></a>__user_libspace</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, libspace.o(.text), UNUSED)

<P><STRONG><a name="[7c]"></a>__user_perproc_libspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, libspace.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[eb]"></a>__user_perthread_libspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, libspace.o(.text), UNUSED)

<P><STRONG><a name="[6b]"></a>main</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, at32_selftest_startup.o(i.$Sub$$main))
<BR><BR>[Stack]<UL><LI>Max Depth = 168 + Unknown Stack Size
<LI>Call Chain = main &rArr; selftest_startup_check &rArr; selftest_system_clock_config &rArr; selftest_fail_handle &rArr; __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;$Super$$main
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_startup_check
</UL>
<BR>[Called By]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_main
</UL>

<P><STRONG><a name="[6]"></a>BusFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, at32f403a_407_int.o(i.BusFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(RESET)
</UL>
<P><STRONG><a name="[9]"></a>DebugMon_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, at32f403a_407_int.o(i.DebugMon_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(RESET)
</UL>
<P><STRONG><a name="[4]"></a>HardFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, at32f403a_407_int.o(i.HardFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(RESET)
</UL>
<P><STRONG><a name="[5]"></a>MemManage_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, at32f403a_407_int.o(i.MemManage_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(RESET)
</UL>
<P><STRONG><a name="[3]"></a>NMI_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, at32f403a_407_int.o(i.NMI_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(RESET)
</UL>
<P><STRONG><a name="[a]"></a>PendSV_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, at32f403a_407_int.o(i.PendSV_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(RESET)
</UL>
<P><STRONG><a name="[8]"></a>SVC_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, at32f403a_407_int.o(i.SVC_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(RESET)
</UL>
<P><STRONG><a name="[b]"></a>SysTick_Handler</STRONG> (Thumb, 218 bytes, Stack size 16 bytes, at32f403a_407_int.o(i.SysTick_Handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 144 + Unknown Stack Size
<LI>Call Chain = SysTick_Handler &rArr; selftest_fail_handle &rArr; __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_sampling_ram_test
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_fail_handle
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;systick_inc
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;putc
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(RESET)
</UL>
<P><STRONG><a name="[59]"></a>SystemInit</STRONG> (Thumb, 102 bytes, Stack size 0 bytes, system_at32f403a_407.o(i.SystemInit))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(.text)
</UL>
<P><STRONG><a name="[3e]"></a>TMR5_GLOBAL_IRQHandler</STRONG> (Thumb, 124 bytes, Stack size 8 bytes, at32f403a_407_int.o(i.TMR5_GLOBAL_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TMR5_GLOBAL_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_flag_get
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_flag_clear
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(RESET)
</UL>
<P><STRONG><a name="[7]"></a>UsageFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, at32f403a_407_int.o(i.UsageFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_at32f403a_407.o(RESET)
</UL>
<P><STRONG><a name="[70]"></a>_sys_exit</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, at32f403a_407_board.o(i._sys_exit))
<BR><BR>[Called By]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit_exit
</UL>

<P><STRONG><a name="[b3]"></a>control_flow_check_point</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, at32_selftest_startup.o(i.control_flow_check_point))
<BR><BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_startup_check
</UL>

<P><STRONG><a name="[a3]"></a>crc32_fsl_continuous</STRONG> (Thumb, 148 bytes, Stack size 20 bytes, crc32.o(i.crc32_fsl_continuous))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = crc32_fsl_continuous
</UL>
<BR>[Called By]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_crc_runtime_test
</UL>

<P><STRONG><a name="[b2]"></a>crc32_fsl_single</STRONG> (Thumb, 40 bytes, Stack size 20 bytes, crc32.o(i.crc32_fsl_single))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = crc32_fsl_single
</UL>
<BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_startup_check
</UL>

<P><STRONG><a name="[b8]"></a>crm_ahb_div_set</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, at32f403a_407_crm.o(i.crm_ahb_div_set))
<BR><BR>[Called By]<UL><LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_system_clock_config
</UL>

<P><STRONG><a name="[ba]"></a>crm_apb1_div_set</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, at32f403a_407_crm.o(i.crm_apb1_div_set))
<BR><BR>[Called By]<UL><LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_system_clock_config
</UL>

<P><STRONG><a name="[b9]"></a>crm_apb2_div_set</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, at32f403a_407_crm.o(i.crm_apb2_div_set))
<BR><BR>[Called By]<UL><LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_system_clock_config
</UL>

<P><STRONG><a name="[bb]"></a>crm_auto_step_mode_enable</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, at32f403a_407_crm.o(i.crm_auto_step_mode_enable))
<BR><BR>[Called By]<UL><LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_system_clock_config
</UL>

<P><STRONG><a name="[a1]"></a>crm_clock_failure_detection_enable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, at32f403a_407_crm.o(i.crm_clock_failure_detection_enable))
<BR><BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_clock_startup_test
</UL>

<P><STRONG><a name="[9f]"></a>crm_clock_source_enable</STRONG> (Thumb, 90 bytes, Stack size 0 bytes, at32f403a_407_crm.o(i.crm_clock_source_enable))
<BR><BR>[Called By]<UL><LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_system_clock_config
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_clock_startup_test
</UL>

<P><STRONG><a name="[85]"></a>crm_clocks_freq_get</STRONG> (Thumb, 268 bytes, Stack size 24 bytes, at32f403a_407_crm.o(i.crm_clocks_freq_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = crm_clocks_freq_get
</UL>
<BR>[Calls]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crm_sysclk_switch_status_get
</UL>
<BR>[Called By]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_init
</UL>

<P><STRONG><a name="[bc]"></a>crm_flag_clear</STRONG> (Thumb, 190 bytes, Stack size 0 bytes, at32f403a_407_crm.o(i.crm_flag_clear))
<BR><BR>[Called By]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_watchdog_test
</UL>

<P><STRONG><a name="[88]"></a>crm_flag_get</STRONG> (Thumb, 42 bytes, Stack size 8 bytes, at32f403a_407_crm.o(i.crm_flag_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = crm_flag_get
</UL>
<BR>[Called By]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crm_hext_stable_wait
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_system_clock_config
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_clock_startup_test
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_watchdog_test
</UL>

<P><STRONG><a name="[b7]"></a>crm_hext_clock_div_set</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, at32f403a_407_crm.o(i.crm_hext_clock_div_set))
<BR><BR>[Called By]<UL><LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_system_clock_config
</UL>

<P><STRONG><a name="[87]"></a>crm_hext_stable_wait</STRONG> (Thumb, 46 bytes, Stack size 12 bytes, at32f403a_407_crm.o(i.crm_hext_stable_wait))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = crm_hext_stable_wait &rArr; crm_flag_get
</UL>
<BR>[Calls]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crm_flag_get
</UL>
<BR>[Called By]<UL><LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_system_clock_config
</UL>

<P><STRONG><a name="[91]"></a>crm_periph_clock_enable</STRONG> (Thumb, 62 bytes, Stack size 8 bytes, at32f403a_407_crm.o(i.crm_periph_clock_enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = crm_periph_clock_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_print_init
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_runtime_init
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_clock_cross_measurement_config
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_watchdog_test
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_startup_check
</UL>

<P><STRONG><a name="[b6]"></a>crm_pll_config</STRONG> (Thumb, 104 bytes, Stack size 8 bytes, at32f403a_407_crm.o(i.crm_pll_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = crm_pll_config
</UL>
<BR>[Called By]<UL><LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_system_clock_config
</UL>

<P><STRONG><a name="[b5]"></a>crm_reset</STRONG> (Thumb, 82 bytes, Stack size 0 bytes, at32f403a_407_crm.o(i.crm_reset))
<BR><BR>[Called By]<UL><LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_system_clock_config
</UL>

<P><STRONG><a name="[9d]"></a>crm_sysclk_switch</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, at32f403a_407_crm.o(i.crm_sysclk_switch))
<BR><BR>[Called By]<UL><LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_system_clock_config
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_clock_runtime_test
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_clock_startup_test
</UL>

<P><STRONG><a name="[86]"></a>crm_sysclk_switch_status_get</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, at32f403a_407_crm.o(i.crm_sysclk_switch_status_get))
<BR><BR>[Called By]<UL><LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_core_clock_update
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_system_clock_config
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crm_clocks_freq_get
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_clock_startup_test
</UL>

<P><STRONG><a name="[a9]"></a>debug_periph_mode_set</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, at32f403a_407_debug.o(i.debug_periph_mode_set))
<BR><BR>[Called By]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_runtime_init
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_watchdog_test
</UL>

<P><STRONG><a name="[5b]"></a>fputc</STRONG> (Thumb, 32 bytes, Stack size 16 bytes, at32f403a_407_board.o(i.fputc))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = fputc
</UL>
<BR>[Calls]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_flag_get
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_data_transmit
</UL>
<BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;putc
</UL>
<BR>[Address Reference Count : 1]<UL><LI> _printf_char_file.o(.text)
</UL>
<P><STRONG><a name="[be]"></a>gpio_default_para_init</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, at32f403a_407_gpio.o(i.gpio_default_para_init))
<BR><BR>[Called By]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_print_init
</UL>

<P><STRONG><a name="[bf]"></a>gpio_init</STRONG> (Thumb, 168 bytes, Stack size 20 bytes, at32f403a_407_gpio.o(i.gpio_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = gpio_init
</UL>
<BR>[Called By]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_print_init
</UL>

<P><STRONG><a name="[97]"></a>gpio_pin_remap_config</STRONG> (Thumb, 70 bytes, Stack size 20 bytes, at32f403a_407_gpio.o(i.gpio_pin_remap_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = gpio_pin_remap_config
</UL>
<BR>[Called By]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_clock_cross_measurement_config
</UL>

<P><STRONG><a name="[80]"></a>$Super$$main</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, main.o(i.main))
<BR><BR>[Stack]<UL><LI>Max Depth = 144 + Unknown Stack Size
<LI>Call Chain = $Super$$main &rArr; selftest_runtime_check &rArr; selftest_cpu_runtime_test &rArr; selftest_fail_handle &rArr; __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_print_init
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_runtime_init
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_runtime_check
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_priority_group_config
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_system_clock_config
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[92]"></a>nvic_irq_enable</STRONG> (Thumb, 190 bytes, Stack size 32 bytes, at32f403a_407_misc.o(i.nvic_irq_enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = nvic_irq_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_clock_cross_measurement_config
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_startup_check
</UL>

<P><STRONG><a name="[8b]"></a>nvic_priority_group_config</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, at32f403a_407_misc.o(i.nvic_priority_group_config))
<BR><BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;$Super$$main
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_startup_check
</UL>

<P><STRONG><a name="[90]"></a>selftest_clock_cross_measurement_config</STRONG> (Thumb, 140 bytes, Stack size 8 bytes, main.o(i.selftest_clock_cross_measurement_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = selftest_clock_cross_measurement_config &rArr; nvic_irq_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_repetition_counter_set
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_interrupt_enable
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_input_default_para_init
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_input_channel_init
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_counter_enable
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_cnt_dir_set
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_clock_source_div_set
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tmr_base_init
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_irq_enable
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_pin_remap_config
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crm_periph_clock_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_clock_startup_test
</UL>

<P><STRONG><a name="[9c]"></a>selftest_clock_runtime_test</STRONG> (Thumb, 106 bytes, Stack size 16 bytes, at32_selftest_clock.o(i.selftest_clock_runtime_test))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = selftest_clock_runtime_test
</UL>
<BR>[Calls]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crm_sysclk_switch
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_runtime_check
</UL>

<P><STRONG><a name="[9e]"></a>selftest_clock_startup_test</STRONG> (Thumb, 262 bytes, Stack size 16 bytes, at32_selftest_clock.o(i.selftest_clock_startup_test))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = selftest_clock_startup_test &rArr; selftest_clock_cross_measurement_config &rArr; nvic_irq_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crm_sysclk_switch_status_get
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crm_sysclk_switch
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crm_flag_get
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crm_clock_source_enable
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;systick_get
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_clock_cross_measurement_config
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crm_clock_failure_detection_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_startup_check
</UL>

<P><STRONG><a name="[a2]"></a>selftest_crc_runtime_test</STRONG> (Thumb, 180 bytes, Stack size 8 bytes, at32_selftest_crc.o(i.selftest_crc_runtime_test))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = selftest_crc_runtime_test &rArr; crc32_fsl_continuous
</UL>
<BR>[Calls]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crc32_fsl_continuous
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_runtime_check
</UL>

<P><STRONG><a name="[72]"></a>selftest_fail_handle</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, at32_selftest_startup.o(i.selftest_fail_handle))
<BR><BR>[Stack]<UL><LI>Max Depth = 128 + Unknown Stack Size
<LI>Call Chain = selftest_fail_handle &rArr; __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_core_clock_update
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_reconfigure
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wwdt_counter_set
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wdt_counter_reload
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_cpu_startup_test
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_cpu_runtime_test
<LI><a href="#[b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_runtime_check
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_system_clock_config
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_startup_check
</UL>

<P><STRONG><a name="[8f]"></a>selftest_runtime_check</STRONG> (Thumb, 412 bytes, Stack size 16 bytes, at32_selftest_runtime.o(i.selftest_runtime_check))
<BR><BR>[Stack]<UL><LI>Max Depth = 144 + Unknown Stack Size
<LI>Call Chain = selftest_runtime_check &rArr; selftest_cpu_runtime_test &rArr; selftest_fail_handle &rArr; __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_flag_get
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_cpu_runtime_test
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_fail_handle
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_reconfigure
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_crc_runtime_test
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_clock_runtime_test
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_stack_check
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wwdt_counter_set
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wdt_counter_reload
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;putc
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;$Super$$main
</UL>

<P><STRONG><a name="[8e]"></a>selftest_runtime_init</STRONG> (Thumb, 270 bytes, Stack size 16 bytes, at32_selftest_runtime.o(i.selftest_runtime_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = selftest_runtime_init &rArr; crm_periph_clock_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crm_periph_clock_enable
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_periph_mode_set
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wwdt_window_counter_set
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wwdt_enable
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wwdt_divider_set
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wdt_reload_value_set
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wdt_enable
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wdt_divider_set
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wdt_counter_reload
</UL>
<BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;$Super$$main
</UL>

<P><STRONG><a name="[82]"></a>selftest_sampling_ram_test</STRONG> (Thumb, 114 bytes, Stack size 8 bytes, at32_selftest_ram.o(i.selftest_sampling_ram_test))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = selftest_sampling_ram_test
</UL>
<BR>[Calls]<UL><LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_ram_step_implement
</UL>
<BR>[Called By]<UL><LI><a href="#[b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>

<P><STRONG><a name="[7f]"></a>selftest_startup_check</STRONG> (Thumb, 586 bytes, Stack size 24 bytes, at32_selftest_startup.o(i.selftest_startup_check))
<BR><BR>[Stack]<UL><LI>Max Depth = 160 + Unknown Stack Size
<LI>Call Chain = selftest_startup_check &rArr; selftest_system_clock_config &rArr; selftest_fail_handle &rArr; __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_flag_get
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crc32_fsl_single
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_full_ram_test
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_cpu_startup_test
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_print_init
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_core_clock_update
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_fail_handle
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_priority_group_config
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_irq_enable
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crm_periph_clock_enable
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_reconfigure
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_system_clock_config
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_clock_startup_test
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_watchdog_test
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;control_flow_check_point
<LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[8c]"></a>selftest_system_clock_config</STRONG> (Thumb, 192 bytes, Stack size 8 bytes, main.o(i.selftest_system_clock_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 136 + Unknown Stack Size
<LI>Call Chain = selftest_system_clock_config &rArr; selftest_fail_handle &rArr; __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_core_clock_update
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_fail_handle
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crm_sysclk_switch_status_get
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crm_sysclk_switch
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crm_reset
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crm_pll_config
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crm_hext_stable_wait
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crm_hext_clock_div_set
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crm_flag_get
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crm_clock_source_enable
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crm_auto_step_mode_enable
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crm_apb2_div_set
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crm_apb1_div_set
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crm_ahb_div_set
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;$Super$$main
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_startup_check
</UL>

<P><STRONG><a name="[b1]"></a>selftest_watchdog_test</STRONG> (Thumb, 394 bytes, Stack size 8 bytes, at32_selftest_startup.o(i.selftest_watchdog_test))
<BR><BR>[Stack]<UL><LI>Max Depth = 136 + Unknown Stack Size
<LI>Call Chain = selftest_watchdog_test &rArr; __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_flag_get
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crm_periph_clock_enable
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crm_flag_get
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_periph_mode_set
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crm_flag_clear
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wwdt_window_counter_set
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wwdt_enable
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wwdt_divider_set
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wdt_reload_value_set
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wdt_register_write_enable
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wdt_enable
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wdt_divider_set
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wdt_counter_reload
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_startup_check
</UL>

<P><STRONG><a name="[a4]"></a>system_core_clock_update</STRONG> (Thumb, 222 bytes, Stack size 32 bytes, system_at32f403a_407.o(i.system_core_clock_update))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = system_core_clock_update
</UL>
<BR>[Calls]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crm_sysclk_switch_status_get
</UL>
<BR>[Called By]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_fail_handle
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_system_clock_config
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_startup_check
</UL>

<P><STRONG><a name="[a0]"></a>systick_get</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, main.o(i.systick_get))
<BR><BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_clock_startup_test
</UL>

<P><STRONG><a name="[81]"></a>systick_inc</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, main.o(i.systick_inc))
<BR><BR>[Called By]<UL><LI><a href="#[b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>

<P><STRONG><a name="[93]"></a>tmr_base_init</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, at32f403a_407_tmr.o(i.tmr_base_init))
<BR><BR>[Called By]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_clock_cross_measurement_config
</UL>

<P><STRONG><a name="[95]"></a>tmr_clock_source_div_set</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, at32f403a_407_tmr.o(i.tmr_clock_source_div_set))
<BR><BR>[Called By]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_clock_cross_measurement_config
</UL>

<P><STRONG><a name="[94]"></a>tmr_cnt_dir_set</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, at32f403a_407_tmr.o(i.tmr_cnt_dir_set))
<BR><BR>[Called By]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_clock_cross_measurement_config
</UL>

<P><STRONG><a name="[9b]"></a>tmr_counter_enable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, at32f403a_407_tmr.o(i.tmr_counter_enable))
<BR><BR>[Called By]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_clock_cross_measurement_config
</UL>

<P><STRONG><a name="[84]"></a>tmr_flag_clear</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, at32f403a_407_tmr.o(i.tmr_flag_clear))
<BR><BR>[Called By]<UL><LI><a href="#[3e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TMR5_GLOBAL_IRQHandler
</UL>

<P><STRONG><a name="[83]"></a>tmr_flag_get</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, at32f403a_407_tmr.o(i.tmr_flag_get))
<BR><BR>[Called By]<UL><LI><a href="#[3e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TMR5_GLOBAL_IRQHandler
</UL>

<P><STRONG><a name="[99]"></a>tmr_input_channel_init</STRONG> (Thumb, 270 bytes, Stack size 12 bytes, at32f403a_407_tmr.o(i.tmr_input_channel_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = tmr_input_channel_init
</UL>
<BR>[Called By]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_clock_cross_measurement_config
</UL>

<P><STRONG><a name="[98]"></a>tmr_input_default_para_init</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, at32f403a_407_tmr.o(i.tmr_input_default_para_init))
<BR><BR>[Called By]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_clock_cross_measurement_config
</UL>

<P><STRONG><a name="[9a]"></a>tmr_interrupt_enable</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, at32f403a_407_tmr.o(i.tmr_interrupt_enable))
<BR><BR>[Called By]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_clock_cross_measurement_config
</UL>

<P><STRONG><a name="[96]"></a>tmr_repetition_counter_set</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, at32f403a_407_tmr.o(i.tmr_repetition_counter_set))
<BR><BR>[Called By]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_clock_cross_measurement_config
</UL>

<P><STRONG><a name="[8d]"></a>uart_print_init</STRONG> (Thumb, 94 bytes, Stack size 16 bytes, at32f403a_407_board.o(i.uart_print_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = uart_print_init &rArr; usart_init &rArr; crm_clocks_freq_get
</UL>
<BR>[Calls]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_init
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_default_para_init
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_transmitter_enable
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_init
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_enable
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crm_periph_clock_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;$Super$$main
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_startup_check
</UL>

<P><STRONG><a name="[8a]"></a>usart_data_transmit</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, at32f403a_407_usart.o(i.usart_data_transmit))
<BR><BR>[Called By]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fputc
</UL>

<P><STRONG><a name="[c2]"></a>usart_enable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, at32f403a_407_usart.o(i.usart_enable))
<BR><BR>[Called By]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_print_init
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_reconfigure
</UL>

<P><STRONG><a name="[89]"></a>usart_flag_get</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, at32f403a_407_usart.o(i.usart_flag_get))
<BR><BR>[Called By]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fputc
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_runtime_check
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_watchdog_test
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_startup_check
</UL>

<P><STRONG><a name="[c0]"></a>usart_init</STRONG> (Thumb, 120 bytes, Stack size 48 bytes, at32f403a_407_usart.o(i.usart_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = usart_init &rArr; crm_clocks_freq_get
</UL>
<BR>[Calls]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crm_clocks_freq_get
</UL>
<BR>[Called By]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_print_init
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_reconfigure
</UL>

<P><STRONG><a name="[a5]"></a>usart_reconfigure</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, main.o(i.usart_reconfigure))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = usart_reconfigure &rArr; usart_init &rArr; crm_clocks_freq_get
</UL>
<BR>[Calls]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_transmitter_enable
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_init
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_runtime_check
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_fail_handle
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_startup_check
</UL>

<P><STRONG><a name="[c1]"></a>usart_transmitter_enable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, at32f403a_407_usart.o(i.usart_transmitter_enable))
<BR><BR>[Called By]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_print_init
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_reconfigure
</UL>

<P><STRONG><a name="[a6]"></a>wdt_counter_reload</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, at32f403a_407_wdt.o(i.wdt_counter_reload))
<BR><BR>[Called By]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_runtime_init
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_runtime_check
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_fail_handle
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_watchdog_test
</UL>

<P><STRONG><a name="[aa]"></a>wdt_divider_set</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, at32f403a_407_wdt.o(i.wdt_divider_set))
<BR><BR>[Called By]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_runtime_init
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_watchdog_test
</UL>

<P><STRONG><a name="[ac]"></a>wdt_enable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, at32f403a_407_wdt.o(i.wdt_enable))
<BR><BR>[Called By]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_runtime_init
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_watchdog_test
</UL>

<P><STRONG><a name="[bd]"></a>wdt_register_write_enable</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, at32f403a_407_wdt.o(i.wdt_register_write_enable))
<BR><BR>[Called By]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_watchdog_test
</UL>

<P><STRONG><a name="[ab]"></a>wdt_reload_value_set</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, at32f403a_407_wdt.o(i.wdt_reload_value_set))
<BR><BR>[Called By]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_runtime_init
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_watchdog_test
</UL>

<P><STRONG><a name="[a7]"></a>wwdt_counter_set</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, at32f403a_407_wwdt.o(i.wwdt_counter_set))
<BR><BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_runtime_check
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_fail_handle
</UL>

<P><STRONG><a name="[ad]"></a>wwdt_divider_set</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, at32f403a_407_wwdt.o(i.wwdt_divider_set))
<BR><BR>[Called By]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_runtime_init
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_watchdog_test
</UL>

<P><STRONG><a name="[af]"></a>wwdt_enable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, at32f403a_407_wwdt.o(i.wwdt_enable))
<BR><BR>[Called By]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_runtime_init
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_watchdog_test
</UL>

<P><STRONG><a name="[ae]"></a>wwdt_window_counter_set</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, at32f403a_407_wwdt.o(i.wwdt_window_counter_set))
<BR><BR>[Called By]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_runtime_init
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_watchdog_test
</UL>

<P><STRONG><a name="[65]"></a>_fp_init</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, fpinit.o(x$fpl$fpinit))
<BR><BR>[Called By]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init_fp_1
</UL>

<P><STRONG><a name="[ec]"></a>__fplib_config_fpu_vfp</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, fpinit.o(x$fpl$fpinit), UNUSED)

<P><STRONG><a name="[ed]"></a>__fplib_config_pureend_doubles</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, fpinit.o(x$fpl$fpinit), UNUSED)
<P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[a8]"></a>selftest_stack_check</STRONG> (Thumb, 66 bytes, Stack size 0 bytes, at32_selftest_runtime.o(i.selftest_stack_check))
<BR><BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;selftest_runtime_check
</UL>

<P><STRONG><a name="[5c]"></a>_printf_input_char</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, _printf_char_common.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> _printf_char_common.o(.text)
</UL><P>
<H3>
Undefined Global Symbols
</H3><HR></body></html>
