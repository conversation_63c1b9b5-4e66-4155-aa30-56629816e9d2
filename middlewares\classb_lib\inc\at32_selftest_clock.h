/**
  **************************************************************************
  * @file     at32_selftest_clock.h
  * @version  v2.0.0
  * @date     2021-12-31
  * @brief    at32 selftest clock header file
  **************************************************************************
  */

#ifndef __AT32_SELFTEST_CLOCK_H
#define __AT32_SELFTEST_CLOCK_H

typedef enum 
{
  CLOCK_TEST_ERROR = 0,
  CLOCK_TEST_SUCCESS = 1,
  CLOCK_TEST_CONTINUE,
} clock_status_type;

clock_status_type selftest_clock_startup_test(void);
clock_status_type selftest_clock_runtime_test(void);

#endif
