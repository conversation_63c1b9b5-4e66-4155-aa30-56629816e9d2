/**
  **************************************************************************
  * @file     at32_selftest_ram.h
  * @version  v2.0.0
  * @date     2021-12-31
  * @brief    at32 selftest ram header file
  **************************************************************************
  */

#ifndef __AT32_SELFTEST_RAM_H
#define __AT32_SELFTEST_RAM_H

#include "stdint.h"

typedef enum
{
  RAM_TEST_ERROR = 0,
  RAM_TEST_SUCCESS = 1, //return val for assembly ram func
  RAM_TEST_CONTINUE
} ram_status_type;

ram_status_type selftest_full_ram_test(uint32_t *beg, uint32_t *end, uint32_t pat);
ram_status_type selftest_ram_step_implement(uint32_t *beg, uint32_t *buf, uint32_t pat);
ram_status_type selftest_sampling_ram_test(void);

#endif
