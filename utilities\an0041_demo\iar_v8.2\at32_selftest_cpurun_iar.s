;/**
;  **************************************************************************
;  * @file     at32_selftest_cpurun_iar.s
;  * @version  v2.0.0
;  * @date     2021-12-31
;  * @brief    contains cpu self-test in runtime phase.
;  **************************************************************************
;  */

  SECTION .text:CODE(2)

  ; Exported function
  EXPORT selftest_cpu_runtime_test

  ; reference to the selftest_fail_handle routine to be executed in case of non-recoverable
  ; failure
  EXTERN selftest_fail_handle

  ; C variables for control flow monitoring
  EXTERN ctrl_flow_cnt
  EXTERN ctrl_flow_cnt_inv


;/**
;  * @brief  cpu selftest in runtime.
;  * @note   when possible, branch are 16-bit only (depending on relative offset to final bl instruction)
;  * @param  input: none.
;  *         output: branch directly to a fail safe routine in case of failure
;  * @retval cpu_test_success (=1) if test is ok
;  */

selftest_cpu_runtime_test:

    STMDB SP!, {R4, R5, R6, R7, R8, R9, R10, R11}
    ; register r0 (holds value returned by the function)
    MOVS R0, #0xAAAAAAAA
    CMP R0, #0xAAAAAAAA
    BNE.W selftest_fail_handle
    MOVS R0, #0x55555555
    CMP R0, #0x55555555
    BNE.W selftest_fail_handle

    ; this is for control flow test (entry point)
    LDR R0,=ctrl_flow_cnt
    ; assumes r1 ok; if not, error will be detected by r1 test and ctrl flow test later on
    LDR R1,[R0]
    ADDS R1,R1,#0x3	 ; ctrl_flow_cnt += OxO3
    STR R1,[R0]

    MOVS R0, #0x0             ; for ramp test

    ; register r1
    MOVS R1, #0xAAAAAAAA
    CMP R1, #0xAAAAAAAA
    BNE.W selftest_fail_handle
    MOVS R1, #0x55555555
    CMP R1, #0x55555555
    BNE.W selftest_fail_handle
    MOVS R1, #0x01            ; for ramp test

    ; register r2
    MOVS R2, #0xAAAAAAAA
    CMP R2, #0xAAAAAAAA
    BNE.W selftest_fail_handle
    MOVS R2, #0x55555555
    CMP R2, #0x55555555
    BNE.W selftest_fail_handle
    MOVS R2, #0x02            ; for ramp test

    ; register r3
    MOVS R3, #0xAAAAAAAA
    CMP R3, #0xAAAAAAAA
    BNE.W selftest_fail_handle
    MOVS R3, #0x55555555
    CMP R3, #0x55555555
    BNE.W selftest_fail_handle
    MOVS R3, #0x03            ; for ramp test

    ; register r4
    MOVS R4, #0xAAAAAAAA
    CMP R4, #0xAAAAAAAA
    BNE.W selftest_fail_handle
    MOVS R4, #0x55555555
    CMP R4, #0x55555555
    BNE.W selftest_fail_handle
    MOVS R4, #0x04            ; for ramp test

    ; register r5
    MOVS R5, #0xAAAAAAAA
    CMP R5, #0xAAAAAAAA
    BNE.W selftest_fail_handle
    MOVS R5, #0x55555555
    CMP R5, #0x55555555
    BNE.W selftest_fail_handle
    MOVS R5, #0x05            ; for ramp test

    ; register r6
    MOVS R6, #0xAAAAAAAA
    CMP R6, #0xAAAAAAAA
    BNE.W selftest_fail_handle
    MOVS R6, #0x55555555
    CMP R6, #0x55555555
    BNE.W selftest_fail_handle
    MOVS R6, #0x06            ; for ramp test

    ; register r7
    MOVS R7, #0xAAAAAAAA
    CMP R7, #0xAAAAAAAA
    BNE.W selftest_fail_handle
    MOVS R7, #0x55555555
    CMP R7, #0x55555555
    BNE.W selftest_fail_handle
    MOVS R7, #0x07            ; for ramp test

    ; register r8
    MOVS R8, #0xAAAAAAAA
    CMP R8, #0xAAAAAAAA
    BNE.W selftest_fail_handle
    MOVS R8, #0x55555555
    CMP R8, #0x55555555
    BNE.W selftest_fail_handle
    MOVS R8, #0x08            ; for ramp test

    ; register r9
    MOVS R9, #0xAAAAAAAA
    CMP R9, #0xAAAAAAAA
    BNE.W selftest_fail_handle
    MOVS R9, #0x55555555
    CMP R9, #0x55555555
    BNE.W selftest_fail_handle
    MOVS R9, #0x09            ; for ramp test

    ; register r10
    MOVS R10, #0xAAAAAAAA
    CMP R10, #0xAAAAAAAA
    BNE selftest_fail_handle
    MOVS R10, #0x55555555
    CMP R10, #0x55555555
    BNE selftest_fail_handle
    MOVS R10, #0x0A           ; for ramp test

    ; register r11
    MOVS R11, #0xAAAAAAAA
    CMP R11, #0xAAAAAAAA
    BNE selftest_fail_handle
    MOVS R11, #0x55555555
    CMP R11, #0x55555555
    BNE selftest_fail_handle
    MOVS R11, #0x0B           ; for ramp test

    ; register r12
    MOVS R12, #0xAAAAAAAA
    CMP R12, #0xAAAAAAAA
    BNE selftest_fail_handle
    MOVS R12, #0x55555555
    CMP R12, #0x55555555
    BNE selftest_fail_handle
    MOVS R12, #0x0C           ; for ramp test

    ; Ramp pattern verification
    CMP R0, #0x00
    BNE selftest_fail_handle
    CMP R1, #0x01
    BNE selftest_fail_handle
    CMP R2, #0x02
    BNE selftest_fail_handle
    CMP R3, #0x03
    BNE selftest_fail_handle
    CMP R4, #0x04
    BNE selftest_fail_handle
    CMP R5, #0x05
    BNE selftest_fail_handle
    CMP R6, #0x06
    BNE selftest_fail_handle
    CMP R7, #0x07
    BNE selftest_fail_handle
    CMP R8, #0x08
    BNE selftest_fail_handle
    CMP R9, #0x09
    BNE selftest_fail_handle
    CMP R10, #0x0A
    BNE selftest_fail_handle
    CMP R11, #0x0B
    BNE selftest_fail_handle
    CMP R12, #0x0C
    BNE selftest_fail_handle

    LDMIA SP!, {R4, R5, R6, R7, R8, R9, R10, R11}

    ; control flow test (exit point)
    LDR R0,=ctrl_flow_cnt_inv
    LDR R1,[R0]
    SUBS R1,R1,#0x3	 ; ctrl_flow_cnt_inv -= OxO3
    STR R1,[R0]

    MOVS R0, #0x1       ; cpu_test_success
    BX LR               ; return to the caller

  END


