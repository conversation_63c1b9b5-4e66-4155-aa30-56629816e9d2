/**
  **************************************************************************
  * @file     at32f403a_407_int.c
  * @version  v2.0.6
  * @date     2021-12-31
  * @brief    main interrupt service routines.
  **************************************************************************
  *                       Copyright notice & Disclaimer
  *
  * The software Board Support Package (BSP) that is made available to
  * download from Artery official website is the copyrighted work of Artery.
  * Artery authorizes customers to use, copy, and distribute the BSP
  * software and its related documentation for the purpose of design and
  * development in conjunction with Artery microcontrollers. Use of the
  * software is governed by this copyright notice and the following disclaimer.
  *
  * THIS SOFTWARE IS PROVIDED ON "AS IS" BASIS WITHOUT WARRANTIES,
  * G<PERSON><PERSON><PERSON>TEES OR REPRESENTATIONS OF ANY KIND. ARTERY EXPRESSLY DISCLAIMS,
  * TO THE FULLEST EXTENT PERMITTED BY LAW, ALL EXPRESS, IMPLIED OR
  * STATUTORY OR OTHER WARRANTIES, GUARANTEES OR REPRESENTATIONS,
  * INCLUDING BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY,
  * FITNESS FOR A PARTICULAR PURPOSE, OR NON-INFRINGEMENT.
  *
  **************************************************************************
  */

/* includes ------------------------------------------------------------------*/
#include "at32f403a_407_int.h"
#include "main.h"
#include "at32_selftest_param.h"
#include "at32_selftest_lib.h"
volatile static uint16_t c4dt_val;	/* tmr5 ch4 data value */
/**
  * @brief  this function handles nmi exception.
  * @param  none
  * @retval none
  */
void NMI_Handler(void)
{
}

/**
  * @brief  this function handles hard fault exception.
  * @param  none
  * @retval none
  */
void HardFault_Handler(void)
{
  /* go to infinite loop when hard fault exception occurs */
  while(1)
  {
  }
}

/**
  * @brief  this function handles memory manage exception.
  * @param  none
  * @retval none
  */
void MemManage_Handler(void)
{
  /* go to infinite loop when memory manage exception occurs */
  while(1)
  {
  }
}

/**
  * @brief  this function handles bus fault exception.
  * @param  none
  * @retval none
  */
void BusFault_Handler(void)
{
  /* go to infinite loop when bus fault exception occurs */
  while(1)
  {
  }
}

/**
  * @brief  this function handles usage fault exception.
  * @param  none
  * @retval none
  */
void UsageFault_Handler(void)
{
  /* go to infinite loop when usage fault exception occurs */
  while(1)
  {
  }
}

/**
  * @brief  this function handles svcall exception.
  * @param  none
  * @retval none
  */
void SVC_Handler(void)
{
}

/**
  * @brief  this function handles debug monitor exception.
  * @param  none
  * @retval none
  */
void DebugMon_Handler(void)
{
}

/**
  * @brief  this function handles pendsv_handler exception.
  * @param  none
  * @retval none
  */
void PendSV_Handler(void)
{
}

/**
  * @brief  this function handles systick handler.
  * @param  none
  * @retval none
  */
void SysTick_Handler(void)
{
  ram_status_type result;
  uint32_t temp_isr_ctrl_flow_cnt;
  systick_inc();
  /* verify systick_cnt integrity */
  if(systick_cnt == ~systick_cnt_inv)
  {
    systick_cnt++;
    systick_cnt_inv = ~systick_cnt;

    if(systick_cnt >= SYSTICK_10MS_TB)
    {
      /* reset timebase counter */
      systick_cnt = 0;
      systick_cnt_inv = ~systick_cnt;

      /* set flag in runtime */
      time_base_flag = 0xAAAAAAAA;
      time_base_flag_inv = ~time_base_flag;

      isr_ctrl_flow_cnt += RAM_MARCHC_ISR_CALLER;
      __disable_irq();
      result = selftest_sampling_ram_test();
      __enable_irq();
      isr_ctrl_flow_cnt_inv -= RAM_MARCHC_ISR_CALLER;

      switch(result)
      {
        case RAM_TEST_CONTINUE:
          break;
        case RAM_TEST_SUCCESS:
          #ifdef DEBUG_MESSAGE_RUNTIME
          /* avoid any long string output here in the interrupt, '.' marks ram test completed ok */
          putchar((int16_t)'.');
          #endif
          break;
        case RAM_TEST_ERROR:
        default:
          RUNTIME_PRINTF("ram check error \r\n");
          selftest_fail_handle();
          break;
      }

      temp_isr_ctrl_flow_cnt = ~isr_ctrl_flow_cnt_inv;
      if(isr_ctrl_flow_cnt == temp_isr_ctrl_flow_cnt)
      {
        if(result == RAM_TEST_SUCCESS)
        {
          if(isr_ctrl_flow_cnt != RAM_TEST_COMPLETED)
          {
            RUNTIME_PRINTF("control flow error (ram test) \r\n");
            selftest_fail_handle();
          }
          else
          {
            isr_ctrl_flow_cnt = 0;
            isr_ctrl_flow_cnt_inv = ~isr_ctrl_flow_cnt;
          }
        }
      }
      else
      {
        RUNTIME_PRINTF("control flow error in isr\r\n");
        selftest_fail_handle();
      }
    }
  }
}

/**
  * @brief  this function handles tmr5 global interrupt request.
  * @param  none
  * @retval none
  */
void TMR5_GLOBAL_IRQHandler(void)
{
  uint16_t c4dt_last_val;

  if(tmr_flag_get(TMR5, TMR_C4_FLAG) == SET)
  {
    /* store last channel data value */
    c4dt_last_val = c4dt_val;

    /* get current channel data value */
    c4dt_val = (uint16_t)(TMR5->c4dt);

    if(lick_period_flag == 0)
    {
      /* only take correct measurement */
      if(tmr_flag_get(TMR5, TMR_C4_RECAPTURE_FLAG) == RESET)
      {
        /* compute period length */
        if(c4dt_val <= c4dt_last_val)
          period_val = (0xFFFF - c4dt_last_val + c4dt_val);
        else
          period_val = (c4dt_val - c4dt_last_val);
        period_val_inv = ~period_val;

        /* set flag */
        lick_period_flag = 0xAAAAAAAA;
        lick_period_flag_inv = ~lick_period_flag;
      }
      else
      {
        /* ignore recapture */
        tmr_flag_clear(TMR5, TMR_C4_RECAPTURE_FLAG);
      }
    }
  }
}
