/**
  **************************************************************************
  * @file     at32_selftest_param.h
  * @version  v2.0.0
  * @date     2021-12-31
  * @brief    contains parameters to be customized
  **************************************************************************
  */

#ifndef __AT32_SELFTEST_PARAM_H
#define __AT32_SELFTEST_PARAM_H

#include "stdint.h"
#include "at32f403a_407.h"

/* selftest debug messages reported on a pc via uart */
#define DEBUG_MESSAGE_STARTUP        /* startup phase */
#define DEBUG_MESSAGE_RUNTIME        /* runtime phase */

#ifdef DEBUG_MESSAGE_STARTUP
#define STARTUP_PRINTF(...)              printf(__VA_ARGS__)
#else
#define STARTUP_PRINTF(...)          
#endif

#ifdef DEBUG_MESSAGE_RUNTIME
#define RUNTIME_PRINTF(...)              printf(__VA_ARGS__)
#else
#define RUNTIME_PRINTF(...)          
#endif

/* uncoment next lines to configure watchdogs & resets for debug purposes */
#define WDT_USED
#define WWDT_USED

/* comment out next line when hext clock is not used - clock test then runs based on hick */
#define HEXT_CLOCK_USED

/* timeout value for crm */
#define CLOCK_SWITCH_TIMEOUT             ((uint32_t)10000)
#define HICK_STABLE_TIMEOUT              ((uint32_t)10000)
#define PLL_STABLE_TIMEOUT               ((uint32_t)10000)

/* reserved area for ram buffer, include overlap for test purposes */
/* don't change this parameter as it is related to physical technology used! */
#define RUNTIME_RAM_BLOCK_SIZE           ((uint32_t)6)
/* min overlap to cover coupling fault from one tested row to the other */
#define RUNTIME_RAM_BLOCK_OVERLAP        ((uint32_t)1)
                                           
/* these are the direct and inverted data (pattern) used during the ram
test, performed using march c- algorithm */
#define BACKGROUND_PATTERN               ((uint32_t)0x00000000)
#define BACKGROUND_PATTERN_INV           ((uint32_t)0xFFFFFFFF)

/* this is to provide a time base longer than the systick */
#define SYSTICK_10MS_TB                  ((uint32_t)10) /* 10*1ms */

/* system clock at runtime stage */
#define SYSCLK_RUNTIME_FREQ              ((uint32_t)240000000)
/* system clock at startup stage */
#define SYSCLK_STARTUP_FREQ              ((uint32_t)240000000)
  
/* value of the internal lick oscillator in hz */
#define LICK_FREQ_TYPICAL                ((uint32_t)40000)
#define LICK_FREQ_MAX                    ((uint32_t)60000)
#define LICK_FREQ_MIN                    ((uint32_t)30000)

/* hext/hick frequency limit */  
#define FREQ_LIMIT_HIGH(fcy)             ((uint32_t)(((fcy/8)/LICK_FREQ_MIN)*8))
#define FREQ_LIMIT_LOW(fcy)              ((uint32_t)(((fcy/8)/LICK_FREQ_MAX)*8))

/* control flow tags and checkpoints */
#define CPU_TEST_CALLER                  ((uint32_t)2)
#define CPU_TEST_CALLEE                  ((uint32_t)3) /* do not modify: used in cpu test assembly file */
#define WDT_TEST_CALLER                  ((uint32_t)5)
#define CLOCK_TEST_CALLER                ((uint32_t)7)
#define CLOCK_TEST_CALLEE                ((uint32_t)11)
#define CRC_TEST_CALLER                  ((uint32_t)13)
#define CRC_TEST_CALLEE                  ((uint32_t)17)
#define CRC_STORE_CALLER                 ((uint32_t)19)
#define STACK_OVERFLOW_CALLER            ((uint32_t)23)
#define STACK_OVERFLOW_CALLEE            ((uint32_t)29)
#define CLOCK_PERIOD_TEST_CALLEE         ((uint32_t)31)

#define CHECKPOINT1                      ((uint32_t)(CPU_TEST_CALLER + \
                                                     CPU_TEST_CALLEE + \
                                                     WDT_TEST_CALLER + \
                                                     CLOCK_TEST_CALLER + \
                                                     CLOCK_TEST_CALLEE + \
                                                     CRC_TEST_CALLER))

#define CHECKPOINT2                      ((uint32_t)(CRC_STORE_CALLER +\
                                                     STACK_OVERFLOW_CALLER))

/* crc test once for run-time */
#define RUNTIME_CIRCLE_ONCE              ((uint32_t)(CPU_TEST_CALLER + \
                                                     CPU_TEST_CALLEE + \
                                                     CLOCK_TEST_CALLER + \
                                                     CLOCK_PERIOD_TEST_CALLEE + \
                                                     CRC_TEST_CALLER + \
                                                     CRC_TEST_CALLEE + \
                                                     STACK_OVERFLOW_CALLER + \
                                                     STACK_OVERFLOW_CALLEE))
/* whole one test for run-time */
#define RUNTIME_WHOLE_CHECKED            ((uint32_t)(RUNTIME_CIRCLE_ONCE * (ROM_TOTAL_STEPS + 1)))

#define RAM_MARCHC_ISR_CALLER            ((uint32_t)2)
#define RAM_MARCHC_ISR_CALLEE            ((uint32_t)3) /* do not modify: used in ram test assembly file */

/* this is for march c tests */
#define ISR_CIRCLE_ONCE                  ((uint32_t)(RAM_MARCHC_ISR_CALLER + RAM_MARCHC_ISR_CALLEE))

#define CLASS_B_ROWS                     ((CLASS_B_END_ADDR - CLASS_B_START_ADDR) / (RUNTIME_RAM_BLOCK_SIZE - 2 * RUNTIME_RAM_BLOCK_OVERLAP))
/* +2 below is for last block & buffer self-test itself */
#define RAM_TEST_COMPLETED               ((uint32_t)(ISR_CIRCLE_ONCE * (uint32_t)(CLASS_B_ROWS / 4 + 2)))

#define control_flow_init()              (ctrl_flow_cnt_inv = ~(ctrl_flow_cnt = 0))
#define control_flow_call(a)             (ctrl_flow_cnt += (a))
#define control_flow_resume(a)           (ctrl_flow_cnt_inv -= (a))

#define STACK_OVERFLOW_FLAG_0            ((uint32_t)0x11111111)
#define STACK_OVERFLOW_FLAG_1            ((uint32_t)0x22222222)
#define STACK_OVERFLOW_FLAG_2            ((uint32_t)0x33333333)
#define STACK_OVERFLOW_FLAG_3            ((uint32_t)0x44444444)

#ifdef __IAR_SYSTEMS_ICC__  /* iar compiler */
/* this is the iar compiler entry point, usually executed right after reset */
 extern void __iar_program_start( void );
 extern uint32_t __checksum;
/* the variables are set by iar linker */
 extern uint32_t __ICFEDIT_region_ROM_start__;
 extern uint32_t __ICFEDIT_region_ROM_end__;
 extern uint32_t __ICFEDIT_region_RAM_start__;
 extern uint32_t __ICFEDIT_region_RAM_end__;
 extern uint32_t __ICFEDIT_region_CLASSB_start__;
 extern uint32_t __ICFEDIT_region_CLASSB_end__;  

 /* constants necessary for flash crc calculation  (last block - xx bytes - separated for crc) */
 #define ROM_START_ADDR                  (uint32_t)&__ICFEDIT_region_ROM_start__ 
 #define ROM_END_ADDR                    (uint32_t)((uint8_t *)(&__checksum) - 1)
 #define ROM_SIZE                        (uint32_t)(ROM_END_ADDR - ROM_START_ADDR + 1)
 #define ROM_ONCE_VERIYF_SIZE            ((uint32_t)0x80) //verify bytes once
 #define ROM_TOTAL_STEPS                 ((uint32_t)(ROM_SIZE / ROM_ONCE_VERIYF_SIZE))
 
 #define RAM_START_ADDR                  (uint32_t)&__ICFEDIT_region_RAM_start__ 
 #define RAM_END_ADDR                    (uint32_t)&__ICFEDIT_region_RAM_end__  
 
 /* constants necessary for transparent march tests */
 #define CLASS_B_START_ADDR              ((uint32_t)(&__ICFEDIT_region_CLASSB_start__))
 #define CLASS_B_END_ADDR                ((uint32_t)(&__ICFEDIT_region_CLASSB_end__))
 
 #define goto_compiler_startup()         {__iar_data_init3(); SystemInit(); __iar_program_start();}

 #define STORED_ROM_CRC                  __checksum
#endif

#ifdef __CC_ARM   /* keil compiler */
 extern void __main( void ); 
 #define ROM_START_ADDR                  ((uint32_t)0x08000000)
 #define ROM_END_ADDR                    ((uint32_t)0x08006000)
 #define ROM_SIZE                        ((uint32_t)(ROM_END_ADDR - ROM_START_ADDR))
 #define ROM_ONCE_VERIYF_SIZE            ((uint32_t)0x80) //verify bytes once
 #define ROM_TOTAL_STEPS                 ((uint32_t)(ROM_SIZE / ROM_ONCE_VERIYF_SIZE))
 
 #define STORED_ROM_CRC                  (*(uint32_t *)ROM_END_ADDR)

 #define RAM_START_ADDR                  ((uint32_t)0x20000000)
 #define RAM_END_ADDR                    ((uint32_t)0x20017FFF)

 /* constants necessary for execution of transparent runtime march tests */
 #define CLASS_B_START_ADDR              ((uint32_t)0x20000030)
 #define CLASS_B_END_ADDR                ((uint32_t)0x20000078)
 #define ENTER_MAIN_FLAG	                (CRC->idt)
 #define goto_compiler_startup()         { ENTER_MAIN_FLAG = 0xAABBAABB; __main(); } /* entry to init c before main() */
#endif

void selftest_fail_handle(void);  
error_status control_flow_check_point(uint32_t chck);

#endif
