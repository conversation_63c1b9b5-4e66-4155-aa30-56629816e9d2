Component: ARM Compiler 5.05 update 1 (build 106) Tool: armlink [4d0efa]

==============================================================================

Section Cross References

    main.o(i.main) refers to at32f403a_407_misc.o(i.nvic_priority_group_config) for nvic_priority_group_config
    main.o(i.main) refers to main.o(i.selftest_system_clock_config) for selftest_system_clock_config
    main.o(i.main) refers to at32f403a_407_board.o(i.uart_print_init) for uart_print_init
    main.o(i.main) refers to noretval__2printf.o(.text) for __2printf
    main.o(i.main) refers to at32_selftest_runtime.o(i.selftest_runtime_init) for selftest_runtime_init
    main.o(i.main) refers to at32_selftest_runtime.o(i.selftest_runtime_check) for selftest_runtime_check
    main.o(i.main) refers to main.o(.conststring) for .conststring
    main.o(i.selftest_clock_cross_measurement_config) refers to at32f403a_407_crm.o(i.crm_periph_clock_enable) for crm_periph_clock_enable
    main.o(i.selftest_clock_cross_measurement_config) refers to at32f403a_407_misc.o(i.nvic_irq_enable) for nvic_irq_enable
    main.o(i.selftest_clock_cross_measurement_config) refers to at32f403a_407_tmr.o(i.tmr_base_init) for tmr_base_init
    main.o(i.selftest_clock_cross_measurement_config) refers to at32f403a_407_tmr.o(i.tmr_cnt_dir_set) for tmr_cnt_dir_set
    main.o(i.selftest_clock_cross_measurement_config) refers to at32f403a_407_tmr.o(i.tmr_clock_source_div_set) for tmr_clock_source_div_set
    main.o(i.selftest_clock_cross_measurement_config) refers to at32f403a_407_tmr.o(i.tmr_repetition_counter_set) for tmr_repetition_counter_set
    main.o(i.selftest_clock_cross_measurement_config) refers to at32f403a_407_gpio.o(i.gpio_pin_remap_config) for gpio_pin_remap_config
    main.o(i.selftest_clock_cross_measurement_config) refers to at32f403a_407_tmr.o(i.tmr_input_default_para_init) for tmr_input_default_para_init
    main.o(i.selftest_clock_cross_measurement_config) refers to at32f403a_407_tmr.o(i.tmr_input_channel_init) for tmr_input_channel_init
    main.o(i.selftest_clock_cross_measurement_config) refers to at32f403a_407_tmr.o(i.tmr_interrupt_enable) for tmr_interrupt_enable
    main.o(i.selftest_clock_cross_measurement_config) refers to at32f403a_407_tmr.o(i.tmr_counter_enable) for tmr_counter_enable
    main.o(i.selftest_clock_cross_measurement_config) refers to at32_selftest_startup.o(CLASS_B_RAM) for lick_period_flag
    main.o(i.selftest_system_clock_config) refers to at32f403a_407_crm.o(i.crm_reset) for crm_reset
    main.o(i.selftest_system_clock_config) refers to at32f403a_407_crm.o(i.crm_clock_source_enable) for crm_clock_source_enable
    main.o(i.selftest_system_clock_config) refers to at32f403a_407_crm.o(i.crm_hext_stable_wait) for crm_hext_stable_wait
    main.o(i.selftest_system_clock_config) refers to noretval__2printf.o(.text) for __2printf
    main.o(i.selftest_system_clock_config) refers to at32_selftest_startup.o(i.selftest_fail_handle) for selftest_fail_handle
    main.o(i.selftest_system_clock_config) refers to at32f403a_407_crm.o(i.crm_pll_config) for crm_pll_config
    main.o(i.selftest_system_clock_config) refers to at32f403a_407_crm.o(i.crm_hext_clock_div_set) for crm_hext_clock_div_set
    main.o(i.selftest_system_clock_config) refers to at32f403a_407_crm.o(i.crm_flag_get) for crm_flag_get
    main.o(i.selftest_system_clock_config) refers to at32f403a_407_crm.o(i.crm_ahb_div_set) for crm_ahb_div_set
    main.o(i.selftest_system_clock_config) refers to at32f403a_407_crm.o(i.crm_apb2_div_set) for crm_apb2_div_set
    main.o(i.selftest_system_clock_config) refers to at32f403a_407_crm.o(i.crm_apb1_div_set) for crm_apb1_div_set
    main.o(i.selftest_system_clock_config) refers to at32f403a_407_crm.o(i.crm_auto_step_mode_enable) for crm_auto_step_mode_enable
    main.o(i.selftest_system_clock_config) refers to at32f403a_407_crm.o(i.crm_sysclk_switch) for crm_sysclk_switch
    main.o(i.selftest_system_clock_config) refers to at32f403a_407_crm.o(i.crm_sysclk_switch_status_get) for crm_sysclk_switch_status_get
    main.o(i.selftest_system_clock_config) refers to system_at32f403a_407.o(i.system_core_clock_update) for system_core_clock_update
    main.o(i.systick_get) refers to main.o(.data) for tick_cnt_val
    main.o(i.systick_inc) refers to main.o(.data) for tick_cnt_val
    main.o(i.usart_reconfigure) refers to at32f403a_407_usart.o(i.usart_init) for usart_init
    main.o(i.usart_reconfigure) refers to at32f403a_407_usart.o(i.usart_transmitter_enable) for usart_transmitter_enable
    main.o(i.usart_reconfigure) refers to at32f403a_407_usart.o(i.usart_enable) for usart_enable
    at32f403a_407_clock.o(i.system_clock_config) refers to at32f403a_407_crm.o(i.crm_reset) for crm_reset
    at32f403a_407_clock.o(i.system_clock_config) refers to at32f403a_407_crm.o(i.crm_clock_source_enable) for crm_clock_source_enable
    at32f403a_407_clock.o(i.system_clock_config) refers to at32f403a_407_crm.o(i.crm_hext_stable_wait) for crm_hext_stable_wait
    at32f403a_407_clock.o(i.system_clock_config) refers to at32f403a_407_crm.o(i.crm_pll_config) for crm_pll_config
    at32f403a_407_clock.o(i.system_clock_config) refers to at32f403a_407_crm.o(i.crm_hext_clock_div_set) for crm_hext_clock_div_set
    at32f403a_407_clock.o(i.system_clock_config) refers to at32f403a_407_crm.o(i.crm_flag_get) for crm_flag_get
    at32f403a_407_clock.o(i.system_clock_config) refers to at32f403a_407_crm.o(i.crm_ahb_div_set) for crm_ahb_div_set
    at32f403a_407_clock.o(i.system_clock_config) refers to at32f403a_407_crm.o(i.crm_apb2_div_set) for crm_apb2_div_set
    at32f403a_407_clock.o(i.system_clock_config) refers to at32f403a_407_crm.o(i.crm_apb1_div_set) for crm_apb1_div_set
    at32f403a_407_clock.o(i.system_clock_config) refers to at32f403a_407_crm.o(i.crm_auto_step_mode_enable) for crm_auto_step_mode_enable
    at32f403a_407_clock.o(i.system_clock_config) refers to at32f403a_407_crm.o(i.crm_sysclk_switch) for crm_sysclk_switch
    at32f403a_407_clock.o(i.system_clock_config) refers to at32f403a_407_crm.o(i.crm_sysclk_switch_status_get) for crm_sysclk_switch_status_get
    at32f403a_407_clock.o(i.system_clock_config) refers to system_at32f403a_407.o(i.system_core_clock_update) for system_core_clock_update
    at32f403a_407_int.o(i.SysTick_Handler) refers to main.o(i.systick_inc) for systick_inc
    at32f403a_407_int.o(i.SysTick_Handler) refers to at32_selftest_ram.o(i.selftest_sampling_ram_test) for selftest_sampling_ram_test
    at32f403a_407_int.o(i.SysTick_Handler) refers to putc.o(.text) for putc
    at32f403a_407_int.o(i.SysTick_Handler) refers to noretval__2printf.o(.text) for __2printf
    at32f403a_407_int.o(i.SysTick_Handler) refers to at32_selftest_startup.o(i.selftest_fail_handle) for selftest_fail_handle
    at32f403a_407_int.o(i.SysTick_Handler) refers to at32_selftest_startup.o(CLASS_B_RAM) for systick_cnt
    at32f403a_407_int.o(i.SysTick_Handler) refers to at32_selftest_startup.o(CLASS_B_RAM_REV) for systick_cnt_inv
    at32f403a_407_int.o(i.SysTick_Handler) refers to at32f403a_407_board.o(.data) for __stdout
    at32f403a_407_int.o(i.TMR5_GLOBAL_IRQHandler) refers to at32f403a_407_tmr.o(i.tmr_flag_get) for tmr_flag_get
    at32f403a_407_int.o(i.TMR5_GLOBAL_IRQHandler) refers to at32f403a_407_tmr.o(i.tmr_flag_clear) for tmr_flag_clear
    at32f403a_407_int.o(i.TMR5_GLOBAL_IRQHandler) refers to at32f403a_407_int.o(.data) for c4dt_val
    at32f403a_407_int.o(i.TMR5_GLOBAL_IRQHandler) refers to at32_selftest_startup.o(CLASS_B_RAM) for lick_period_flag
    at32f403a_407_int.o(i.TMR5_GLOBAL_IRQHandler) refers to at32_selftest_startup.o(CLASS_B_RAM_REV) for period_val_inv
    at32_selftest_cpurun_keil.o(.text) refers to at32_selftest_startup.o(i.selftest_fail_handle) for selftest_fail_handle
    at32_selftest_cpurun_keil.o(.text) refers to at32_selftest_startup.o(CLASS_B_RAM) for ctrl_flow_cnt
    at32_selftest_cpurun_keil.o(.text) refers to at32_selftest_startup.o(CLASS_B_RAM_REV) for ctrl_flow_cnt_inv
    at32_selftest_cpustart_keil.o(.text) refers to at32_selftest_startup.o(i.selftest_fail_handle) for selftest_fail_handle
    at32_selftest_cpustart_keil.o(.text) refers to at32_selftest_startup.o(CLASS_B_RAM) for ctrl_flow_cnt
    at32_selftest_cpustart_keil.o(.text) refers to at32_selftest_startup.o(CLASS_B_RAM_REV) for ctrl_flow_cnt_inv
    at32_selftest_ram_keil.o(.text) refers to at32_selftest_startup.o(CLASS_B_RAM) for isr_ctrl_flow_cnt
    at32_selftest_ram_keil.o(.text) refers to at32_selftest_startup.o(CLASS_B_RAM_REV) for isr_ctrl_flow_cnt_inv
    crc32.o(i.crc32_fsl_continuous) refers to crc32.o(.data) for crc_final_val
    crc32.o(i.crc32_fsl_continuous) refers to crc32.o(.constdata) for crcTable
    crc32.o(i.crc32_fsl_single) refers to crc32.o(.constdata) for crcTable
    at32f403a_407_board.o(.rev16_text) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    at32f403a_407_board.o(.revsh_text) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    at32f403a_407_board.o(.rrx_text) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    at32f403a_407_board.o(i._sys_exit) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    at32f403a_407_board.o(i._ttywrch) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    at32f403a_407_board.o(i.at32_board_init) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    at32f403a_407_board.o(i.at32_board_init) refers to at32f403a_407_board.o(i.delay_init) for delay_init
    at32f403a_407_board.o(i.at32_board_init) refers to at32f403a_407_board.o(i.at32_led_init) for at32_led_init
    at32f403a_407_board.o(i.at32_board_init) refers to at32f403a_407_board.o(i.at32_led_off) for at32_led_off
    at32f403a_407_board.o(i.at32_board_init) refers to at32f403a_407_board.o(i.at32_button_init) for at32_button_init
    at32f403a_407_board.o(i.at32_button_init) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    at32f403a_407_board.o(i.at32_button_init) refers to at32f403a_407_crm.o(i.crm_periph_clock_enable) for crm_periph_clock_enable
    at32f403a_407_board.o(i.at32_button_init) refers to at32f403a_407_gpio.o(i.gpio_default_para_init) for gpio_default_para_init
    at32f403a_407_board.o(i.at32_button_init) refers to at32f403a_407_gpio.o(i.gpio_init) for gpio_init
    at32f403a_407_board.o(i.at32_button_press) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    at32f403a_407_board.o(i.at32_button_press) refers to at32f403a_407_board.o(i.at32_button_state) for at32_button_state
    at32f403a_407_board.o(i.at32_button_press) refers to at32f403a_407_board.o(i.delay_ms) for delay_ms
    at32f403a_407_board.o(i.at32_button_press) refers to at32f403a_407_board.o(.data) for pressed
    at32f403a_407_board.o(i.at32_button_state) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    at32f403a_407_board.o(i.at32_button_state) refers to at32f403a_407_gpio.o(i.gpio_input_data_bit_read) for gpio_input_data_bit_read
    at32f403a_407_board.o(i.at32_led_init) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    at32f403a_407_board.o(i.at32_led_init) refers to at32f403a_407_crm.o(i.crm_periph_clock_enable) for crm_periph_clock_enable
    at32f403a_407_board.o(i.at32_led_init) refers to at32f403a_407_gpio.o(i.gpio_default_para_init) for gpio_default_para_init
    at32f403a_407_board.o(i.at32_led_init) refers to at32f403a_407_gpio.o(i.gpio_init) for gpio_init
    at32f403a_407_board.o(i.at32_led_init) refers to at32f403a_407_board.o(.data) for led_gpio_crm_clk
    at32f403a_407_board.o(i.at32_led_off) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    at32f403a_407_board.o(i.at32_led_off) refers to at32f403a_407_board.o(.data) for led_gpio_pin
    at32f403a_407_board.o(i.at32_led_on) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    at32f403a_407_board.o(i.at32_led_on) refers to at32f403a_407_board.o(.data) for led_gpio_pin
    at32f403a_407_board.o(i.at32_led_toggle) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    at32f403a_407_board.o(i.at32_led_toggle) refers to at32f403a_407_board.o(.data) for led_gpio_pin
    at32f403a_407_board.o(i.delay_init) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    at32f403a_407_board.o(i.delay_init) refers to at32f403a_407_misc.o(i.systick_clock_source_config) for systick_clock_source_config
    at32f403a_407_board.o(i.delay_init) refers to system_at32f403a_407.o(.data) for system_core_clock
    at32f403a_407_board.o(i.delay_init) refers to at32f403a_407_board.o(.data) for fac_us
    at32f403a_407_board.o(i.delay_ms) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    at32f403a_407_board.o(i.delay_ms) refers to at32f403a_407_board.o(.data) for fac_ms
    at32f403a_407_board.o(i.delay_sec) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    at32f403a_407_board.o(i.delay_sec) refers to at32f403a_407_board.o(i.delay_ms) for delay_ms
    at32f403a_407_board.o(i.delay_us) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    at32f403a_407_board.o(i.delay_us) refers to at32f403a_407_board.o(.data) for fac_us
    at32f403a_407_board.o(i.fputc) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    at32f403a_407_board.o(i.fputc) refers to at32f403a_407_usart.o(i.usart_flag_get) for usart_flag_get
    at32f403a_407_board.o(i.fputc) refers to at32f403a_407_usart.o(i.usart_data_transmit) for usart_data_transmit
    at32f403a_407_board.o(i.uart_print_init) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    at32f403a_407_board.o(i.uart_print_init) refers to at32f403a_407_crm.o(i.crm_periph_clock_enable) for crm_periph_clock_enable
    at32f403a_407_board.o(i.uart_print_init) refers to at32f403a_407_gpio.o(i.gpio_default_para_init) for gpio_default_para_init
    at32f403a_407_board.o(i.uart_print_init) refers to at32f403a_407_gpio.o(i.gpio_init) for gpio_init
    at32f403a_407_board.o(i.uart_print_init) refers to at32f403a_407_usart.o(i.usart_init) for usart_init
    at32f403a_407_board.o(i.uart_print_init) refers to at32f403a_407_usart.o(i.usart_transmitter_enable) for usart_transmitter_enable
    at32f403a_407_board.o(i.uart_print_init) refers to at32f403a_407_usart.o(i.usart_enable) for usart_enable
    at32f403a_407_board.o(.data) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    at32f403a_407_adc.o(i.adc_reset) refers to at32f403a_407_crm.o(i.crm_periph_reset) for crm_periph_reset
    at32f403a_407_bpr.o(i.bpr_reset) refers to at32f403a_407_crm.o(i.crm_battery_powered_domain_reset) for crm_battery_powered_domain_reset
    at32f403a_407_can.o(i.can_message_receive) refers to at32f403a_407_can.o(i.can_receive_fifo_release) for can_receive_fifo_release
    at32f403a_407_can.o(i.can_reset) refers to at32f403a_407_crm.o(i.crm_periph_reset) for crm_periph_reset
    at32f403a_407_crm.o(i.crm_clocks_freq_get) refers to at32f403a_407_crm.o(i.crm_sysclk_switch_status_get) for crm_sysclk_switch_status_get
    at32f403a_407_crm.o(i.crm_clocks_freq_get) refers to at32f403a_407_crm.o(.constdata) for sclk_ahb_div_table
    at32f403a_407_crm.o(i.crm_hext_stable_wait) refers to at32f403a_407_crm.o(i.crm_flag_get) for crm_flag_get
    at32f403a_407_crm.o(i.crm_hick_sclk_frequency_select) refers to at32f403a_407_crm.o(i.crm_hick_divider_select) for crm_hick_divider_select
    at32f403a_407_crm.o(i.crm_usb_clock_source_select) refers to at32f403a_407_crm.o(i.crm_hick_sclk_frequency_select) for crm_hick_sclk_frequency_select
    at32f403a_407_dac.o(i.dac_reset) refers to at32f403a_407_crm.o(i.crm_periph_reset) for crm_periph_reset
    at32f403a_407_flash.o(i.flash_bank1_erase) refers to at32f403a_407_flash.o(i.flash_bank1_operation_wait_for) for flash_bank1_operation_wait_for
    at32f403a_407_flash.o(i.flash_bank1_operation_wait_for) refers to at32f403a_407_flash.o(i.flash_bank1_operation_status_get) for flash_bank1_operation_status_get
    at32f403a_407_flash.o(i.flash_bank2_erase) refers to at32f403a_407_flash.o(i.flash_bank2_operation_wait_for) for flash_bank2_operation_wait_for
    at32f403a_407_flash.o(i.flash_bank2_operation_wait_for) refers to at32f403a_407_flash.o(i.flash_bank2_operation_status_get) for flash_bank2_operation_status_get
    at32f403a_407_flash.o(i.flash_byte_program) refers to at32f403a_407_flash.o(i.flash_bank1_operation_wait_for) for flash_bank1_operation_wait_for
    at32f403a_407_flash.o(i.flash_byte_program) refers to at32f403a_407_flash.o(i.flash_bank2_operation_wait_for) for flash_bank2_operation_wait_for
    at32f403a_407_flash.o(i.flash_crc_calibrate) refers to at32f403a_407_flash.o(i.flash_operation_wait_for) for flash_operation_wait_for
    at32f403a_407_flash.o(i.flash_epp_set) refers to at32f403a_407_flash.o(i.flash_operation_wait_for) for flash_operation_wait_for
    at32f403a_407_flash.o(i.flash_epp_set) refers to at32f403a_407_flash.o(.constdata) for .constdata
    at32f403a_407_flash.o(i.flash_fap_enable) refers to at32f403a_407_flash.o(i.flash_operation_wait_for) for flash_operation_wait_for
    at32f403a_407_flash.o(i.flash_halfword_program) refers to at32f403a_407_flash.o(i.flash_bank1_operation_wait_for) for flash_bank1_operation_wait_for
    at32f403a_407_flash.o(i.flash_halfword_program) refers to at32f403a_407_flash.o(i.flash_bank2_operation_wait_for) for flash_bank2_operation_wait_for
    at32f403a_407_flash.o(i.flash_halfword_program) refers to at32f403a_407_flash.o(i.flash_spim_operation_wait_for) for flash_spim_operation_wait_for
    at32f403a_407_flash.o(i.flash_internal_all_erase) refers to at32f403a_407_flash.o(i.flash_bank1_operation_wait_for) for flash_bank1_operation_wait_for
    at32f403a_407_flash.o(i.flash_internal_all_erase) refers to at32f403a_407_flash.o(i.flash_bank2_operation_wait_for) for flash_bank2_operation_wait_for
    at32f403a_407_flash.o(i.flash_operation_wait_for) refers to at32f403a_407_flash.o(i.flash_operation_status_get) for flash_operation_status_get
    at32f403a_407_flash.o(i.flash_sector_erase) refers to at32f403a_407_flash.o(i.flash_bank1_operation_wait_for) for flash_bank1_operation_wait_for
    at32f403a_407_flash.o(i.flash_sector_erase) refers to at32f403a_407_flash.o(i.flash_bank2_operation_wait_for) for flash_bank2_operation_wait_for
    at32f403a_407_flash.o(i.flash_sector_erase) refers to at32f403a_407_flash.o(i.flash_spim_operation_wait_for) for flash_spim_operation_wait_for
    at32f403a_407_flash.o(i.flash_slib_disable) refers to at32f403a_407_flash.o(i.flash_operation_wait_for) for flash_operation_wait_for
    at32f403a_407_flash.o(i.flash_slib_enable) refers to at32f403a_407_flash.o(i.flash_operation_wait_for) for flash_operation_wait_for
    at32f403a_407_flash.o(i.flash_spim_all_erase) refers to at32f403a_407_flash.o(i.flash_spim_operation_wait_for) for flash_spim_operation_wait_for
    at32f403a_407_flash.o(i.flash_spim_operation_wait_for) refers to at32f403a_407_flash.o(i.flash_spim_operation_status_get) for flash_spim_operation_status_get
    at32f403a_407_flash.o(i.flash_ssb_set) refers to at32f403a_407_flash.o(i.flash_operation_wait_for) for flash_operation_wait_for
    at32f403a_407_flash.o(i.flash_user_system_data_erase) refers to at32f403a_407_flash.o(i.flash_fap_status_get) for flash_fap_status_get
    at32f403a_407_flash.o(i.flash_user_system_data_erase) refers to at32f403a_407_flash.o(i.flash_operation_wait_for) for flash_operation_wait_for
    at32f403a_407_flash.o(i.flash_user_system_data_program) refers to at32f403a_407_flash.o(i.flash_operation_wait_for) for flash_operation_wait_for
    at32f403a_407_flash.o(i.flash_word_program) refers to at32f403a_407_flash.o(i.flash_bank1_operation_wait_for) for flash_bank1_operation_wait_for
    at32f403a_407_flash.o(i.flash_word_program) refers to at32f403a_407_flash.o(i.flash_bank2_operation_wait_for) for flash_bank2_operation_wait_for
    at32f403a_407_flash.o(i.flash_word_program) refers to at32f403a_407_flash.o(i.flash_spim_operation_wait_for) for flash_spim_operation_wait_for
    at32f403a_407_gpio.o(i.gpio_iomux_reset) refers to at32f403a_407_crm.o(i.crm_periph_reset) for crm_periph_reset
    at32f403a_407_gpio.o(i.gpio_reset) refers to at32f403a_407_crm.o(i.crm_periph_reset) for crm_periph_reset
    at32f403a_407_i2c.o(i.i2c_init) refers to at32f403a_407_crm.o(i.crm_clocks_freq_get) for crm_clocks_freq_get
    at32f403a_407_i2c.o(i.i2c_reset) refers to at32f403a_407_crm.o(i.crm_periph_reset) for crm_periph_reset
    at32f403a_407_pwc.o(i.pwc_reset) refers to at32f403a_407_crm.o(i.crm_periph_reset) for crm_periph_reset
    at32f403a_407_spi.o(i.i2s_init) refers to at32f403a_407_crm.o(i.crm_clocks_freq_get) for crm_clocks_freq_get
    at32f403a_407_spi.o(i.spi_i2s_reset) refers to at32f403a_407_crm.o(i.crm_periph_reset) for crm_periph_reset
    at32f403a_407_tmr.o(i.tmr_external_clock_mode1_config) refers to at32f403a_407_tmr.o(i.tmr_external_clock_config) for tmr_external_clock_config
    at32f403a_407_tmr.o(i.tmr_external_clock_mode2_config) refers to at32f403a_407_tmr.o(i.tmr_external_clock_config) for tmr_external_clock_config
    at32f403a_407_tmr.o(i.tmr_reset) refers to at32f403a_407_crm.o(i.crm_periph_reset) for crm_periph_reset
    at32f403a_407_usart.o(i.usart_init) refers to at32f403a_407_crm.o(i.crm_clocks_freq_get) for crm_clocks_freq_get
    at32f403a_407_usart.o(i.usart_reset) refers to at32f403a_407_crm.o(i.crm_periph_reset) for crm_periph_reset
    at32f403a_407_usb.o(i.usb_buffer_free) refers to at32f403a_407_usb.o(.data) for g_usb_offset_addr
    at32f403a_407_usb.o(i.usb_buffer_malloc) refers to at32f403a_407_usb.o(.data) for g_usb_offset_addr
    at32f403a_407_usb.o(i.usb_dev_init) refers to at32f403a_407_usb.o(i.usb_interrupt_enable) for usb_interrupt_enable
    at32f403a_407_usb.o(i.usb_ept_open) refers to at32f403a_407_usb.o(.data) for g_usb_packet_address
    at32f403a_407_usb.o(i.usb_read_packet) refers to at32f403a_407_usb.o(.data) for g_usb_packet_address
    at32f403a_407_usb.o(i.usb_usbbufs_enable) refers to at32f403a_407_usb.o(.data) for g_usb_packet_address
    at32f403a_407_usb.o(i.usb_write_packet) refers to at32f403a_407_usb.o(.data) for g_usb_packet_address
    at32f403a_407_wwdt.o(i.wwdt_reset) refers to at32f403a_407_crm.o(i.crm_periph_reset) for crm_periph_reset
    at32_selftest_startup.o(i.$Sub$$main) refers to at32_selftest_startup.o(i.selftest_startup_check) for selftest_startup_check
    at32_selftest_startup.o(i.$Sub$$main) refers to main.o(i.main) for $Super$$main
    at32_selftest_startup.o(i.control_flow_check_point) refers to at32_selftest_startup.o(CLASS_B_RAM) for ctrl_flow_cnt
    at32_selftest_startup.o(i.control_flow_check_point) refers to at32_selftest_startup.o(CLASS_B_RAM_REV) for ctrl_flow_cnt_inv
    at32_selftest_startup.o(i.selftest_fail_handle) refers to system_at32f403a_407.o(i.system_core_clock_update) for system_core_clock_update
    at32_selftest_startup.o(i.selftest_fail_handle) refers to main.o(i.usart_reconfigure) for usart_reconfigure
    at32_selftest_startup.o(i.selftest_fail_handle) refers to noretval__2printf.o(.text) for __2printf
    at32_selftest_startup.o(i.selftest_fail_handle) refers to at32f403a_407_wdt.o(i.wdt_counter_reload) for wdt_counter_reload
    at32_selftest_startup.o(i.selftest_fail_handle) refers to at32f403a_407_wwdt.o(i.wwdt_counter_set) for wwdt_counter_set
    at32_selftest_startup.o(i.selftest_fail_handle) refers to at32_selftest_startup.o(CLASS_B_RAM_REV) for systick_cnt_inv
    at32_selftest_startup.o(i.selftest_fail_handle) refers to at32_selftest_startup.o(CLASS_B_RAM) for systick_cnt
    at32_selftest_startup.o(i.selftest_startup_check) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    at32_selftest_startup.o(i.selftest_startup_check) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    at32_selftest_startup.o(i.selftest_startup_check) refers to _printf_dec.o(.text) for _printf_int_dec
    at32_selftest_startup.o(i.selftest_startup_check) refers to at32f403a_407_misc.o(i.nvic_priority_group_config) for nvic_priority_group_config
    at32_selftest_startup.o(i.selftest_startup_check) refers to at32f403a_407_misc.o(i.nvic_irq_enable) for nvic_irq_enable
    at32_selftest_startup.o(i.selftest_startup_check) refers to system_at32f403a_407.o(i.system_core_clock_update) for system_core_clock_update
    at32_selftest_startup.o(i.selftest_startup_check) refers to at32f403a_407_board.o(i.uart_print_init) for uart_print_init
    at32_selftest_startup.o(i.selftest_startup_check) refers to noretval__2printf.o(.text) for __2printf
    at32_selftest_startup.o(i.selftest_startup_check) refers to at32_selftest_cpustart_keil.o(.text) for selftest_cpu_startup_test
    at32_selftest_startup.o(i.selftest_startup_check) refers to at32_selftest_startup.o(i.selftest_fail_handle) for selftest_fail_handle
    at32_selftest_startup.o(i.selftest_startup_check) refers to at32_selftest_startup.o(i.selftest_watchdog_test) for selftest_watchdog_test
    at32_selftest_startup.o(i.selftest_startup_check) refers to at32f403a_407_usart.o(i.usart_flag_get) for usart_flag_get
    at32_selftest_startup.o(i.selftest_startup_check) refers to at32_selftest_clock.o(i.selftest_clock_startup_test) for selftest_clock_startup_test
    at32_selftest_startup.o(i.selftest_startup_check) refers to main.o(i.usart_reconfigure) for usart_reconfigure
    at32_selftest_startup.o(i.selftest_startup_check) refers to main.o(i.selftest_system_clock_config) for selftest_system_clock_config
    at32_selftest_startup.o(i.selftest_startup_check) refers to at32f403a_407_crm.o(i.crm_periph_clock_enable) for crm_periph_clock_enable
    at32_selftest_startup.o(i.selftest_startup_check) refers to crc32.o(i.crc32_fsl_single) for crc32_fsl_single
    at32_selftest_startup.o(i.selftest_startup_check) refers to at32_selftest_startup.o(i.control_flow_check_point) for control_flow_check_point
    at32_selftest_startup.o(i.selftest_startup_check) refers to at32_selftest_ram_keil.o(.text) for selftest_full_ram_test
    at32_selftest_startup.o(i.selftest_startup_check) refers to __main.o(!!!main) for __main
    at32_selftest_startup.o(i.selftest_startup_check) refers to at32_selftest_startup.o(CLASS_B_RAM) for systick_cnt
    at32_selftest_startup.o(i.selftest_startup_check) refers to at32_selftest_startup.o(CLASS_B_RAM_REV) for systick_cnt_inv
    at32_selftest_startup.o(i.selftest_startup_check) refers to system_at32f403a_407.o(.data) for system_core_clock
    at32_selftest_startup.o(i.selftest_startup_check) refers to at32_selftest_startup.o(.conststring) for .conststring
    at32_selftest_startup.o(i.selftest_startup_check) refers to at32_selftest_startup.o(STACK_BOTTOM) for stack_overflow_buf
    at32_selftest_startup.o(i.selftest_watchdog_test) refers to at32f403a_407_crm.o(i.crm_flag_get) for crm_flag_get
    at32_selftest_startup.o(i.selftest_watchdog_test) refers to noretval__2printf.o(.text) for __2printf
    at32_selftest_startup.o(i.selftest_watchdog_test) refers to at32f403a_407_debug.o(i.debug_periph_mode_set) for debug_periph_mode_set
    at32_selftest_startup.o(i.selftest_watchdog_test) refers to at32f403a_407_crm.o(i.crm_flag_clear) for crm_flag_clear
    at32_selftest_startup.o(i.selftest_watchdog_test) refers to at32f403a_407_wdt.o(i.wdt_register_write_enable) for wdt_register_write_enable
    at32_selftest_startup.o(i.selftest_watchdog_test) refers to at32f403a_407_wdt.o(i.wdt_divider_set) for wdt_divider_set
    at32_selftest_startup.o(i.selftest_watchdog_test) refers to at32f403a_407_wdt.o(i.wdt_reload_value_set) for wdt_reload_value_set
    at32_selftest_startup.o(i.selftest_watchdog_test) refers to at32f403a_407_wdt.o(i.wdt_counter_reload) for wdt_counter_reload
    at32_selftest_startup.o(i.selftest_watchdog_test) refers to at32f403a_407_wdt.o(i.wdt_enable) for wdt_enable
    at32_selftest_startup.o(i.selftest_watchdog_test) refers to at32f403a_407_crm.o(i.crm_periph_clock_enable) for crm_periph_clock_enable
    at32_selftest_startup.o(i.selftest_watchdog_test) refers to at32f403a_407_wwdt.o(i.wwdt_divider_set) for wwdt_divider_set
    at32_selftest_startup.o(i.selftest_watchdog_test) refers to at32f403a_407_wwdt.o(i.wwdt_window_counter_set) for wwdt_window_counter_set
    at32_selftest_startup.o(i.selftest_watchdog_test) refers to at32f403a_407_wwdt.o(i.wwdt_enable) for wwdt_enable
    at32_selftest_startup.o(i.selftest_watchdog_test) refers to at32f403a_407_usart.o(i.usart_flag_get) for usart_flag_get
    at32_selftest_runtime.o(i.selftest_runtime_check) refers to at32_selftest_cpurun_keil.o(.text) for selftest_cpu_runtime_test
    at32_selftest_runtime.o(i.selftest_runtime_check) refers to noretval__2printf.o(.text) for __2printf
    at32_selftest_runtime.o(i.selftest_runtime_check) refers to at32_selftest_startup.o(i.selftest_fail_handle) for selftest_fail_handle
    at32_selftest_runtime.o(i.selftest_runtime_check) refers to at32_selftest_clock.o(i.selftest_clock_runtime_test) for selftest_clock_runtime_test
    at32_selftest_runtime.o(i.selftest_runtime_check) refers to at32f403a_407_usart.o(i.usart_flag_get) for usart_flag_get
    at32_selftest_runtime.o(i.selftest_runtime_check) refers to main.o(i.usart_reconfigure) for usart_reconfigure
    at32_selftest_runtime.o(i.selftest_runtime_check) refers to at32_selftest_crc.o(i.selftest_crc_runtime_test) for selftest_crc_runtime_test
    at32_selftest_runtime.o(i.selftest_runtime_check) refers to putc.o(.text) for putc
    at32_selftest_runtime.o(i.selftest_runtime_check) refers to at32_selftest_runtime.o(i.selftest_stack_check) for selftest_stack_check
    at32_selftest_runtime.o(i.selftest_runtime_check) refers to at32f403a_407_wwdt.o(i.wwdt_counter_set) for wwdt_counter_set
    at32_selftest_runtime.o(i.selftest_runtime_check) refers to at32f403a_407_wdt.o(i.wdt_counter_reload) for wdt_counter_reload
    at32_selftest_runtime.o(i.selftest_runtime_check) refers to at32_selftest_startup.o(CLASS_B_RAM) for time_base_flag
    at32_selftest_runtime.o(i.selftest_runtime_check) refers to at32_selftest_startup.o(CLASS_B_RAM_REV) for time_base_flag_inv
    at32_selftest_runtime.o(i.selftest_runtime_check) refers to at32f403a_407_board.o(.data) for __stdout
    at32_selftest_runtime.o(i.selftest_runtime_init) refers to at32f403a_407_debug.o(i.debug_periph_mode_set) for debug_periph_mode_set
    at32_selftest_runtime.o(i.selftest_runtime_init) refers to at32f403a_407_wdt.o(i.wdt_divider_set) for wdt_divider_set
    at32_selftest_runtime.o(i.selftest_runtime_init) refers to at32f403a_407_wdt.o(i.wdt_reload_value_set) for wdt_reload_value_set
    at32_selftest_runtime.o(i.selftest_runtime_init) refers to at32f403a_407_wdt.o(i.wdt_counter_reload) for wdt_counter_reload
    at32_selftest_runtime.o(i.selftest_runtime_init) refers to at32f403a_407_wdt.o(i.wdt_enable) for wdt_enable
    at32_selftest_runtime.o(i.selftest_runtime_init) refers to at32f403a_407_crm.o(i.crm_periph_clock_enable) for crm_periph_clock_enable
    at32_selftest_runtime.o(i.selftest_runtime_init) refers to at32f403a_407_wwdt.o(i.wwdt_divider_set) for wwdt_divider_set
    at32_selftest_runtime.o(i.selftest_runtime_init) refers to at32f403a_407_wwdt.o(i.wwdt_window_counter_set) for wwdt_window_counter_set
    at32_selftest_runtime.o(i.selftest_runtime_init) refers to at32f403a_407_wwdt.o(i.wwdt_enable) for wwdt_enable
    at32_selftest_runtime.o(i.selftest_runtime_init) refers to at32_selftest_startup.o(RUNTIME_RAM_POINTER) for p_runtime_ram_chk
    at32_selftest_runtime.o(i.selftest_runtime_init) refers to at32_selftest_startup.o(CLASS_B_RAM) for isr_ctrl_flow_cnt
    at32_selftest_runtime.o(i.selftest_runtime_init) refers to at32_selftest_startup.o(CLASS_B_RAM_REV) for isr_ctrl_flow_cnt_inv
    at32_selftest_runtime.o(i.selftest_runtime_init) refers to system_at32f403a_407.o(.data) for system_core_clock
    at32_selftest_runtime.o(i.selftest_stack_check) refers to at32_selftest_startup.o(CLASS_B_RAM) for ctrl_flow_cnt
    at32_selftest_runtime.o(i.selftest_stack_check) refers to at32_selftest_startup.o(STACK_BOTTOM) for stack_overflow_buf
    at32_selftest_runtime.o(i.selftest_stack_check) refers to at32_selftest_startup.o(CLASS_B_RAM_REV) for ctrl_flow_cnt_inv
    at32_selftest_crc.o(i.selftest_crc_runtime_test) refers to crc32.o(i.crc32_fsl_continuous) for crc32_fsl_continuous
    at32_selftest_crc.o(i.selftest_crc_runtime_test) refers to at32_selftest_startup.o(CLASS_B_RAM) for ctrl_flow_cnt
    at32_selftest_crc.o(i.selftest_crc_runtime_test) refers to at32_selftest_startup.o(CLASS_B_RAM_REV) for p_runtime_crc_chk_inv
    at32_selftest_ram.o(i.selftest_sampling_ram_test) refers to at32_selftest_ram_keil.o(.text) for selftest_ram_step_implement
    at32_selftest_ram.o(i.selftest_sampling_ram_test) refers to at32_selftest_startup.o(RUNTIME_RAM_POINTER) for p_runtime_ram_chk
    at32_selftest_ram.o(i.selftest_sampling_ram_test) refers to at32_selftest_startup.o(RUNTIME_RAM_BUF) for runtime_ram_buf
    at32_selftest_clock.o(i.selftest_clock_runtime_test) refers to at32f403a_407_crm.o(i.crm_sysclk_switch) for crm_sysclk_switch
    at32_selftest_clock.o(i.selftest_clock_runtime_test) refers to at32_selftest_startup.o(CLASS_B_RAM) for ctrl_flow_cnt
    at32_selftest_clock.o(i.selftest_clock_runtime_test) refers to at32_selftest_startup.o(CLASS_B_RAM_REV) for period_val_inv
    at32_selftest_clock.o(i.selftest_clock_startup_test) refers to at32f403a_407_crm.o(i.crm_clock_source_enable) for crm_clock_source_enable
    at32_selftest_clock.o(i.selftest_clock_startup_test) refers to main.o(i.systick_get) for systick_get
    at32_selftest_clock.o(i.selftest_clock_startup_test) refers to at32f403a_407_crm.o(i.crm_flag_get) for crm_flag_get
    at32_selftest_clock.o(i.selftest_clock_startup_test) refers to at32f403a_407_crm.o(i.crm_clock_failure_detection_enable) for crm_clock_failure_detection_enable
    at32_selftest_clock.o(i.selftest_clock_startup_test) refers to at32f403a_407_crm.o(i.crm_sysclk_switch) for crm_sysclk_switch
    at32_selftest_clock.o(i.selftest_clock_startup_test) refers to at32f403a_407_crm.o(i.crm_sysclk_switch_status_get) for crm_sysclk_switch_status_get
    at32_selftest_clock.o(i.selftest_clock_startup_test) refers to main.o(i.selftest_clock_cross_measurement_config) for selftest_clock_cross_measurement_config
    at32_selftest_clock.o(i.selftest_clock_startup_test) refers to at32_selftest_startup.o(CLASS_B_RAM) for ctrl_flow_cnt
    at32_selftest_clock.o(i.selftest_clock_startup_test) refers to at32_selftest_startup.o(CLASS_B_RAM_REV) for ctrl_flow_cnt_inv
    system_at32f403a_407.o(i.system_core_clock_update) refers to at32f403a_407_crm.o(i.crm_sysclk_switch_status_get) for crm_sysclk_switch_status_get
    system_at32f403a_407.o(i.system_core_clock_update) refers to system_at32f403a_407.o(.data) for system_core_clock
    system_at32f403a_407.o(i.system_core_clock_update) refers to system_at32f403a_407.o(.constdata) for sys_ahb_div_table
    startup_at32f403a_407.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_at32f403a_407.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_at32f403a_407.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_at32f403a_407.o(RESET) refers to startup_at32f403a_407.o(STACK) for __initial_sp
    startup_at32f403a_407.o(RESET) refers to startup_at32f403a_407.o(.text) for Reset_Handler
    startup_at32f403a_407.o(RESET) refers to at32f403a_407_int.o(i.NMI_Handler) for NMI_Handler
    startup_at32f403a_407.o(RESET) refers to at32f403a_407_int.o(i.HardFault_Handler) for HardFault_Handler
    startup_at32f403a_407.o(RESET) refers to at32f403a_407_int.o(i.MemManage_Handler) for MemManage_Handler
    startup_at32f403a_407.o(RESET) refers to at32f403a_407_int.o(i.BusFault_Handler) for BusFault_Handler
    startup_at32f403a_407.o(RESET) refers to at32f403a_407_int.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_at32f403a_407.o(RESET) refers to at32f403a_407_int.o(i.SVC_Handler) for SVC_Handler
    startup_at32f403a_407.o(RESET) refers to at32f403a_407_int.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_at32f403a_407.o(RESET) refers to at32f403a_407_int.o(i.PendSV_Handler) for PendSV_Handler
    startup_at32f403a_407.o(RESET) refers to at32f403a_407_int.o(i.SysTick_Handler) for SysTick_Handler
    startup_at32f403a_407.o(RESET) refers to at32f403a_407_int.o(i.TMR5_GLOBAL_IRQHandler) for TMR5_GLOBAL_IRQHandler
    startup_at32f403a_407.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_at32f403a_407.o(.text) refers to system_at32f403a_407.o(i.SystemInit) for SystemInit
    startup_at32f403a_407.o(.text) refers to __main.o(!!!main) for __main
    startup_at32f403a_407.o(.text) refers to startup_at32f403a_407.o(HEAP) for Heap_Mem
    startup_at32f403a_407.o(.text) refers to startup_at32f403a_407.o(STACK) for Stack_Mem
    use_no_semi_2.o(.text) refers (Special) to use_no_semi.o(.text) for __use_no_semihosting_swi
    __2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    __2printf.o(.text) refers to at32f403a_407_board.o(.data) for __stdout
    noretval__2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    noretval__2printf.o(.text) refers to at32f403a_407_board.o(.data) for __stdout
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    putc.o(.text) refers to at32f403a_407_board.o(i.fputc) for fputc
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    _printf_char_file.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    _printf_char_file.o(.text) refers to ferror.o(.text) for ferror
    _printf_char_file.o(.text) refers to at32f403a_407_board.o(i.fputc) for fputc
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to at32_selftest_startup.o(i.$Sub$$main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    _printf_char_common.o(.text) refers to __printf.o(.text) for __printf
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_at32f403a_407.o(.text) for __user_initial_stackheap
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000001) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    libinit2.o(.ARM.Collect$$libinit$$00000001) refers to fpinit.o(x$fpl$fpinit) for _fp_init
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to at32f403a_407_board.o(i._sys_exit) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000006) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000009) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000003) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000B) for __rt_lib_shutdown_user_alloc_1
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to at32f403a_407_board.o(i._sys_exit) for _sys_exit
    defsig_exit.o(.text) refers to at32f403a_407_board.o(i._sys_exit) for _sys_exit
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to at32f403a_407_board.o(i._ttywrch) for _ttywrch
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing at32f403a_407_clock.o(.rev16_text), (4 bytes).
    Removing at32f403a_407_clock.o(.revsh_text), (4 bytes).
    Removing at32f403a_407_clock.o(.rrx_text), (6 bytes).
    Removing at32f403a_407_clock.o(i.system_clock_config), (112 bytes).
    Removing at32f403a_407_int.o(.rev16_text), (4 bytes).
    Removing at32f403a_407_int.o(.revsh_text), (4 bytes).
    Removing at32f403a_407_int.o(.rrx_text), (6 bytes).
    Removing at32f403a_407_board.o(.rev16_text), (4 bytes).
    Removing at32f403a_407_board.o(.revsh_text), (4 bytes).
    Removing at32f403a_407_board.o(.rrx_text), (6 bytes).
    Removing at32f403a_407_board.o(i._ttywrch), (4 bytes).
    Removing at32f403a_407_board.o(i.at32_board_init), (48 bytes).
    Removing at32f403a_407_board.o(i.at32_button_init), (60 bytes).
    Removing at32f403a_407_board.o(i.at32_button_press), (60 bytes).
    Removing at32f403a_407_board.o(i.at32_button_state), (16 bytes).
    Removing at32f403a_407_board.o(i.at32_led_init), (80 bytes).
    Removing at32f403a_407_board.o(i.at32_led_off), (40 bytes).
    Removing at32f403a_407_board.o(i.at32_led_on), (40 bytes).
    Removing at32f403a_407_board.o(i.at32_led_toggle), (52 bytes).
    Removing at32f403a_407_board.o(i.delay_init), (56 bytes).
    Removing at32f403a_407_board.o(i.delay_ms), (116 bytes).
    Removing at32f403a_407_board.o(i.delay_sec), (34 bytes).
    Removing at32f403a_407_board.o(i.delay_us), (80 bytes).
    Removing at32f403a_407_acc.o(.rev16_text), (4 bytes).
    Removing at32f403a_407_acc.o(.revsh_text), (4 bytes).
    Removing at32f403a_407_acc.o(.rrx_text), (6 bytes).
    Removing at32f403a_407_acc.o(i.acc_calibration_mode_enable), (48 bytes).
    Removing at32f403a_407_acc.o(i.acc_flag_clear), (12 bytes).
    Removing at32f403a_407_acc.o(i.acc_flag_get), (32 bytes).
    Removing at32f403a_407_acc.o(i.acc_hickcal_get), (12 bytes).
    Removing at32f403a_407_acc.o(i.acc_hicktrim_get), (16 bytes).
    Removing at32f403a_407_acc.o(i.acc_interrupt_enable), (36 bytes).
    Removing at32f403a_407_acc.o(i.acc_read_c1), (12 bytes).
    Removing at32f403a_407_acc.o(i.acc_read_c2), (12 bytes).
    Removing at32f403a_407_acc.o(i.acc_read_c3), (12 bytes).
    Removing at32f403a_407_acc.o(i.acc_step_set), (20 bytes).
    Removing at32f403a_407_acc.o(i.acc_write_c1), (12 bytes).
    Removing at32f403a_407_acc.o(i.acc_write_c2), (12 bytes).
    Removing at32f403a_407_acc.o(i.acc_write_c3), (12 bytes).
    Removing at32f403a_407_adc.o(.rev16_text), (4 bytes).
    Removing at32f403a_407_adc.o(.revsh_text), (4 bytes).
    Removing at32f403a_407_adc.o(.rrx_text), (6 bytes).
    Removing at32f403a_407_adc.o(i.adc_base_config), (44 bytes).
    Removing at32f403a_407_adc.o(i.adc_base_default_para_init), (14 bytes).
    Removing at32f403a_407_adc.o(i.adc_calibration_init), (12 bytes).
    Removing at32f403a_407_adc.o(i.adc_calibration_init_status_get), (18 bytes).
    Removing at32f403a_407_adc.o(i.adc_calibration_start), (12 bytes).
    Removing at32f403a_407_adc.o(i.adc_calibration_status_get), (18 bytes).
    Removing at32f403a_407_adc.o(i.adc_combine_mode_select), (20 bytes).
    Removing at32f403a_407_adc.o(i.adc_combine_ordinary_conversion_data_get), (12 bytes).
    Removing at32f403a_407_adc.o(i.adc_dma_mode_enable), (10 bytes).
    Removing at32f403a_407_adc.o(i.adc_enable), (10 bytes).
    Removing at32f403a_407_adc.o(i.adc_flag_clear), (6 bytes).
    Removing at32f403a_407_adc.o(i.adc_flag_get), (16 bytes).
    Removing at32f403a_407_adc.o(i.adc_interrupt_enable), (22 bytes).
    Removing at32f403a_407_adc.o(i.adc_ordinary_channel_set), (404 bytes).
    Removing at32f403a_407_adc.o(i.adc_ordinary_conversion_data_get), (8 bytes).
    Removing at32f403a_407_adc.o(i.adc_ordinary_conversion_trigger_set), (52 bytes).
    Removing at32f403a_407_adc.o(i.adc_ordinary_part_count_set), (12 bytes).
    Removing at32f403a_407_adc.o(i.adc_ordinary_part_mode_enable), (10 bytes).
    Removing at32f403a_407_adc.o(i.adc_ordinary_software_trigger_enable), (10 bytes).
    Removing at32f403a_407_adc.o(i.adc_ordinary_software_trigger_status_get), (18 bytes).
    Removing at32f403a_407_adc.o(i.adc_preempt_auto_mode_enable), (10 bytes).
    Removing at32f403a_407_adc.o(i.adc_preempt_channel_length_set), (12 bytes).
    Removing at32f403a_407_adc.o(i.adc_preempt_channel_set), (290 bytes).
    Removing at32f403a_407_adc.o(i.adc_preempt_conversion_data_get), (50 bytes).
    Removing at32f403a_407_adc.o(i.adc_preempt_conversion_trigger_set), (52 bytes).
    Removing at32f403a_407_adc.o(i.adc_preempt_offset_value_set), (62 bytes).
    Removing at32f403a_407_adc.o(i.adc_preempt_part_mode_enable), (10 bytes).
    Removing at32f403a_407_adc.o(i.adc_preempt_software_trigger_enable), (10 bytes).
    Removing at32f403a_407_adc.o(i.adc_preempt_software_trigger_status_get), (18 bytes).
    Removing at32f403a_407_adc.o(i.adc_reset), (100 bytes).
    Removing at32f403a_407_adc.o(i.adc_tempersensor_vintrv_enable), (20 bytes).
    Removing at32f403a_407_adc.o(i.adc_voltage_monitor_enable), (32 bytes).
    Removing at32f403a_407_adc.o(i.adc_voltage_monitor_single_channel_select), (10 bytes).
    Removing at32f403a_407_adc.o(i.adc_voltage_monitor_threshold_value_set), (18 bytes).
    Removing at32f403a_407_bpr.o(.rev16_text), (4 bytes).
    Removing at32f403a_407_bpr.o(.revsh_text), (4 bytes).
    Removing at32f403a_407_bpr.o(.rrx_text), (6 bytes).
    Removing at32f403a_407_bpr.o(i.bpr_data_read), (16 bytes).
    Removing at32f403a_407_bpr.o(i.bpr_data_write), (16 bytes).
    Removing at32f403a_407_bpr.o(i.bpr_flag_clear), (40 bytes).
    Removing at32f403a_407_bpr.o(i.bpr_flag_get), (32 bytes).
    Removing at32f403a_407_bpr.o(i.bpr_interrupt_enable), (20 bytes).
    Removing at32f403a_407_bpr.o(i.bpr_reset), (16 bytes).
    Removing at32f403a_407_bpr.o(i.bpr_rtc_clock_calibration_value_set), (20 bytes).
    Removing at32f403a_407_bpr.o(i.bpr_rtc_output_select), (28 bytes).
    Removing at32f403a_407_bpr.o(i.bpr_tamper_pin_active_level_set), (20 bytes).
    Removing at32f403a_407_bpr.o(i.bpr_tamper_pin_enable), (20 bytes).
    Removing at32f403a_407_can.o(.rev16_text), (4 bytes).
    Removing at32f403a_407_can.o(.revsh_text), (4 bytes).
    Removing at32f403a_407_can.o(.rrx_text), (6 bytes).
    Removing at32f403a_407_can.o(i.can_base_init), (188 bytes).
    Removing at32f403a_407_can.o(i.can_baudrate_default_para_init), (16 bytes).
    Removing at32f403a_407_can.o(i.can_baudrate_set), (146 bytes).
    Removing at32f403a_407_can.o(i.can_debug_transmission_prohibit), (10 bytes).
    Removing at32f403a_407_can.o(i.can_default_para_init), (18 bytes).
    Removing at32f403a_407_can.o(i.can_doze_mode_enter), (74 bytes).
    Removing at32f403a_407_can.o(i.can_doze_mode_exit), (44 bytes).
    Removing at32f403a_407_can.o(i.can_error_type_record_get), (12 bytes).
    Removing at32f403a_407_can.o(i.can_filter_default_para_init), (22 bytes).
    Removing at32f403a_407_can.o(i.can_filter_init), (356 bytes).
    Removing at32f403a_407_can.o(i.can_flag_clear), (124 bytes).
    Removing at32f403a_407_can.o(i.can_flag_get), (240 bytes).
    Removing at32f403a_407_can.o(i.can_interrupt_enable), (18 bytes).
    Removing at32f403a_407_can.o(i.can_message_receive), (240 bytes).
    Removing at32f403a_407_can.o(i.can_message_transmit), (312 bytes).
    Removing at32f403a_407_can.o(i.can_operating_mode_set), (222 bytes).
    Removing at32f403a_407_can.o(i.can_receive_error_counter_get), (10 bytes).
    Removing at32f403a_407_can.o(i.can_receive_fifo_release), (26 bytes).
    Removing at32f403a_407_can.o(i.can_receive_message_pending_get), (34 bytes).
    Removing at32f403a_407_can.o(i.can_reset), (60 bytes).
    Removing at32f403a_407_can.o(i.can_transmit_cancel), (40 bytes).
    Removing at32f403a_407_can.o(i.can_transmit_error_counter_get), (12 bytes).
    Removing at32f403a_407_can.o(i.can_transmit_status_get), (108 bytes).
    Removing at32f403a_407_can.o(i.can_ttc_mode_enable), (50 bytes).
    Removing at32f403a_407_crc.o(.rev16_text), (4 bytes).
    Removing at32f403a_407_crc.o(.revsh_text), (4 bytes).
    Removing at32f403a_407_crc.o(.rrx_text), (6 bytes).
    Removing at32f403a_407_crc.o(i.crc_block_calculate), (36 bytes).
    Removing at32f403a_407_crc.o(i.crc_common_data_set), (20 bytes).
    Removing at32f403a_407_crc.o(i.crc_common_date_get), (12 bytes).
    Removing at32f403a_407_crc.o(i.crc_data_get), (12 bytes).
    Removing at32f403a_407_crc.o(i.crc_data_reset), (20 bytes).
    Removing at32f403a_407_crc.o(i.crc_init_data_set), (12 bytes).
    Removing at32f403a_407_crc.o(i.crc_one_word_calculate), (16 bytes).
    Removing at32f403a_407_crc.o(i.crc_reverse_input_data_set), (20 bytes).
    Removing at32f403a_407_crc.o(i.crc_reverse_output_data_set), (20 bytes).
    Removing at32f403a_407_crm.o(.rev16_text), (4 bytes).
    Removing at32f403a_407_crm.o(.revsh_text), (4 bytes).
    Removing at32f403a_407_crm.o(.rrx_text), (6 bytes).
    Removing at32f403a_407_crm.o(i.crm_adc_clock_div_set), (32 bytes).
    Removing at32f403a_407_crm.o(i.crm_battery_powered_domain_reset), (20 bytes).
    Removing at32f403a_407_crm.o(i.crm_clkout_div_set), (20 bytes).
    Removing at32f403a_407_crm.o(i.crm_clkout_to_tmr10_enable), (20 bytes).
    Removing at32f403a_407_crm.o(i.crm_clock_out_set), (32 bytes).
    Removing at32f403a_407_crm.o(i.crm_hext_bypass), (20 bytes).
    Removing at32f403a_407_crm.o(i.crm_hick_clock_calibration_set), (40 bytes).
    Removing at32f403a_407_crm.o(i.crm_hick_clock_trimming_set), (20 bytes).
    Removing at32f403a_407_crm.o(i.crm_hick_divider_select), (20 bytes).
    Removing at32f403a_407_crm.o(i.crm_hick_sclk_frequency_select), (28 bytes).
    Removing at32f403a_407_crm.o(i.crm_interrupt_enable), (32 bytes).
    Removing at32f403a_407_crm.o(i.crm_lext_bypass), (20 bytes).
    Removing at32f403a_407_crm.o(i.crm_periph_reset), (68 bytes).
    Removing at32f403a_407_crm.o(i.crm_periph_sleep_mode_clock_enable), (68 bytes).
    Removing at32f403a_407_crm.o(i.crm_rtc_clock_enable), (20 bytes).
    Removing at32f403a_407_crm.o(i.crm_rtc_clock_select), (20 bytes).
    Removing at32f403a_407_crm.o(i.crm_usb_clock_div_set), (32 bytes).
    Removing at32f403a_407_crm.o(i.crm_usb_clock_source_select), (32 bytes).
    Removing at32f403a_407_crm.o(i.crm_usb_interrupt_remapping_set), (20 bytes).
    Removing at32f403a_407_dac.o(.rev16_text), (4 bytes).
    Removing at32f403a_407_dac.o(.revsh_text), (4 bytes).
    Removing at32f403a_407_dac.o(.rrx_text), (6 bytes).
    Removing at32f403a_407_dac.o(i.dac_1_data_set), (4 bytes).
    Removing at32f403a_407_dac.o(i.dac_2_data_set), (4 bytes).
    Removing at32f403a_407_dac.o(i.dac_data_output_get), (48 bytes).
    Removing at32f403a_407_dac.o(i.dac_dma_enable), (48 bytes).
    Removing at32f403a_407_dac.o(i.dac_dual_data_set), (52 bytes).
    Removing at32f403a_407_dac.o(i.dac_dual_software_trigger_generate), (20 bytes).
    Removing at32f403a_407_dac.o(i.dac_enable), (48 bytes).
    Removing at32f403a_407_dac.o(i.dac_mask_amplitude_select), (48 bytes).
    Removing at32f403a_407_dac.o(i.dac_output_buffer_enable), (60 bytes).
    Removing at32f403a_407_dac.o(i.dac_reset), (24 bytes).
    Removing at32f403a_407_dac.o(i.dac_software_trigger_generate), (52 bytes).
    Removing at32f403a_407_dac.o(i.dac_trigger_enable), (48 bytes).
    Removing at32f403a_407_dac.o(i.dac_trigger_select), (48 bytes).
    Removing at32f403a_407_dac.o(i.dac_wave_generate), (48 bytes).
    Removing at32f403a_407_debug.o(.rev16_text), (4 bytes).
    Removing at32f403a_407_debug.o(.revsh_text), (4 bytes).
    Removing at32f403a_407_debug.o(.rrx_text), (6 bytes).
    Removing at32f403a_407_debug.o(i.debug_device_id_get), (12 bytes).
    Removing at32f403a_407_dma.o(.rev16_text), (4 bytes).
    Removing at32f403a_407_dma.o(.revsh_text), (4 bytes).
    Removing at32f403a_407_dma.o(.rrx_text), (6 bytes).
    Removing at32f403a_407_dma.o(i.dma_channel_enable), (10 bytes).
    Removing at32f403a_407_dma.o(i.dma_data_number_get), (8 bytes).
    Removing at32f403a_407_dma.o(i.dma_data_number_set), (4 bytes).
    Removing at32f403a_407_dma.o(i.dma_default_para_init), (24 bytes).
    Removing at32f403a_407_dma.o(i.dma_flag_clear), (32 bytes).
    Removing at32f403a_407_dma.o(i.dma_flag_get), (44 bytes).
    Removing at32f403a_407_dma.o(i.dma_flexible_config), (152 bytes).
    Removing at32f403a_407_dma.o(i.dma_init), (92 bytes).
    Removing at32f403a_407_dma.o(i.dma_interrupt_enable), (18 bytes).
    Removing at32f403a_407_dma.o(i.dma_reset), (112 bytes).
    Removing at32f403a_407_emac.o(.rev16_text), (4 bytes).
    Removing at32f403a_407_emac.o(.revsh_text), (4 bytes).
    Removing at32f403a_407_emac.o(.rrx_text), (6 bytes).
    Removing at32f403a_407_exint.o(.rev16_text), (4 bytes).
    Removing at32f403a_407_exint.o(.revsh_text), (4 bytes).
    Removing at32f403a_407_exint.o(.rrx_text), (6 bytes).
    Removing at32f403a_407_exint.o(i.exint_default_para_init), (14 bytes).
    Removing at32f403a_407_exint.o(i.exint_event_enable), (32 bytes).
    Removing at32f403a_407_exint.o(i.exint_flag_clear), (12 bytes).
    Removing at32f403a_407_exint.o(i.exint_flag_get), (28 bytes).
    Removing at32f403a_407_exint.o(i.exint_init), (152 bytes).
    Removing at32f403a_407_exint.o(i.exint_interrupt_enable), (32 bytes).
    Removing at32f403a_407_exint.o(i.exint_reset), (40 bytes).
    Removing at32f403a_407_exint.o(i.exint_software_interrupt_event_generate), (16 bytes).
    Removing at32f403a_407_flash.o(.rev16_text), (4 bytes).
    Removing at32f403a_407_flash.o(.revsh_text), (4 bytes).
    Removing at32f403a_407_flash.o(.rrx_text), (6 bytes).
    Removing at32f403a_407_flash.o(i.flash_bank1_erase), (72 bytes).
    Removing at32f403a_407_flash.o(i.flash_bank1_lock), (20 bytes).
    Removing at32f403a_407_flash.o(i.flash_bank1_operation_status_get), (52 bytes).
    Removing at32f403a_407_flash.o(i.flash_bank1_operation_wait_for), (36 bytes).
    Removing at32f403a_407_flash.o(i.flash_bank1_unlock), (24 bytes).
    Removing at32f403a_407_flash.o(i.flash_bank2_erase), (72 bytes).
    Removing at32f403a_407_flash.o(i.flash_bank2_lock), (20 bytes).
    Removing at32f403a_407_flash.o(i.flash_bank2_operation_status_get), (52 bytes).
    Removing at32f403a_407_flash.o(i.flash_bank2_operation_wait_for), (36 bytes).
    Removing at32f403a_407_flash.o(i.flash_bank2_unlock), (24 bytes).
    Removing at32f403a_407_flash.o(i.flash_byte_program), (152 bytes).
    Removing at32f403a_407_flash.o(i.flash_crc_calibrate), (72 bytes).
    Removing at32f403a_407_flash.o(i.flash_epp_set), (232 bytes).
    Removing at32f403a_407_flash.o(i.flash_epp_status_get), (12 bytes).
    Removing at32f403a_407_flash.o(i.flash_fap_enable), (156 bytes).
    Removing at32f403a_407_flash.o(i.flash_fap_status_get), (16 bytes).
    Removing at32f403a_407_flash.o(i.flash_flag_clear), (56 bytes).
    Removing at32f403a_407_flash.o(i.flash_flag_get), (96 bytes).
    Removing at32f403a_407_flash.o(i.flash_halfword_program), (220 bytes).
    Removing at32f403a_407_flash.o(i.flash_internal_all_erase), (124 bytes).
    Removing at32f403a_407_flash.o(i.flash_interrupt_enable), (124 bytes).
    Removing at32f403a_407_flash.o(i.flash_lock), (32 bytes).
    Removing at32f403a_407_flash.o(i.flash_operation_status_get), (52 bytes).
    Removing at32f403a_407_flash.o(i.flash_operation_wait_for), (36 bytes).
    Removing at32f403a_407_flash.o(i.flash_sector_erase), (260 bytes).
    Removing at32f403a_407_flash.o(i.flash_slib_datstart_sector_get), (16 bytes).
    Removing at32f403a_407_flash.o(i.flash_slib_disable), (52 bytes).
    Removing at32f403a_407_flash.o(i.flash_slib_enable), (156 bytes).
    Removing at32f403a_407_flash.o(i.flash_slib_end_sector_get), (12 bytes).
    Removing at32f403a_407_flash.o(i.flash_slib_remaining_count_get), (16 bytes).
    Removing at32f403a_407_flash.o(i.flash_slib_start_sector_get), (16 bytes).
    Removing at32f403a_407_flash.o(i.flash_slib_state_get), (24 bytes).
    Removing at32f403a_407_flash.o(i.flash_spim_all_erase), (84 bytes).
    Removing at32f403a_407_flash.o(i.flash_spim_encryption_range_set), (12 bytes).
    Removing at32f403a_407_flash.o(i.flash_spim_lock), (24 bytes).
    Removing at32f403a_407_flash.o(i.flash_spim_model_select), (12 bytes).
    Removing at32f403a_407_flash.o(i.flash_spim_operation_status_get), (52 bytes).
    Removing at32f403a_407_flash.o(i.flash_spim_operation_wait_for), (36 bytes).
    Removing at32f403a_407_flash.o(i.flash_spim_unlock), (32 bytes).
    Removing at32f403a_407_flash.o(i.flash_ssb_set), (104 bytes).
    Removing at32f403a_407_flash.o(i.flash_ssb_status_get), (16 bytes).
    Removing at32f403a_407_flash.o(i.flash_unlock), (32 bytes).
    Removing at32f403a_407_flash.o(i.flash_user_system_data_erase), (164 bytes).
    Removing at32f403a_407_flash.o(i.flash_user_system_data_program), (100 bytes).
    Removing at32f403a_407_flash.o(i.flash_word_program), (220 bytes).
    Removing at32f403a_407_flash.o(.constdata), (8 bytes).
    Removing at32f403a_407_gpio.o(.rev16_text), (4 bytes).
    Removing at32f403a_407_gpio.o(.revsh_text), (4 bytes).
    Removing at32f403a_407_gpio.o(.rrx_text), (6 bytes).
    Removing at32f403a_407_gpio.o(i.gpio_bits_reset), (4 bytes).
    Removing at32f403a_407_gpio.o(i.gpio_bits_set), (4 bytes).
    Removing at32f403a_407_gpio.o(i.gpio_bits_write), (10 bytes).
    Removing at32f403a_407_gpio.o(i.gpio_event_output_config), (28 bytes).
    Removing at32f403a_407_gpio.o(i.gpio_event_output_enable), (20 bytes).
    Removing at32f403a_407_gpio.o(i.gpio_exint_line_config), (164 bytes).
    Removing at32f403a_407_gpio.o(i.gpio_input_data_bit_read), (18 bytes).
    Removing at32f403a_407_gpio.o(i.gpio_input_data_read), (8 bytes).
    Removing at32f403a_407_gpio.o(i.gpio_iomux_reset), (24 bytes).
    Removing at32f403a_407_gpio.o(i.gpio_output_data_bit_read), (18 bytes).
    Removing at32f403a_407_gpio.o(i.gpio_output_data_read), (8 bytes).
    Removing at32f403a_407_gpio.o(i.gpio_pin_wp_config), (18 bytes).
    Removing at32f403a_407_gpio.o(i.gpio_pins_huge_driven_config), (18 bytes).
    Removing at32f403a_407_gpio.o(i.gpio_port_wirte), (4 bytes).
    Removing at32f403a_407_gpio.o(i.gpio_reset), (164 bytes).
    Removing at32f403a_407_i2c.o(.rev16_text), (4 bytes).
    Removing at32f403a_407_i2c.o(.revsh_text), (4 bytes).
    Removing at32f403a_407_i2c.o(.rrx_text), (6 bytes).
    Removing at32f403a_407_i2c.o(i.i2c_7bit_address_send), (18 bytes).
    Removing at32f403a_407_i2c.o(i.i2c_ack_enable), (10 bytes).
    Removing at32f403a_407_i2c.o(i.i2c_arp_mode_enable), (10 bytes).
    Removing at32f403a_407_i2c.o(i.i2c_clock_stretch_enable), (18 bytes).
    Removing at32f403a_407_i2c.o(i.i2c_data_receive), (8 bytes).
    Removing at32f403a_407_i2c.o(i.i2c_data_send), (4 bytes).
    Removing at32f403a_407_i2c.o(i.i2c_dma_enable), (10 bytes).
    Removing at32f403a_407_i2c.o(i.i2c_dma_end_transfer_set), (10 bytes).
    Removing at32f403a_407_i2c.o(i.i2c_enable), (10 bytes).
    Removing at32f403a_407_i2c.o(i.i2c_fast_mode_duty_set), (10 bytes).
    Removing at32f403a_407_i2c.o(i.i2c_flag_clear), (8 bytes).
    Removing at32f403a_407_i2c.o(i.i2c_flag_get), (48 bytes).
    Removing at32f403a_407_i2c.o(i.i2c_general_call_enable), (10 bytes).
    Removing at32f403a_407_i2c.o(i.i2c_init), (272 bytes).
    Removing at32f403a_407_i2c.o(i.i2c_interrupt_enable), (24 bytes).
    Removing at32f403a_407_i2c.o(i.i2c_master_receive_ack_set), (10 bytes).
    Removing at32f403a_407_i2c.o(i.i2c_own_address1_set), (18 bytes).
    Removing at32f403a_407_i2c.o(i.i2c_own_address2_enable), (10 bytes).
    Removing at32f403a_407_i2c.o(i.i2c_own_address2_set), (12 bytes).
    Removing at32f403a_407_i2c.o(i.i2c_pec_calculate_enable), (10 bytes).
    Removing at32f403a_407_i2c.o(i.i2c_pec_position_set), (10 bytes).
    Removing at32f403a_407_i2c.o(i.i2c_pec_transmit_enable), (10 bytes).
    Removing at32f403a_407_i2c.o(i.i2c_pec_value_get), (10 bytes).
    Removing at32f403a_407_i2c.o(i.i2c_reset), (92 bytes).
    Removing at32f403a_407_i2c.o(i.i2c_smbus_alert_set), (10 bytes).
    Removing at32f403a_407_i2c.o(i.i2c_smbus_enable), (10 bytes).
    Removing at32f403a_407_i2c.o(i.i2c_smbus_mode_set), (10 bytes).
    Removing at32f403a_407_i2c.o(i.i2c_software_reset), (10 bytes).
    Removing at32f403a_407_i2c.o(i.i2c_start_generate), (14 bytes).
    Removing at32f403a_407_i2c.o(i.i2c_stop_generate), (14 bytes).
    Removing at32f403a_407_misc.o(.rev16_text), (4 bytes).
    Removing at32f403a_407_misc.o(.revsh_text), (4 bytes).
    Removing at32f403a_407_misc.o(.rrx_text), (6 bytes).
    Removing at32f403a_407_misc.o(i.nvic_irq_disable), (44 bytes).
    Removing at32f403a_407_misc.o(i.nvic_lowpower_mode_config), (32 bytes).
    Removing at32f403a_407_misc.o(i.nvic_system_reset), (44 bytes).
    Removing at32f403a_407_misc.o(i.nvic_vector_table_set), (20 bytes).
    Removing at32f403a_407_misc.o(i.systick_clock_source_config), (40 bytes).
    Removing at32f403a_407_pwc.o(.rev16_text), (4 bytes).
    Removing at32f403a_407_pwc.o(.revsh_text), (4 bytes).
    Removing at32f403a_407_pwc.o(.rrx_text), (6 bytes).
    Removing at32f403a_407_pwc.o(i.pwc_battery_powered_domain_access), (20 bytes).
    Removing at32f403a_407_pwc.o(i.pwc_deep_sleep_mode_enter), (48 bytes).
    Removing at32f403a_407_pwc.o(i.pwc_flag_clear), (44 bytes).
    Removing at32f403a_407_pwc.o(i.pwc_flag_get), (24 bytes).
    Removing at32f403a_407_pwc.o(i.pwc_power_voltage_monitor_enable), (20 bytes).
    Removing at32f403a_407_pwc.o(i.pwc_pvm_level_select), (20 bytes).
    Removing at32f403a_407_pwc.o(i.pwc_reset), (24 bytes).
    Removing at32f403a_407_pwc.o(i.pwc_sleep_mode_enter), (36 bytes).
    Removing at32f403a_407_pwc.o(i.pwc_standby_mode_enter), (52 bytes).
    Removing at32f403a_407_pwc.o(i.pwc_voltage_regulate_set), (20 bytes).
    Removing at32f403a_407_pwc.o(i.pwc_wakeup_pin_enable), (32 bytes).
    Removing at32f403a_407_rtc.o(.rev16_text), (4 bytes).
    Removing at32f403a_407_rtc.o(.revsh_text), (4 bytes).
    Removing at32f403a_407_rtc.o(.rrx_text), (6 bytes).
    Removing at32f403a_407_rtc.o(i.rtc_alarm_set), (48 bytes).
    Removing at32f403a_407_rtc.o(i.rtc_counter_get), (20 bytes).
    Removing at32f403a_407_rtc.o(i.rtc_counter_set), (48 bytes).
    Removing at32f403a_407_rtc.o(i.rtc_divider_get), (20 bytes).
    Removing at32f403a_407_rtc.o(i.rtc_divider_set), (48 bytes).
    Removing at32f403a_407_rtc.o(i.rtc_flag_clear), (28 bytes).
    Removing at32f403a_407_rtc.o(i.rtc_flag_get), (24 bytes).
    Removing at32f403a_407_rtc.o(i.rtc_interrupt_enable), (32 bytes).
    Removing at32f403a_407_rtc.o(i.rtc_wait_config_finish), (20 bytes).
    Removing at32f403a_407_rtc.o(i.rtc_wait_update_finish), (20 bytes).
    Removing at32f403a_407_sdio.o(.rev16_text), (4 bytes).
    Removing at32f403a_407_sdio.o(.revsh_text), (4 bytes).
    Removing at32f403a_407_sdio.o(.rrx_text), (6 bytes).
    Removing at32f403a_407_sdio.o(i.sdio_buffer_counter_get), (6 bytes).
    Removing at32f403a_407_sdio.o(i.sdio_bus_width_config), (10 bytes).
    Removing at32f403a_407_sdio.o(i.sdio_clock_bypass), (10 bytes).
    Removing at32f403a_407_sdio.o(i.sdio_clock_config), (30 bytes).
    Removing at32f403a_407_sdio.o(i.sdio_clock_enable), (10 bytes).
    Removing at32f403a_407_sdio.o(i.sdio_command_config), (56 bytes).
    Removing at32f403a_407_sdio.o(i.sdio_command_response_get), (10 bytes).
    Removing at32f403a_407_sdio.o(i.sdio_command_state_machine_enable), (10 bytes).
    Removing at32f403a_407_sdio.o(i.sdio_data_config), (56 bytes).
    Removing at32f403a_407_sdio.o(i.sdio_data_counter_get), (6 bytes).
    Removing at32f403a_407_sdio.o(i.sdio_data_read), (8 bytes).
    Removing at32f403a_407_sdio.o(i.sdio_data_state_machine_enable), (10 bytes).
    Removing at32f403a_407_sdio.o(i.sdio_data_write), (6 bytes).
    Removing at32f403a_407_sdio.o(i.sdio_dma_enable), (10 bytes).
    Removing at32f403a_407_sdio.o(i.sdio_flag_clear), (4 bytes).
    Removing at32f403a_407_sdio.o(i.sdio_flag_get), (20 bytes).
    Removing at32f403a_407_sdio.o(i.sdio_flow_control_enable), (10 bytes).
    Removing at32f403a_407_sdio.o(i.sdio_interrupt_enable), (20 bytes).
    Removing at32f403a_407_sdio.o(i.sdio_io_function_enable), (10 bytes).
    Removing at32f403a_407_sdio.o(i.sdio_io_suspend_command_set), (10 bytes).
    Removing at32f403a_407_sdio.o(i.sdio_power_saving_mode_enable), (10 bytes).
    Removing at32f403a_407_sdio.o(i.sdio_power_set), (10 bytes).
    Removing at32f403a_407_sdio.o(i.sdio_power_status_get), (30 bytes).
    Removing at32f403a_407_sdio.o(i.sdio_read_wait_mode_set), (10 bytes).
    Removing at32f403a_407_sdio.o(i.sdio_read_wait_start), (10 bytes).
    Removing at32f403a_407_sdio.o(i.sdio_read_wait_stop), (10 bytes).
    Removing at32f403a_407_sdio.o(i.sdio_reset), (28 bytes).
    Removing at32f403a_407_sdio.o(i.sdio_response_get), (42 bytes).
    Removing at32f403a_407_spi.o(.rev16_text), (4 bytes).
    Removing at32f403a_407_spi.o(.revsh_text), (4 bytes).
    Removing at32f403a_407_spi.o(.rrx_text), (6 bytes).
    Removing at32f403a_407_spi.o(i.i2s_default_para_init), (22 bytes).
    Removing at32f403a_407_spi.o(i.i2s_enable), (10 bytes).
    Removing at32f403a_407_spi.o(i.i2s_init), (554 bytes).
    Removing at32f403a_407_spi.o(i.spi_crc_enable), (10 bytes).
    Removing at32f403a_407_spi.o(i.spi_crc_next_transmit), (14 bytes).
    Removing at32f403a_407_spi.o(i.spi_crc_polynomial_get), (8 bytes).
    Removing at32f403a_407_spi.o(i.spi_crc_polynomial_set), (10 bytes).
    Removing at32f403a_407_spi.o(i.spi_crc_value_get), (18 bytes).
    Removing at32f403a_407_spi.o(i.spi_default_para_init), (22 bytes).
    Removing at32f403a_407_spi.o(i.spi_enable), (10 bytes).
    Removing at32f403a_407_spi.o(i.spi_frame_bit_num_set), (10 bytes).
    Removing at32f403a_407_spi.o(i.spi_half_duplex_direction_set), (10 bytes).
    Removing at32f403a_407_spi.o(i.spi_hardware_cs_output_enable), (10 bytes).
    Removing at32f403a_407_spi.o(i.spi_i2s_data_receive), (8 bytes).
    Removing at32f403a_407_spi.o(i.spi_i2s_data_transmit), (4 bytes).
    Removing at32f403a_407_spi.o(i.spi_i2s_dma_receiver_enable), (10 bytes).
    Removing at32f403a_407_spi.o(i.spi_i2s_dma_transmitter_enable), (10 bytes).
    Removing at32f403a_407_spi.o(i.spi_i2s_flag_clear), (68 bytes).
    Removing at32f403a_407_spi.o(i.spi_i2s_flag_get), (16 bytes).
    Removing at32f403a_407_spi.o(i.spi_i2s_interrupt_enable), (18 bytes).
    Removing at32f403a_407_spi.o(i.spi_i2s_reset), (120 bytes).
    Removing at32f403a_407_spi.o(i.spi_init), (292 bytes).
    Removing at32f403a_407_spi.o(i.spi_software_cs_internal_level_set), (10 bytes).
    Removing at32f403a_407_tmr.o(.rev16_text), (4 bytes).
    Removing at32f403a_407_tmr.o(.revsh_text), (4 bytes).
    Removing at32f403a_407_tmr.o(.rrx_text), (6 bytes).
    Removing at32f403a_407_tmr.o(i.tmr_32_bit_function_enable), (28 bytes).
    Removing at32f403a_407_tmr.o(i.tmr_brkdt_config), (72 bytes).
    Removing at32f403a_407_tmr.o(i.tmr_brkdt_default_para_init), (18 bytes).
    Removing at32f403a_407_tmr.o(i.tmr_channel1_input_select), (10 bytes).
    Removing at32f403a_407_tmr.o(i.tmr_channel_buffer_enable), (10 bytes).
    Removing at32f403a_407_tmr.o(i.tmr_channel_dma_select), (10 bytes).
    Removing at32f403a_407_tmr.o(i.tmr_channel_enable), (96 bytes).
    Removing at32f403a_407_tmr.o(i.tmr_channel_value_get), (46 bytes).
    Removing at32f403a_407_tmr.o(i.tmr_channel_value_set), (40 bytes).
    Removing at32f403a_407_tmr.o(i.tmr_counter_value_get), (6 bytes).
    Removing at32f403a_407_tmr.o(i.tmr_counter_value_set), (4 bytes).
    Removing at32f403a_407_tmr.o(i.tmr_div_value_get), (6 bytes).
    Removing at32f403a_407_tmr.o(i.tmr_div_value_set), (4 bytes).
    Removing at32f403a_407_tmr.o(i.tmr_dma_control_config), (18 bytes).
    Removing at32f403a_407_tmr.o(i.tmr_dma_request_enable), (22 bytes).
    Removing at32f403a_407_tmr.o(i.tmr_encoder_mode_config), (70 bytes).
    Removing at32f403a_407_tmr.o(i.tmr_event_sw_trigger), (8 bytes).
    Removing at32f403a_407_tmr.o(i.tmr_external_clock_config), (28 bytes).
    Removing at32f403a_407_tmr.o(i.tmr_external_clock_mode1_config), (44 bytes).
    Removing at32f403a_407_tmr.o(i.tmr_external_clock_mode2_config), (36 bytes).
    Removing at32f403a_407_tmr.o(i.tmr_force_output_set), (66 bytes).
    Removing at32f403a_407_tmr.o(i.tmr_hall_select), (10 bytes).
    Removing at32f403a_407_tmr.o(i.tmr_input_channel_divider_set), (66 bytes).
    Removing at32f403a_407_tmr.o(i.tmr_input_channel_filter_set), (66 bytes).
    Removing at32f403a_407_tmr.o(i.tmr_internal_clock_set), (10 bytes).
    Removing at32f403a_407_tmr.o(i.tmr_one_cycle_mode_enable), (10 bytes).
    Removing at32f403a_407_tmr.o(i.tmr_output_channel_buffer_enable), (66 bytes).
    Removing at32f403a_407_tmr.o(i.tmr_output_channel_config), (260 bytes).
    Removing at32f403a_407_tmr.o(i.tmr_output_channel_immediately_set), (66 bytes).
    Removing at32f403a_407_tmr.o(i.tmr_output_channel_mode_select), (66 bytes).
    Removing at32f403a_407_tmr.o(i.tmr_output_channel_polarity_set), (96 bytes).
    Removing at32f403a_407_tmr.o(i.tmr_output_channel_switch_set), (66 bytes).
    Removing at32f403a_407_tmr.o(i.tmr_output_default_para_init), (18 bytes).
    Removing at32f403a_407_tmr.o(i.tmr_output_enable), (10 bytes).
    Removing at32f403a_407_tmr.o(i.tmr_overflow_event_disable), (10 bytes).
    Removing at32f403a_407_tmr.o(i.tmr_overflow_request_source_set), (10 bytes).
    Removing at32f403a_407_tmr.o(i.tmr_period_buffer_enable), (10 bytes).
    Removing at32f403a_407_tmr.o(i.tmr_period_value_get), (6 bytes).
    Removing at32f403a_407_tmr.o(i.tmr_period_value_set), (4 bytes).
    Removing at32f403a_407_tmr.o(i.tmr_primary_mode_select), (10 bytes).
    Removing at32f403a_407_tmr.o(i.tmr_pwm_input_config), (350 bytes).
    Removing at32f403a_407_tmr.o(i.tmr_reset), (416 bytes).
    Removing at32f403a_407_tmr.o(i.tmr_sub_mode_select), (10 bytes).
    Removing at32f403a_407_tmr.o(i.tmr_sub_sync_mode_set), (10 bytes).
    Removing at32f403a_407_tmr.o(i.tmr_trigger_input_select), (10 bytes).
    Removing at32f403a_407_usart.o(.rev16_text), (4 bytes).
    Removing at32f403a_407_usart.o(.revsh_text), (4 bytes).
    Removing at32f403a_407_usart.o(.rrx_text), (6 bytes).
    Removing at32f403a_407_usart.o(i.usart_break_bit_num_set), (10 bytes).
    Removing at32f403a_407_usart.o(i.usart_break_send), (12 bytes).
    Removing at32f403a_407_usart.o(i.usart_clock_config), (28 bytes).
    Removing at32f403a_407_usart.o(i.usart_clock_enable), (10 bytes).
    Removing at32f403a_407_usart.o(i.usart_data_receive), (8 bytes).
    Removing at32f403a_407_usart.o(i.usart_dma_receiver_enable), (10 bytes).
    Removing at32f403a_407_usart.o(i.usart_dma_transmitter_enable), (10 bytes).
    Removing at32f403a_407_usart.o(i.usart_flag_clear), (6 bytes).
    Removing at32f403a_407_usart.o(i.usart_hardware_flow_control_set), (102 bytes).
    Removing at32f403a_407_usart.o(i.usart_interrupt_enable), (54 bytes).
    Removing at32f403a_407_usart.o(i.usart_irda_low_power_enable), (10 bytes).
    Removing at32f403a_407_usart.o(i.usart_irda_mode_enable), (10 bytes).
    Removing at32f403a_407_usart.o(i.usart_irda_smartcard_division_set), (10 bytes).
    Removing at32f403a_407_usart.o(i.usart_lin_mode_enable), (10 bytes).
    Removing at32f403a_407_usart.o(i.usart_parity_selection_config), (76 bytes).
    Removing at32f403a_407_usart.o(i.usart_receiver_enable), (10 bytes).
    Removing at32f403a_407_usart.o(i.usart_receiver_mute_enable), (10 bytes).
    Removing at32f403a_407_usart.o(i.usart_reset), (244 bytes).
    Removing at32f403a_407_usart.o(i.usart_single_line_halfduplex_select), (10 bytes).
    Removing at32f403a_407_usart.o(i.usart_smartcard_guard_time_set), (10 bytes).
    Removing at32f403a_407_usart.o(i.usart_smartcard_mode_enable), (10 bytes).
    Removing at32f403a_407_usart.o(i.usart_smartcard_nack_set), (10 bytes).
    Removing at32f403a_407_usart.o(i.usart_wakeup_id_set), (10 bytes).
    Removing at32f403a_407_usart.o(i.usart_wakeup_mode_set), (10 bytes).
    Removing at32f403a_407_usb.o(.rev16_text), (4 bytes).
    Removing at32f403a_407_usb.o(.revsh_text), (4 bytes).
    Removing at32f403a_407_usb.o(.rrx_text), (6 bytes).
    Removing at32f403a_407_usb.o(i.usb_buffer_free), (12 bytes).
    Removing at32f403a_407_usb.o(i.usb_buffer_malloc), (20 bytes).
    Removing at32f403a_407_usb.o(i.usb_connect), (18 bytes).
    Removing at32f403a_407_usb.o(i.usb_dev_init), (36 bytes).
    Removing at32f403a_407_usb.o(i.usb_disconnect), (22 bytes).
    Removing at32f403a_407_usb.o(i.usb_enter_suspend), (22 bytes).
    Removing at32f403a_407_usb.o(i.usb_ept_close), (572 bytes).
    Removing at32f403a_407_usb.o(i.usb_ept_open), (1304 bytes).
    Removing at32f403a_407_usb.o(i.usb_ept_stall), (108 bytes).
    Removing at32f403a_407_usb.o(i.usb_exit_suspend), (18 bytes).
    Removing at32f403a_407_usb.o(i.usb_flag_clear), (6 bytes).
    Removing at32f403a_407_usb.o(i.usb_flag_get), (16 bytes).
    Removing at32f403a_407_usb.o(i.usb_interrupt_enable), (20 bytes).
    Removing at32f403a_407_usb.o(i.usb_read_packet), (44 bytes).
    Removing at32f403a_407_usb.o(i.usb_remote_wkup_clear), (10 bytes).
    Removing at32f403a_407_usb.o(i.usb_remote_wkup_set), (12 bytes).
    Removing at32f403a_407_usb.o(i.usb_set_address), (20 bytes).
    Removing at32f403a_407_usb.o(i.usb_usbbufs_enable), (64 bytes).
    Removing at32f403a_407_usb.o(i.usb_write_packet), (44 bytes).
    Removing at32f403a_407_usb.o(.data), (6 bytes).
    Removing at32f403a_407_wdt.o(.rev16_text), (4 bytes).
    Removing at32f403a_407_wdt.o(.revsh_text), (4 bytes).
    Removing at32f403a_407_wdt.o(.rrx_text), (6 bytes).
    Removing at32f403a_407_wdt.o(i.wdt_flag_get), (24 bytes).
    Removing at32f403a_407_wwdt.o(.rev16_text), (4 bytes).
    Removing at32f403a_407_wwdt.o(.revsh_text), (4 bytes).
    Removing at32f403a_407_wwdt.o(.rrx_text), (6 bytes).
    Removing at32f403a_407_wwdt.o(i.wwdt_flag_clear), (12 bytes).
    Removing at32f403a_407_wwdt.o(i.wwdt_flag_get), (16 bytes).
    Removing at32f403a_407_wwdt.o(i.wwdt_interrupt_enable), (24 bytes).
    Removing at32f403a_407_wwdt.o(i.wwdt_reset), (24 bytes).
    Removing at32f403a_407_xmc.o(.rev16_text), (4 bytes).
    Removing at32f403a_407_xmc.o(.revsh_text), (4 bytes).
    Removing at32f403a_407_xmc.o(.rrx_text), (6 bytes).
    Removing at32f403a_407_xmc.o(i.xmc_ecc_get), (16 bytes).
    Removing at32f403a_407_xmc.o(i.xmc_ext_timing_config), (40 bytes).
    Removing at32f403a_407_xmc.o(i.xmc_flag_clear), (36 bytes).
    Removing at32f403a_407_xmc.o(i.xmc_flag_status_get), (32 bytes).
    Removing at32f403a_407_xmc.o(i.xmc_interrupt_enable), (44 bytes).
    Removing at32f403a_407_xmc.o(i.xmc_nand_default_para_init), (20 bytes).
    Removing at32f403a_407_xmc.o(i.xmc_nand_ecc_enable), (22 bytes).
    Removing at32f403a_407_xmc.o(i.xmc_nand_enable), (22 bytes).
    Removing at32f403a_407_xmc.o(i.xmc_nand_init), (46 bytes).
    Removing at32f403a_407_xmc.o(i.xmc_nand_reset), (26 bytes).
    Removing at32f403a_407_xmc.o(i.xmc_nand_timing_config), (62 bytes).
    Removing at32f403a_407_xmc.o(i.xmc_nand_timing_default_para_init), (30 bytes).
    Removing at32f403a_407_xmc.o(i.xmc_nor_sram_enable), (22 bytes).
    Removing at32f403a_407_xmc.o(i.xmc_nor_sram_init), (90 bytes).
    Removing at32f403a_407_xmc.o(i.xmc_nor_sram_reset), (56 bytes).
    Removing at32f403a_407_xmc.o(i.xmc_nor_sram_timing_config), (124 bytes).
    Removing at32f403a_407_xmc.o(i.xmc_norsram_default_para_init), (42 bytes).
    Removing at32f403a_407_xmc.o(i.xmc_norsram_timing_default_para_init), (56 bytes).
    Removing at32_selftest_startup.o(.rev16_text), (4 bytes).
    Removing at32_selftest_startup.o(.revsh_text), (4 bytes).
    Removing at32_selftest_startup.o(.rrx_text), (6 bytes).
    Removing at32_selftest_runtime.o(.rev16_text), (4 bytes).
    Removing at32_selftest_runtime.o(.revsh_text), (4 bytes).
    Removing at32_selftest_runtime.o(.rrx_text), (6 bytes).
    Removing at32_selftest_crc.o(.rev16_text), (4 bytes).
    Removing at32_selftest_crc.o(.revsh_text), (4 bytes).
    Removing at32_selftest_crc.o(.rrx_text), (6 bytes).
    Removing at32_selftest_ram.o(.rev16_text), (4 bytes).
    Removing at32_selftest_ram.o(.revsh_text), (4 bytes).
    Removing at32_selftest_ram.o(.rrx_text), (6 bytes).
    Removing at32_selftest_clock.o(.rev16_text), (4 bytes).
    Removing at32_selftest_clock.o(.revsh_text), (4 bytes).
    Removing at32_selftest_clock.o(.rrx_text), (6 bytes).
    Removing system_at32f403a_407.o(.rev16_text), (4 bytes).
    Removing system_at32f403a_407.o(.revsh_text), (4 bytes).
    Removing system_at32f403a_407.o(.rrx_text), (6 bytes).

541 unused section(s) (total 20954 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi_2.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_file.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2printf.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_d.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  putc.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror_locked.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ..\..\..\libraries\cmsis\cm4\device_support\startup\mdk\startup_at32f403a_407.s 0x00000000   Number         0  startup_at32f403a_407.o ABSOLUTE
    ..\..\..\libraries\cmsis\cm4\device_support\system_at32f403a_407.c 0x00000000   Number         0  system_at32f403a_407.o ABSOLUTE
    ..\..\..\libraries\drivers\src\at32f403a_407_acc.c 0x00000000   Number         0  at32f403a_407_acc.o ABSOLUTE
    ..\..\..\libraries\drivers\src\at32f403a_407_adc.c 0x00000000   Number         0  at32f403a_407_adc.o ABSOLUTE
    ..\..\..\libraries\drivers\src\at32f403a_407_bpr.c 0x00000000   Number         0  at32f403a_407_bpr.o ABSOLUTE
    ..\..\..\libraries\drivers\src\at32f403a_407_can.c 0x00000000   Number         0  at32f403a_407_can.o ABSOLUTE
    ..\..\..\libraries\drivers\src\at32f403a_407_crc.c 0x00000000   Number         0  at32f403a_407_crc.o ABSOLUTE
    ..\..\..\libraries\drivers\src\at32f403a_407_crm.c 0x00000000   Number         0  at32f403a_407_crm.o ABSOLUTE
    ..\..\..\libraries\drivers\src\at32f403a_407_dac.c 0x00000000   Number         0  at32f403a_407_dac.o ABSOLUTE
    ..\..\..\libraries\drivers\src\at32f403a_407_debug.c 0x00000000   Number         0  at32f403a_407_debug.o ABSOLUTE
    ..\..\..\libraries\drivers\src\at32f403a_407_dma.c 0x00000000   Number         0  at32f403a_407_dma.o ABSOLUTE
    ..\..\..\libraries\drivers\src\at32f403a_407_emac.c 0x00000000   Number         0  at32f403a_407_emac.o ABSOLUTE
    ..\..\..\libraries\drivers\src\at32f403a_407_exint.c 0x00000000   Number         0  at32f403a_407_exint.o ABSOLUTE
    ..\..\..\libraries\drivers\src\at32f403a_407_flash.c 0x00000000   Number         0  at32f403a_407_flash.o ABSOLUTE
    ..\..\..\libraries\drivers\src\at32f403a_407_gpio.c 0x00000000   Number         0  at32f403a_407_gpio.o ABSOLUTE
    ..\..\..\libraries\drivers\src\at32f403a_407_i2c.c 0x00000000   Number         0  at32f403a_407_i2c.o ABSOLUTE
    ..\..\..\libraries\drivers\src\at32f403a_407_misc.c 0x00000000   Number         0  at32f403a_407_misc.o ABSOLUTE
    ..\..\..\libraries\drivers\src\at32f403a_407_pwc.c 0x00000000   Number         0  at32f403a_407_pwc.o ABSOLUTE
    ..\..\..\libraries\drivers\src\at32f403a_407_rtc.c 0x00000000   Number         0  at32f403a_407_rtc.o ABSOLUTE
    ..\..\..\libraries\drivers\src\at32f403a_407_sdio.c 0x00000000   Number         0  at32f403a_407_sdio.o ABSOLUTE
    ..\..\..\libraries\drivers\src\at32f403a_407_spi.c 0x00000000   Number         0  at32f403a_407_spi.o ABSOLUTE
    ..\..\..\libraries\drivers\src\at32f403a_407_tmr.c 0x00000000   Number         0  at32f403a_407_tmr.o ABSOLUTE
    ..\..\..\libraries\drivers\src\at32f403a_407_usart.c 0x00000000   Number         0  at32f403a_407_usart.o ABSOLUTE
    ..\..\..\libraries\drivers\src\at32f403a_407_usb.c 0x00000000   Number         0  at32f403a_407_usb.o ABSOLUTE
    ..\..\..\libraries\drivers\src\at32f403a_407_wdt.c 0x00000000   Number         0  at32f403a_407_wdt.o ABSOLUTE
    ..\..\..\libraries\drivers\src\at32f403a_407_wwdt.c 0x00000000   Number         0  at32f403a_407_wwdt.o ABSOLUTE
    ..\..\..\libraries\drivers\src\at32f403a_407_xmc.c 0x00000000   Number         0  at32f403a_407_xmc.o ABSOLUTE
    ..\..\..\middlewares\classb_lib\src\at32_selftest_clock.c 0x00000000   Number         0  at32_selftest_clock.o ABSOLUTE
    ..\..\..\middlewares\classb_lib\src\at32_selftest_crc.c 0x00000000   Number         0  at32_selftest_crc.o ABSOLUTE
    ..\..\..\middlewares\classb_lib\src\at32_selftest_ram.c 0x00000000   Number         0  at32_selftest_ram.o ABSOLUTE
    ..\..\..\middlewares\classb_lib\src\at32_selftest_runtime.c 0x00000000   Number         0  at32_selftest_runtime.o ABSOLUTE
    ..\..\..\middlewares\classb_lib\src\at32_selftest_startup.c 0x00000000   Number         0  at32_selftest_startup.o ABSOLUTE
    ..\..\..\project\at32f403a_407_board\at32f403a_407_board.c 0x00000000   Number         0  at32f403a_407_board.o ABSOLUTE
    ..\\..\\..\\libraries\\cmsis\\cm4\\device_support\\system_at32f403a_407.c 0x00000000   Number         0  system_at32f403a_407.o ABSOLUTE
    ..\\..\\..\\libraries\\drivers\\src\\at32f403a_407_acc.c 0x00000000   Number         0  at32f403a_407_acc.o ABSOLUTE
    ..\\..\\..\\libraries\\drivers\\src\\at32f403a_407_adc.c 0x00000000   Number         0  at32f403a_407_adc.o ABSOLUTE
    ..\\..\\..\\libraries\\drivers\\src\\at32f403a_407_bpr.c 0x00000000   Number         0  at32f403a_407_bpr.o ABSOLUTE
    ..\\..\\..\\libraries\\drivers\\src\\at32f403a_407_can.c 0x00000000   Number         0  at32f403a_407_can.o ABSOLUTE
    ..\\..\\..\\libraries\\drivers\\src\\at32f403a_407_crc.c 0x00000000   Number         0  at32f403a_407_crc.o ABSOLUTE
    ..\\..\\..\\libraries\\drivers\\src\\at32f403a_407_crm.c 0x00000000   Number         0  at32f403a_407_crm.o ABSOLUTE
    ..\\..\\..\\libraries\\drivers\\src\\at32f403a_407_dac.c 0x00000000   Number         0  at32f403a_407_dac.o ABSOLUTE
    ..\\..\\..\\libraries\\drivers\\src\\at32f403a_407_debug.c 0x00000000   Number         0  at32f403a_407_debug.o ABSOLUTE
    ..\\..\\..\\libraries\\drivers\\src\\at32f403a_407_dma.c 0x00000000   Number         0  at32f403a_407_dma.o ABSOLUTE
    ..\\..\\..\\libraries\\drivers\\src\\at32f403a_407_emac.c 0x00000000   Number         0  at32f403a_407_emac.o ABSOLUTE
    ..\\..\\..\\libraries\\drivers\\src\\at32f403a_407_exint.c 0x00000000   Number         0  at32f403a_407_exint.o ABSOLUTE
    ..\\..\\..\\libraries\\drivers\\src\\at32f403a_407_flash.c 0x00000000   Number         0  at32f403a_407_flash.o ABSOLUTE
    ..\\..\\..\\libraries\\drivers\\src\\at32f403a_407_gpio.c 0x00000000   Number         0  at32f403a_407_gpio.o ABSOLUTE
    ..\\..\\..\\libraries\\drivers\\src\\at32f403a_407_i2c.c 0x00000000   Number         0  at32f403a_407_i2c.o ABSOLUTE
    ..\\..\\..\\libraries\\drivers\\src\\at32f403a_407_misc.c 0x00000000   Number         0  at32f403a_407_misc.o ABSOLUTE
    ..\\..\\..\\libraries\\drivers\\src\\at32f403a_407_pwc.c 0x00000000   Number         0  at32f403a_407_pwc.o ABSOLUTE
    ..\\..\\..\\libraries\\drivers\\src\\at32f403a_407_rtc.c 0x00000000   Number         0  at32f403a_407_rtc.o ABSOLUTE
    ..\\..\\..\\libraries\\drivers\\src\\at32f403a_407_sdio.c 0x00000000   Number         0  at32f403a_407_sdio.o ABSOLUTE
    ..\\..\\..\\libraries\\drivers\\src\\at32f403a_407_spi.c 0x00000000   Number         0  at32f403a_407_spi.o ABSOLUTE
    ..\\..\\..\\libraries\\drivers\\src\\at32f403a_407_tmr.c 0x00000000   Number         0  at32f403a_407_tmr.o ABSOLUTE
    ..\\..\\..\\libraries\\drivers\\src\\at32f403a_407_usart.c 0x00000000   Number         0  at32f403a_407_usart.o ABSOLUTE
    ..\\..\\..\\libraries\\drivers\\src\\at32f403a_407_usb.c 0x00000000   Number         0  at32f403a_407_usb.o ABSOLUTE
    ..\\..\\..\\libraries\\drivers\\src\\at32f403a_407_wdt.c 0x00000000   Number         0  at32f403a_407_wdt.o ABSOLUTE
    ..\\..\\..\\libraries\\drivers\\src\\at32f403a_407_wwdt.c 0x00000000   Number         0  at32f403a_407_wwdt.o ABSOLUTE
    ..\\..\\..\\libraries\\drivers\\src\\at32f403a_407_xmc.c 0x00000000   Number         0  at32f403a_407_xmc.o ABSOLUTE
    ..\\..\\..\\middlewares\\classb_lib\\src\\at32_selftest_clock.c 0x00000000   Number         0  at32_selftest_clock.o ABSOLUTE
    ..\\..\\..\\middlewares\\classb_lib\\src\\at32_selftest_crc.c 0x00000000   Number         0  at32_selftest_crc.o ABSOLUTE
    ..\\..\\..\\middlewares\\classb_lib\\src\\at32_selftest_ram.c 0x00000000   Number         0  at32_selftest_ram.o ABSOLUTE
    ..\\..\\..\\middlewares\\classb_lib\\src\\at32_selftest_runtime.c 0x00000000   Number         0  at32_selftest_runtime.o ABSOLUTE
    ..\\..\\..\\middlewares\\classb_lib\\src\\at32_selftest_startup.c 0x00000000   Number         0  at32_selftest_startup.o ABSOLUTE
    ..\\..\\..\\project\\at32f403a_407_board\\at32f403a_407_board.c 0x00000000   Number         0  at32f403a_407_board.o ABSOLUTE
    ..\\src\\at32f403a_407_clock.c           0x00000000   Number         0  at32f403a_407_clock.o ABSOLUTE
    ..\\src\\at32f403a_407_int.c             0x00000000   Number         0  at32f403a_407_int.o ABSOLUTE
    ..\\src\\main.c                          0x00000000   Number         0  main.o ABSOLUTE
    ..\src\at32f403a_407_clock.c             0x00000000   Number         0  at32f403a_407_clock.o ABSOLUTE
    ..\src\at32f403a_407_int.c               0x00000000   Number         0  at32f403a_407_int.o ABSOLUTE
    ..\src\main.c                            0x00000000   Number         0  main.o ABSOLUTE
    at32_selftest_cpurun_keil.s              0x00000000   Number         0  at32_selftest_cpurun_keil.o ABSOLUTE
    at32_selftest_cpustart_keil.s            0x00000000   Number         0  at32_selftest_cpustart_keil.o ABSOLUTE
    at32_selftest_ram_keil.s                 0x00000000   Number         0  at32_selftest_ram_keil.o ABSOLUTE
    crc32.c                                  0x00000000   Number         0  crc32.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    RESET                                    0x08000000   Section      388  startup_at32f403a_407.o(RESET)
    !!!main                                  0x08000184   Section        8  __main.o(!!!main)
    !!!scatter                               0x0800018c   Section       52  __scatter.o(!!!scatter)
    !!handler_copy                           0x080001c0   Section       26  __scatter_copy.o(!!handler_copy)
    .ARM.Collect$$_printf_percent$$00000000  0x080001da   Section        0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    .ARM.Collect$$_printf_percent$$00000009  0x080001da   Section        6  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    .ARM.Collect$$_printf_percent$$00000017  0x080001e0   Section        4  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    .ARM.Collect$$libinit$$00000000          0x080001e4   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000001          0x080001e6   Section        4  libinit2.o(.ARM.Collect$$libinit$$00000001)
    .ARM.Collect$$libinit$$00000004          0x080001ea   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x080001ea   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x080001ea   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x080001ea   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$00000011          0x080001ea   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000013          0x080001ea   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x080001ea   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000017          0x080001ea   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x080001ea   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x080001ea   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x080001ea   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x080001ea   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x080001ea   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x080001ea   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x080001ea   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x080001ea   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x080001ea   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x080001ea   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x080001ea   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x080001ea   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x080001ec   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000003      0x080001ee   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000003)
    .ARM.Collect$$libshutdown$$00000006      0x080001ee   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    .ARM.Collect$$libshutdown$$00000009      0x080001ee   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    .ARM.Collect$$libshutdown$$0000000B      0x080001ee   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000B)
    .ARM.Collect$$libshutdown$$0000000E      0x080001ee   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    .ARM.Collect$$libshutdown$$0000000F      0x080001ee   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    .ARM.Collect$$rtentry$$00000000          0x080001f0   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x080001f0   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x080001f0   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x080001f6   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x080001f6   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x080001fa   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x080001fa   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x08000202   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x08000204   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x08000204   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x08000208   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x08000210   Section      472  at32_selftest_cpurun_keil.o(.text)
    $v0                                      0x08000210   Number         0  at32_selftest_cpurun_keil.o(.text)
    .text                                    0x080003e8   Section      648  at32_selftest_cpustart_keil.o(.text)
    $v0                                      0x080003e8   Number         0  at32_selftest_cpustart_keil.o(.text)
    .text                                    0x08000670   Section      592  at32_selftest_ram_keil.o(.text)
    __STANDARD_RAM_ORDER                     0x08000670   Data           0  at32_selftest_ram_keil.o(.text)
    $v0                                      0x080006b8   Number         0  at32_selftest_ram_keil.o(.text)
    .text                                    0x080008c0   Section       64  startup_at32f403a_407.o(.text)
    $v0                                      0x080008c0   Number         0  startup_at32f403a_407.o(.text)
    .text                                    0x08000900   Section        2  use_no_semi_2.o(.text)
    .text                                    0x08000904   Section        0  noretval__2printf.o(.text)
    .text                                    0x0800091c   Section        0  __printf.o(.text)
    .text                                    0x08000984   Section        0  _printf_dec.o(.text)
    .text                                    0x080009fc   Section        0  putc.o(.text)
    .text                                    0x08000a00   Section        0  heapauxi.o(.text)
    .text                                    0x08000a06   Section        2  use_no_semi.o(.text)
    .text                                    0x08000a08   Section        0  _printf_intcommon.o(.text)
    .text                                    0x08000abc   Section        0  _printf_char_file.o(.text)
    .text                                    0x08000ae0   Section        0  _printf_char_common.o(.text)
    _printf_input_char                       0x08000ae1   Thumb Code    10  _printf_char_common.o(.text)
    .text                                    0x08000b10   Section        0  ferror.o(.text)
    .text                                    0x08000b18   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x08000b62   Section        0  exit.o(.text)
    .text                                    0x08000b70   Section        8  libspace.o(.text)
    i.$Sub$$main                             0x08000b78   Section        0  at32_selftest_startup.o(i.$Sub$$main)
    i.BusFault_Handler                       0x08000ba0   Section        0  at32f403a_407_int.o(i.BusFault_Handler)
    i.DebugMon_Handler                       0x08000ba4   Section        0  at32f403a_407_int.o(i.DebugMon_Handler)
    i.HardFault_Handler                      0x08000ba6   Section        0  at32f403a_407_int.o(i.HardFault_Handler)
    i.MemManage_Handler                      0x08000baa   Section        0  at32f403a_407_int.o(i.MemManage_Handler)
    i.NMI_Handler                            0x08000bae   Section        0  at32f403a_407_int.o(i.NMI_Handler)
    i.PendSV_Handler                         0x08000bb0   Section        0  at32f403a_407_int.o(i.PendSV_Handler)
    i.SVC_Handler                            0x08000bb2   Section        0  at32f403a_407_int.o(i.SVC_Handler)
    i.SysTick_Handler                        0x08000bb4   Section        0  at32f403a_407_int.o(i.SysTick_Handler)
    i.SystemInit                             0x08000d00   Section        0  system_at32f403a_407.o(i.SystemInit)
    i.TMR5_GLOBAL_IRQHandler                 0x08000d74   Section        0  at32f403a_407_int.o(i.TMR5_GLOBAL_IRQHandler)
    i.UsageFault_Handler                     0x08000e08   Section        0  at32f403a_407_int.o(i.UsageFault_Handler)
    i._sys_exit                              0x08000e0c   Section        0  at32f403a_407_board.o(i._sys_exit)
    i.control_flow_check_point               0x08000e10   Section        0  at32_selftest_startup.o(i.control_flow_check_point)
    i.crc32_fsl_continuous                   0x08000e38   Section        0  crc32.o(i.crc32_fsl_continuous)
    i.crc32_fsl_single                       0x08000ed4   Section        0  crc32.o(i.crc32_fsl_single)
    i.crm_ahb_div_set                        0x08000f00   Section        0  at32f403a_407_crm.o(i.crm_ahb_div_set)
    i.crm_apb1_div_set                       0x08000f14   Section        0  at32f403a_407_crm.o(i.crm_apb1_div_set)
    i.crm_apb2_div_set                       0x08000f28   Section        0  at32f403a_407_crm.o(i.crm_apb2_div_set)
    i.crm_auto_step_mode_enable              0x08000f3c   Section        0  at32f403a_407_crm.o(i.crm_auto_step_mode_enable)
    i.crm_clock_failure_detection_enable     0x08000f64   Section        0  at32f403a_407_crm.o(i.crm_clock_failure_detection_enable)
    i.crm_clock_source_enable                0x08000f78   Section        0  at32f403a_407_crm.o(i.crm_clock_source_enable)
    i.crm_clocks_freq_get                    0x08000fd8   Section        0  at32f403a_407_crm.o(i.crm_clocks_freq_get)
    i.crm_flag_clear                         0x08001104   Section        0  at32f403a_407_crm.o(i.crm_flag_clear)
    i.crm_flag_get                           0x080011cc   Section        0  at32f403a_407_crm.o(i.crm_flag_get)
    i.crm_hext_clock_div_set                 0x080011fc   Section        0  at32f403a_407_crm.o(i.crm_hext_clock_div_set)
    i.crm_hext_stable_wait                   0x08001210   Section        0  at32f403a_407_crm.o(i.crm_hext_stable_wait)
    i.crm_periph_clock_enable                0x08001240   Section        0  at32f403a_407_crm.o(i.crm_periph_clock_enable)
    i.crm_pll_config                         0x08001284   Section        0  at32f403a_407_crm.o(i.crm_pll_config)
    i.crm_reset                              0x080012f0   Section        0  at32f403a_407_crm.o(i.crm_reset)
    i.crm_sysclk_switch                      0x0800134c   Section        0  at32f403a_407_crm.o(i.crm_sysclk_switch)
    i.crm_sysclk_switch_status_get           0x08001360   Section        0  at32f403a_407_crm.o(i.crm_sysclk_switch_status_get)
    i.debug_periph_mode_set                  0x08001370   Section        0  at32f403a_407_debug.o(i.debug_periph_mode_set)
    i.fputc                                  0x08001390   Section        0  at32f403a_407_board.o(i.fputc)
    i.gpio_default_para_init                 0x080013b4   Section        0  at32f403a_407_gpio.o(i.gpio_default_para_init)
    i.gpio_init                              0x080013ca   Section        0  at32f403a_407_gpio.o(i.gpio_init)
    i.gpio_pin_remap_config                  0x08001474   Section        0  at32f403a_407_gpio.o(i.gpio_pin_remap_config)
    i.main                                   0x080014c0   Section        0  main.o(i.main)
    i.nvic_irq_enable                        0x080014e8   Section        0  at32f403a_407_misc.o(i.nvic_irq_enable)
    i.nvic_priority_group_config             0x080015b0   Section        0  at32f403a_407_misc.o(i.nvic_priority_group_config)
    i.selftest_clock_cross_measurement_config 0x080015d8   Section        0  main.o(i.selftest_clock_cross_measurement_config)
    i.selftest_clock_runtime_test            0x08001674   Section        0  at32_selftest_clock.o(i.selftest_clock_runtime_test)
    i.selftest_clock_startup_test            0x080016f8   Section        0  at32_selftest_clock.o(i.selftest_clock_startup_test)
    i.selftest_crc_runtime_test              0x08001814   Section        0  at32_selftest_crc.o(i.selftest_crc_runtime_test)
    i.selftest_fail_handle                   0x080018e4   Section        0  at32_selftest_startup.o(i.selftest_fail_handle)
    i.selftest_runtime_check                 0x08001934   Section        0  at32_selftest_runtime.o(i.selftest_runtime_check)
    i.selftest_runtime_init                  0x08001bdc   Section        0  at32_selftest_runtime.o(i.selftest_runtime_init)
    i.selftest_sampling_ram_test             0x08001d40   Section        0  at32_selftest_ram.o(i.selftest_sampling_ram_test)
    i.selftest_stack_check                   0x08001dc4   Section        0  at32_selftest_runtime.o(i.selftest_stack_check)
    selftest_stack_check                     0x08001dc5   Thumb Code    66  at32_selftest_runtime.o(i.selftest_stack_check)
    i.selftest_startup_check                 0x08001e14   Section        0  at32_selftest_startup.o(i.selftest_startup_check)
    i.selftest_system_clock_config           0x0800222c   Section        0  main.o(i.selftest_system_clock_config)
    i.selftest_watchdog_test                 0x08002308   Section        0  at32_selftest_startup.o(i.selftest_watchdog_test)
    i.system_core_clock_update               0x080025b4   Section        0  system_at32f403a_407.o(i.system_core_clock_update)
    i.systick_get                            0x080026ac   Section        0  main.o(i.systick_get)
    i.systick_inc                            0x080026b8   Section        0  main.o(i.systick_inc)
    i.tmr_base_init                          0x080026c8   Section        0  at32f403a_407_tmr.o(i.tmr_base_init)
    i.tmr_clock_source_div_set               0x080026d8   Section        0  at32f403a_407_tmr.o(i.tmr_clock_source_div_set)
    i.tmr_cnt_dir_set                        0x080026e2   Section        0  at32f403a_407_tmr.o(i.tmr_cnt_dir_set)
    i.tmr_counter_enable                     0x080026ec   Section        0  at32f403a_407_tmr.o(i.tmr_counter_enable)
    i.tmr_flag_clear                         0x080026f6   Section        0  at32f403a_407_tmr.o(i.tmr_flag_clear)
    i.tmr_flag_get                           0x080026fc   Section        0  at32f403a_407_tmr.o(i.tmr_flag_get)
    i.tmr_input_channel_init                 0x0800270e   Section        0  at32f403a_407_tmr.o(i.tmr_input_channel_init)
    i.tmr_input_default_para_init            0x0800281c   Section        0  at32f403a_407_tmr.o(i.tmr_input_default_para_init)
    i.tmr_interrupt_enable                   0x0800282c   Section        0  at32f403a_407_tmr.o(i.tmr_interrupt_enable)
    i.tmr_repetition_counter_set             0x08002844   Section        0  at32f403a_407_tmr.o(i.tmr_repetition_counter_set)
    i.uart_print_init                        0x08002864   Section        0  at32f403a_407_board.o(i.uart_print_init)
    i.usart_data_transmit                    0x080028d0   Section        0  at32f403a_407_usart.o(i.usart_data_transmit)
    i.usart_enable                           0x080028d8   Section        0  at32f403a_407_usart.o(i.usart_enable)
    i.usart_flag_get                         0x080028e2   Section        0  at32f403a_407_usart.o(i.usart_flag_get)
    i.usart_init                             0x080028f4   Section        0  at32f403a_407_usart.o(i.usart_init)
    i.usart_reconfigure                      0x0800297c   Section        0  main.o(i.usart_reconfigure)
    i.usart_transmitter_enable               0x080029a4   Section        0  at32f403a_407_usart.o(i.usart_transmitter_enable)
    i.wdt_counter_reload                     0x080029b0   Section        0  at32f403a_407_wdt.o(i.wdt_counter_reload)
    i.wdt_divider_set                        0x080029c0   Section        0  at32f403a_407_wdt.o(i.wdt_divider_set)
    i.wdt_enable                             0x080029d4   Section        0  at32f403a_407_wdt.o(i.wdt_enable)
    i.wdt_register_write_enable              0x080029e4   Section        0  at32f403a_407_wdt.o(i.wdt_register_write_enable)
    i.wdt_reload_value_set                   0x080029fc   Section        0  at32f403a_407_wdt.o(i.wdt_reload_value_set)
    i.wwdt_counter_set                       0x08002a08   Section        0  at32f403a_407_wwdt.o(i.wwdt_counter_set)
    i.wwdt_divider_set                       0x08002a1c   Section        0  at32f403a_407_wwdt.o(i.wwdt_divider_set)
    i.wwdt_enable                            0x08002a30   Section        0  at32f403a_407_wwdt.o(i.wwdt_enable)
    i.wwdt_window_counter_set                0x08002a40   Section        0  at32f403a_407_wwdt.o(i.wwdt_window_counter_set)
    x$fpl$fpinit                             0x08002a54   Section       10  fpinit.o(x$fpl$fpinit)
    $v0                                      0x08002a54   Number         0  fpinit.o(x$fpl$fpinit)
    .constdata                               0x08002a60   Section     1024  crc32.o(.constdata)
    crcTable                                 0x08002a60   Data        1024  crc32.o(.constdata)
    .constdata                               0x08002e60   Section       40  at32f403a_407_crm.o(.constdata)
    sclk_ahb_div_table                       0x08002e60   Data          16  at32f403a_407_crm.o(.constdata)
    ahb_apb1_div_table                       0x08002e70   Data           8  at32f403a_407_crm.o(.constdata)
    ahb_apb2_div_table                       0x08002e78   Data           8  at32f403a_407_crm.o(.constdata)
    adc_div_table                            0x08002e80   Data           8  at32f403a_407_crm.o(.constdata)
    .constdata                               0x08002e88   Section       16  system_at32f403a_407.o(.constdata)
    sys_ahb_div_table                        0x08002e88   Data          16  system_at32f403a_407.o(.constdata)
    .conststring                             0x08002e98   Section       71  main.o(.conststring)
    .conststring                             0x08002ee0   Section       71  at32_selftest_startup.o(.conststring)
    RUNTIME_RAM_BUF                          0x20000000   Section       32  at32_selftest_startup.o(RUNTIME_RAM_BUF)
    RUNTIME_RAM_POINTER                      0x20000020   Section       16  at32_selftest_startup.o(RUNTIME_RAM_POINTER)
    CLASS_B_RAM                              0x20000030   Section       36  at32_selftest_startup.o(CLASS_B_RAM)
    CLASS_B_RAM_REV                          0x20000054   Section       36  at32_selftest_startup.o(CLASS_B_RAM_REV)
    .data                                    0x20000078   Section        4  main.o(.data)
    .data                                    0x2000007c   Section        2  at32f403a_407_int.o(.data)
    c4dt_val                                 0x2000007c   Data           2  at32f403a_407_int.o(.data)
    .data                                    0x20000080   Section        4  crc32.o(.data)
    crc_final_val                            0x20000080   Data           4  crc32.o(.data)
    .data                                    0x20000084   Section       45  at32f403a_407_board.o(.data)
    fac_us                                   0x200000a4   Data           4  at32f403a_407_board.o(.data)
    fac_ms                                   0x200000a8   Data           4  at32f403a_407_board.o(.data)
    pressed                                  0x200000b0   Data           1  at32f403a_407_board.o(.data)
    .data                                    0x200000b4   Section        4  system_at32f403a_407.o(.data)
    .bss                                     0x200000b8   Section       96  libspace.o(.bss)
    HEAP                                     0x20000118   Section      512  startup_at32f403a_407.o(HEAP)
    Heap_Mem                                 0x20000118   Data         512  startup_at32f403a_407.o(HEAP)
    STACK_BOTTOM                             0x20001078   Section       16  at32_selftest_startup.o(STACK_BOTTOM)
    STACK                                    0x20001088   Section     1024  startup_at32f403a_407.o(STACK)
    Stack_Mem                                0x20001088   Data        1024  startup_at32f403a_407.o(STACK)
    __initial_sp                             0x20001488   Data           0  startup_at32f403a_407.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OSPACE$ROPI$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __rt_locale                               - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_ctype                             - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_numeric                           - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _mutex_acquire                            - Undefined Weak Reference
    _mutex_release                            - Undefined Weak Reference
    _printf_post_padding                      - Undefined Weak Reference
    _printf_pre_padding                       - Undefined Weak Reference
    _printf_truncate_signed                   - Undefined Weak Reference
    _printf_truncate_unsigned                 - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x00000184   Number         0  startup_at32f403a_407.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_at32f403a_407.o(RESET)
    __Vectors_End                            0x08000184   Data           0  startup_at32f403a_407.o(RESET)
    __main                                   0x08000185   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x0800018d   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x0800018d   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x0800018d   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x0800019b   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x080001c1   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    _printf_d                                0x080001db   Thumb Code     0  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    _printf_percent                          0x080001db   Thumb Code     0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    _printf_percent_end                      0x080001e1   Thumb Code     0  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    __rt_lib_init                            0x080001e5   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x080001e7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000001)
    __rt_lib_init_alloca_1                   0x080001eb   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x080001eb   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x080001eb   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x080001eb   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x080001eb   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x080001eb   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_trap_1                  0x080001eb   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x080001eb   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_heap_1                     0x080001eb   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_collate_1               0x080001eb   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_1                 0x080001eb   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x080001eb   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_1               0x080001eb   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x080001eb   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_preinit_1                  0x080001eb   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x080001eb   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_return                     0x080001eb   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x080001eb   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x080001eb   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_init_user_alloc_1               0x080001eb   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_shutdown                        0x080001ed   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_fp_trap_1              0x080001ef   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    __rt_lib_shutdown_heap_1                 0x080001ef   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    __rt_lib_shutdown_return                 0x080001ef   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    __rt_lib_shutdown_signal_1               0x080001ef   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    __rt_lib_shutdown_stdio_1                0x080001ef   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000003)
    __rt_lib_shutdown_user_alloc_1           0x080001ef   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000B)
    __rt_entry                               0x080001f1   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x080001f1   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x080001f1   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x080001f7   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x080001f7   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x080001fb   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x080001fb   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x08000203   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x08000205   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x08000205   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x08000209   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    selftest_cpu_runtime_test                0x08000211   Thumb Code   464  at32_selftest_cpurun_keil.o(.text)
    selftest_cpu_startup_test                0x080003e9   Thumb Code   640  at32_selftest_cpustart_keil.o(.text)
    selftest_full_ram_test                   0x080006b9   Thumb Code     0  at32_selftest_ram_keil.o(.text)
    selftest_ram_step_implement              0x0800078f   Thumb Code     0  at32_selftest_ram_keil.o(.text)
    Reset_Handler                            0x080008c1   Thumb Code     8  startup_at32f403a_407.o(.text)
    ACC_IRQHandler                           0x080008db   Thumb Code     0  startup_at32f403a_407.o(.text)
    ADC1_2_IRQHandler                        0x080008db   Thumb Code     0  startup_at32f403a_407.o(.text)
    ADC3_IRQHandler                          0x080008db   Thumb Code     0  startup_at32f403a_407.o(.text)
    CAN1_RX1_IRQHandler                      0x080008db   Thumb Code     0  startup_at32f403a_407.o(.text)
    CAN1_SE_IRQHandler                       0x080008db   Thumb Code     0  startup_at32f403a_407.o(.text)
    CAN2_RX0_IRQHandler                      0x080008db   Thumb Code     0  startup_at32f403a_407.o(.text)
    CAN2_RX1_IRQHandler                      0x080008db   Thumb Code     0  startup_at32f403a_407.o(.text)
    CAN2_SE_IRQHandler                       0x080008db   Thumb Code     0  startup_at32f403a_407.o(.text)
    CAN2_TX_IRQHandler                       0x080008db   Thumb Code     0  startup_at32f403a_407.o(.text)
    CRM_IRQHandler                           0x080008db   Thumb Code     0  startup_at32f403a_407.o(.text)
    DMA1_Channel1_IRQHandler                 0x080008db   Thumb Code     0  startup_at32f403a_407.o(.text)
    DMA1_Channel2_IRQHandler                 0x080008db   Thumb Code     0  startup_at32f403a_407.o(.text)
    DMA1_Channel3_IRQHandler                 0x080008db   Thumb Code     0  startup_at32f403a_407.o(.text)
    DMA1_Channel4_IRQHandler                 0x080008db   Thumb Code     0  startup_at32f403a_407.o(.text)
    DMA1_Channel5_IRQHandler                 0x080008db   Thumb Code     0  startup_at32f403a_407.o(.text)
    DMA1_Channel6_IRQHandler                 0x080008db   Thumb Code     0  startup_at32f403a_407.o(.text)
    DMA1_Channel7_IRQHandler                 0x080008db   Thumb Code     0  startup_at32f403a_407.o(.text)
    DMA2_Channel1_IRQHandler                 0x080008db   Thumb Code     0  startup_at32f403a_407.o(.text)
    DMA2_Channel2_IRQHandler                 0x080008db   Thumb Code     0  startup_at32f403a_407.o(.text)
    DMA2_Channel3_IRQHandler                 0x080008db   Thumb Code     0  startup_at32f403a_407.o(.text)
    DMA2_Channel4_5_IRQHandler               0x080008db   Thumb Code     0  startup_at32f403a_407.o(.text)
    DMA2_Channel6_7_IRQHandler               0x080008db   Thumb Code     0  startup_at32f403a_407.o(.text)
    EMAC_IRQHandler                          0x080008db   Thumb Code     0  startup_at32f403a_407.o(.text)
    EMAC_WKUP_IRQHandler                     0x080008db   Thumb Code     0  startup_at32f403a_407.o(.text)
    EXINT0_IRQHandler                        0x080008db   Thumb Code     0  startup_at32f403a_407.o(.text)
    EXINT15_10_IRQHandler                    0x080008db   Thumb Code     0  startup_at32f403a_407.o(.text)
    EXINT1_IRQHandler                        0x080008db   Thumb Code     0  startup_at32f403a_407.o(.text)
    EXINT2_IRQHandler                        0x080008db   Thumb Code     0  startup_at32f403a_407.o(.text)
    EXINT3_IRQHandler                        0x080008db   Thumb Code     0  startup_at32f403a_407.o(.text)
    EXINT4_IRQHandler                        0x080008db   Thumb Code     0  startup_at32f403a_407.o(.text)
    EXINT9_5_IRQHandler                      0x080008db   Thumb Code     0  startup_at32f403a_407.o(.text)
    FLASH_IRQHandler                         0x080008db   Thumb Code     0  startup_at32f403a_407.o(.text)
    I2C1_ERR_IRQHandler                      0x080008db   Thumb Code     0  startup_at32f403a_407.o(.text)
    I2C1_EVT_IRQHandler                      0x080008db   Thumb Code     0  startup_at32f403a_407.o(.text)
    I2C2_ERR_IRQHandler                      0x080008db   Thumb Code     0  startup_at32f403a_407.o(.text)
    I2C2_EVT_IRQHandler                      0x080008db   Thumb Code     0  startup_at32f403a_407.o(.text)
    I2C3_ERR_IRQHandler                      0x080008db   Thumb Code     0  startup_at32f403a_407.o(.text)
    I2C3_EVT_IRQHandler                      0x080008db   Thumb Code     0  startup_at32f403a_407.o(.text)
    PVM_IRQHandler                           0x080008db   Thumb Code     0  startup_at32f403a_407.o(.text)
    RTCAlarm_IRQHandler                      0x080008db   Thumb Code     0  startup_at32f403a_407.o(.text)
    RTC_IRQHandler                           0x080008db   Thumb Code     0  startup_at32f403a_407.o(.text)
    SDIO1_IRQHandler                         0x080008db   Thumb Code     0  startup_at32f403a_407.o(.text)
    SDIO2_IRQHandler                         0x080008db   Thumb Code     0  startup_at32f403a_407.o(.text)
    SPI1_IRQHandler                          0x080008db   Thumb Code     0  startup_at32f403a_407.o(.text)
    SPI2_I2S2EXT_IRQHandler                  0x080008db   Thumb Code     0  startup_at32f403a_407.o(.text)
    SPI3_I2S3EXT_IRQHandler                  0x080008db   Thumb Code     0  startup_at32f403a_407.o(.text)
    SPI4_IRQHandler                          0x080008db   Thumb Code     0  startup_at32f403a_407.o(.text)
    TAMPER_IRQHandler                        0x080008db   Thumb Code     0  startup_at32f403a_407.o(.text)
    TMR1_BRK_TMR9_IRQHandler                 0x080008db   Thumb Code     0  startup_at32f403a_407.o(.text)
    TMR1_CH_IRQHandler                       0x080008db   Thumb Code     0  startup_at32f403a_407.o(.text)
    TMR1_OVF_TMR10_IRQHandler                0x080008db   Thumb Code     0  startup_at32f403a_407.o(.text)
    TMR1_TRG_HALL_TMR11_IRQHandler           0x080008db   Thumb Code     0  startup_at32f403a_407.o(.text)
    TMR2_GLOBAL_IRQHandler                   0x080008db   Thumb Code     0  startup_at32f403a_407.o(.text)
    TMR3_GLOBAL_IRQHandler                   0x080008db   Thumb Code     0  startup_at32f403a_407.o(.text)
    TMR4_GLOBAL_IRQHandler                   0x080008db   Thumb Code     0  startup_at32f403a_407.o(.text)
    TMR6_GLOBAL_IRQHandler                   0x080008db   Thumb Code     0  startup_at32f403a_407.o(.text)
    TMR7_GLOBAL_IRQHandler                   0x080008db   Thumb Code     0  startup_at32f403a_407.o(.text)
    TMR8_BRK_TMR12_IRQHandler                0x080008db   Thumb Code     0  startup_at32f403a_407.o(.text)
    TMR8_CH_IRQHandler                       0x080008db   Thumb Code     0  startup_at32f403a_407.o(.text)
    TMR8_OVF_TMR13_IRQHandler                0x080008db   Thumb Code     0  startup_at32f403a_407.o(.text)
    TMR8_TRG_HALL_TMR14_IRQHandler           0x080008db   Thumb Code     0  startup_at32f403a_407.o(.text)
    UART4_IRQHandler                         0x080008db   Thumb Code     0  startup_at32f403a_407.o(.text)
    UART5_IRQHandler                         0x080008db   Thumb Code     0  startup_at32f403a_407.o(.text)
    UART7_IRQHandler                         0x080008db   Thumb Code     0  startup_at32f403a_407.o(.text)
    UART8_IRQHandler                         0x080008db   Thumb Code     0  startup_at32f403a_407.o(.text)
    USART1_IRQHandler                        0x080008db   Thumb Code     0  startup_at32f403a_407.o(.text)
    USART2_IRQHandler                        0x080008db   Thumb Code     0  startup_at32f403a_407.o(.text)
    USART3_IRQHandler                        0x080008db   Thumb Code     0  startup_at32f403a_407.o(.text)
    USART6_IRQHandler                        0x080008db   Thumb Code     0  startup_at32f403a_407.o(.text)
    USBFSWakeUp_IRQHandler                   0x080008db   Thumb Code     0  startup_at32f403a_407.o(.text)
    USBFS_H_CAN1_TX_IRQHandler               0x080008db   Thumb Code     0  startup_at32f403a_407.o(.text)
    USBFS_L_CAN1_RX0_IRQHandler              0x080008db   Thumb Code     0  startup_at32f403a_407.o(.text)
    USBFS_MAPH_IRQHandler                    0x080008db   Thumb Code     0  startup_at32f403a_407.o(.text)
    USBFS_MAPL_IRQHandler                    0x080008db   Thumb Code     0  startup_at32f403a_407.o(.text)
    WWDT_IRQHandler                          0x080008db   Thumb Code     0  startup_at32f403a_407.o(.text)
    XMC_IRQHandler                           0x080008db   Thumb Code     0  startup_at32f403a_407.o(.text)
    __user_initial_stackheap                 0x080008dd   Thumb Code     0  startup_at32f403a_407.o(.text)
    __use_no_semihosting                     0x08000901   Thumb Code     2  use_no_semi_2.o(.text)
    __2printf                                0x08000905   Thumb Code    20  noretval__2printf.o(.text)
    __printf                                 0x0800091d   Thumb Code   104  __printf.o(.text)
    _printf_int_dec                          0x08000985   Thumb Code   104  _printf_dec.o(.text)
    putc                                     0x080009fd   Thumb Code     4  putc.o(.text)
    __use_two_region_memory                  0x08000a01   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x08000a03   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x08000a05   Thumb Code     2  heapauxi.o(.text)
    __I$use$semihosting                      0x08000a07   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x08000a07   Thumb Code     2  use_no_semi.o(.text)
    _printf_int_common                       0x08000a09   Thumb Code   178  _printf_intcommon.o(.text)
    _printf_char_file                        0x08000abd   Thumb Code    32  _printf_char_file.o(.text)
    _printf_char_common                      0x08000aeb   Thumb Code    32  _printf_char_common.o(.text)
    ferror                                   0x08000b11   Thumb Code     8  ferror.o(.text)
    __user_setup_stackheap                   0x08000b19   Thumb Code    74  sys_stackheap_outer.o(.text)
    exit                                     0x08000b63   Thumb Code    12  exit.o(.text)
    __user_libspace                          0x08000b71   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x08000b71   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x08000b71   Thumb Code     0  libspace.o(.text)
    main                                     0x08000b79   Thumb Code    30  at32_selftest_startup.o(i.$Sub$$main)
    BusFault_Handler                         0x08000ba1   Thumb Code     4  at32f403a_407_int.o(i.BusFault_Handler)
    DebugMon_Handler                         0x08000ba5   Thumb Code     2  at32f403a_407_int.o(i.DebugMon_Handler)
    HardFault_Handler                        0x08000ba7   Thumb Code     4  at32f403a_407_int.o(i.HardFault_Handler)
    MemManage_Handler                        0x08000bab   Thumb Code     4  at32f403a_407_int.o(i.MemManage_Handler)
    NMI_Handler                              0x08000baf   Thumb Code     2  at32f403a_407_int.o(i.NMI_Handler)
    PendSV_Handler                           0x08000bb1   Thumb Code     2  at32f403a_407_int.o(i.PendSV_Handler)
    SVC_Handler                              0x08000bb3   Thumb Code     2  at32f403a_407_int.o(i.SVC_Handler)
    SysTick_Handler                          0x08000bb5   Thumb Code   218  at32f403a_407_int.o(i.SysTick_Handler)
    SystemInit                               0x08000d01   Thumb Code   102  system_at32f403a_407.o(i.SystemInit)
    TMR5_GLOBAL_IRQHandler                   0x08000d75   Thumb Code   124  at32f403a_407_int.o(i.TMR5_GLOBAL_IRQHandler)
    UsageFault_Handler                       0x08000e09   Thumb Code     4  at32f403a_407_int.o(i.UsageFault_Handler)
    _sys_exit                                0x08000e0d   Thumb Code     4  at32f403a_407_board.o(i._sys_exit)
    control_flow_check_point                 0x08000e11   Thumb Code    30  at32_selftest_startup.o(i.control_flow_check_point)
    crc32_fsl_continuous                     0x08000e39   Thumb Code   148  crc32.o(i.crc32_fsl_continuous)
    crc32_fsl_single                         0x08000ed5   Thumb Code    40  crc32.o(i.crc32_fsl_single)
    crm_ahb_div_set                          0x08000f01   Thumb Code    14  at32f403a_407_crm.o(i.crm_ahb_div_set)
    crm_apb1_div_set                         0x08000f15   Thumb Code    14  at32f403a_407_crm.o(i.crm_apb1_div_set)
    crm_apb2_div_set                         0x08000f29   Thumb Code    14  at32f403a_407_crm.o(i.crm_apb2_div_set)
    crm_auto_step_mode_enable                0x08000f3d   Thumb Code    34  at32f403a_407_crm.o(i.crm_auto_step_mode_enable)
    crm_clock_failure_detection_enable       0x08000f65   Thumb Code    14  at32f403a_407_crm.o(i.crm_clock_failure_detection_enable)
    crm_clock_source_enable                  0x08000f79   Thumb Code    90  at32f403a_407_crm.o(i.crm_clock_source_enable)
    crm_clocks_freq_get                      0x08000fd9   Thumb Code   268  at32f403a_407_crm.o(i.crm_clocks_freq_get)
    crm_flag_clear                           0x08001105   Thumb Code   190  at32f403a_407_crm.o(i.crm_flag_clear)
    crm_flag_get                             0x080011cd   Thumb Code    42  at32f403a_407_crm.o(i.crm_flag_get)
    crm_hext_clock_div_set                   0x080011fd   Thumb Code    14  at32f403a_407_crm.o(i.crm_hext_clock_div_set)
    crm_hext_stable_wait                     0x08001211   Thumb Code    46  at32f403a_407_crm.o(i.crm_hext_stable_wait)
    crm_periph_clock_enable                  0x08001241   Thumb Code    62  at32f403a_407_crm.o(i.crm_periph_clock_enable)
    crm_pll_config                           0x08001285   Thumb Code   104  at32f403a_407_crm.o(i.crm_pll_config)
    crm_reset                                0x080012f1   Thumb Code    82  at32f403a_407_crm.o(i.crm_reset)
    crm_sysclk_switch                        0x0800134d   Thumb Code    14  at32f403a_407_crm.o(i.crm_sysclk_switch)
    crm_sysclk_switch_status_get             0x08001361   Thumb Code    10  at32f403a_407_crm.o(i.crm_sysclk_switch_status_get)
    debug_periph_mode_set                    0x08001371   Thumb Code    26  at32f403a_407_debug.o(i.debug_periph_mode_set)
    fputc                                    0x08001391   Thumb Code    32  at32f403a_407_board.o(i.fputc)
    gpio_default_para_init                   0x080013b5   Thumb Code    22  at32f403a_407_gpio.o(i.gpio_default_para_init)
    gpio_init                                0x080013cb   Thumb Code   168  at32f403a_407_gpio.o(i.gpio_init)
    gpio_pin_remap_config                    0x08001475   Thumb Code    70  at32f403a_407_gpio.o(i.gpio_pin_remap_config)
    $Super$$main                             0x080014c1   Thumb Code    36  main.o(i.main)
    nvic_irq_enable                          0x080014e9   Thumb Code   190  at32f403a_407_misc.o(i.nvic_irq_enable)
    nvic_priority_group_config               0x080015b1   Thumb Code    32  at32f403a_407_misc.o(i.nvic_priority_group_config)
    selftest_clock_cross_measurement_config  0x080015d9   Thumb Code   140  main.o(i.selftest_clock_cross_measurement_config)
    selftest_clock_runtime_test              0x08001675   Thumb Code   106  at32_selftest_clock.o(i.selftest_clock_runtime_test)
    selftest_clock_startup_test              0x080016f9   Thumb Code   262  at32_selftest_clock.o(i.selftest_clock_startup_test)
    selftest_crc_runtime_test                0x08001815   Thumb Code   180  at32_selftest_crc.o(i.selftest_crc_runtime_test)
    selftest_fail_handle                     0x080018e5   Thumb Code    36  at32_selftest_startup.o(i.selftest_fail_handle)
    selftest_runtime_check                   0x08001935   Thumb Code   412  at32_selftest_runtime.o(i.selftest_runtime_check)
    selftest_runtime_init                    0x08001bdd   Thumb Code   270  at32_selftest_runtime.o(i.selftest_runtime_init)
    selftest_sampling_ram_test               0x08001d41   Thumb Code   114  at32_selftest_ram.o(i.selftest_sampling_ram_test)
    selftest_startup_check                   0x08001e15   Thumb Code   586  at32_selftest_startup.o(i.selftest_startup_check)
    selftest_system_clock_config             0x0800222d   Thumb Code   192  main.o(i.selftest_system_clock_config)
    selftest_watchdog_test                   0x08002309   Thumb Code   394  at32_selftest_startup.o(i.selftest_watchdog_test)
    system_core_clock_update                 0x080025b5   Thumb Code   222  system_at32f403a_407.o(i.system_core_clock_update)
    systick_get                              0x080026ad   Thumb Code     6  main.o(i.systick_get)
    systick_inc                              0x080026b9   Thumb Code    12  main.o(i.systick_inc)
    tmr_base_init                            0x080026c9   Thumb Code    16  at32f403a_407_tmr.o(i.tmr_base_init)
    tmr_clock_source_div_set                 0x080026d9   Thumb Code    10  at32f403a_407_tmr.o(i.tmr_clock_source_div_set)
    tmr_cnt_dir_set                          0x080026e3   Thumb Code    10  at32f403a_407_tmr.o(i.tmr_cnt_dir_set)
    tmr_counter_enable                       0x080026ed   Thumb Code    10  at32f403a_407_tmr.o(i.tmr_counter_enable)
    tmr_flag_clear                           0x080026f7   Thumb Code     6  at32f403a_407_tmr.o(i.tmr_flag_clear)
    tmr_flag_get                             0x080026fd   Thumb Code    18  at32f403a_407_tmr.o(i.tmr_flag_get)
    tmr_input_channel_init                   0x0800270f   Thumb Code   270  at32f403a_407_tmr.o(i.tmr_input_channel_init)
    tmr_input_default_para_init              0x0800281d   Thumb Code    16  at32f403a_407_tmr.o(i.tmr_input_default_para_init)
    tmr_interrupt_enable                     0x0800282d   Thumb Code    22  at32f403a_407_tmr.o(i.tmr_interrupt_enable)
    tmr_repetition_counter_set               0x08002845   Thumb Code    22  at32f403a_407_tmr.o(i.tmr_repetition_counter_set)
    uart_print_init                          0x08002865   Thumb Code    94  at32f403a_407_board.o(i.uart_print_init)
    usart_data_transmit                      0x080028d1   Thumb Code     8  at32f403a_407_usart.o(i.usart_data_transmit)
    usart_enable                             0x080028d9   Thumb Code    10  at32f403a_407_usart.o(i.usart_enable)
    usart_flag_get                           0x080028e3   Thumb Code    16  at32f403a_407_usart.o(i.usart_flag_get)
    usart_init                               0x080028f5   Thumb Code   120  at32f403a_407_usart.o(i.usart_init)
    usart_reconfigure                        0x0800297d   Thumb Code    34  main.o(i.usart_reconfigure)
    usart_transmitter_enable                 0x080029a5   Thumb Code    10  at32f403a_407_usart.o(i.usart_transmitter_enable)
    wdt_counter_reload                       0x080029b1   Thumb Code    10  at32f403a_407_wdt.o(i.wdt_counter_reload)
    wdt_divider_set                          0x080029c1   Thumb Code    14  at32f403a_407_wdt.o(i.wdt_divider_set)
    wdt_enable                               0x080029d5   Thumb Code    10  at32f403a_407_wdt.o(i.wdt_enable)
    wdt_register_write_enable                0x080029e5   Thumb Code    20  at32f403a_407_wdt.o(i.wdt_register_write_enable)
    wdt_reload_value_set                     0x080029fd   Thumb Code     6  at32f403a_407_wdt.o(i.wdt_reload_value_set)
    wwdt_counter_set                         0x08002a09   Thumb Code    14  at32f403a_407_wwdt.o(i.wwdt_counter_set)
    wwdt_divider_set                         0x08002a1d   Thumb Code    14  at32f403a_407_wwdt.o(i.wwdt_divider_set)
    wwdt_enable                              0x08002a31   Thumb Code    10  at32f403a_407_wwdt.o(i.wwdt_enable)
    wwdt_window_counter_set                  0x08002a41   Thumb Code    14  at32f403a_407_wwdt.o(i.wwdt_window_counter_set)
    _fp_init                                 0x08002a55   Thumb Code    10  fpinit.o(x$fpl$fpinit)
    __fplib_config_fpu_vfp                   0x08002a5d   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fplib_config_pureend_doubles           0x08002a5d   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    Region$$Table$$Base                      0x08002f28   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08002f58   Number         0  anon$$obj.o(Region$$Table)
    runtime_ram_buf                          0x20000000   Data          32  at32_selftest_startup.o(RUNTIME_RAM_BUF)
    p_runtime_ram_chk                        0x20000020   Data           4  at32_selftest_startup.o(RUNTIME_RAM_POINTER)
    p_runtime_ram_chk_inv                    0x20000024   Data           4  at32_selftest_startup.o(RUNTIME_RAM_POINTER)
    gap_for_ram_test_overlay                 0x20000028   Data           8  at32_selftest_startup.o(RUNTIME_RAM_POINTER)
    ctrl_flow_cnt                            0x20000030   Data           4  at32_selftest_startup.o(CLASS_B_RAM)
    isr_ctrl_flow_cnt                        0x20000034   Data           4  at32_selftest_startup.o(CLASS_B_RAM)
    period_val                               0x20000038   Data           4  at32_selftest_startup.o(CLASS_B_RAM)
    systick_cnt                              0x2000003c   Data           4  at32_selftest_startup.o(CLASS_B_RAM)
    time_base_flag                           0x20000040   Data           4  at32_selftest_startup.o(CLASS_B_RAM)
    lick_period_flag                         0x20000044   Data           4  at32_selftest_startup.o(CLASS_B_RAM)
    last_ctrl_flow_cnt                       0x20000048   Data           4  at32_selftest_startup.o(CLASS_B_RAM)
    p_runtime_crc_chk                        0x2000004c   Data           4  at32_selftest_startup.o(CLASS_B_RAM)
    reference_crc                            0x20000050   Data           4  at32_selftest_startup.o(CLASS_B_RAM)
    ctrl_flow_cnt_inv                        0x20000054   Data           4  at32_selftest_startup.o(CLASS_B_RAM_REV)
    isr_ctrl_flow_cnt_inv                    0x20000058   Data           4  at32_selftest_startup.o(CLASS_B_RAM_REV)
    period_val_inv                           0x2000005c   Data           4  at32_selftest_startup.o(CLASS_B_RAM_REV)
    systick_cnt_inv                          0x20000060   Data           4  at32_selftest_startup.o(CLASS_B_RAM_REV)
    time_base_flag_inv                       0x20000064   Data           4  at32_selftest_startup.o(CLASS_B_RAM_REV)
    lick_period_flag_inv                     0x20000068   Data           4  at32_selftest_startup.o(CLASS_B_RAM_REV)
    last_ctrl_flow_cnt_inv                   0x2000006c   Data           4  at32_selftest_startup.o(CLASS_B_RAM_REV)
    p_runtime_crc_chk_inv                    0x20000070   Data           4  at32_selftest_startup.o(CLASS_B_RAM_REV)
    reference_crc_inv                        0x20000074   Data           4  at32_selftest_startup.o(CLASS_B_RAM_REV)
    tick_cnt_val                             0x20000078   Data           4  main.o(.data)
    led_gpio_port                            0x20000084   Data          12  at32f403a_407_board.o(.data)
    led_gpio_pin                             0x20000090   Data           6  at32f403a_407_board.o(.data)
    led_gpio_crm_clk                         0x20000098   Data          12  at32f403a_407_board.o(.data)
    __stdout                                 0x200000ac   Data           4  at32f403a_407_board.o(.data)
    system_core_clock                        0x200000b4   Data           4  system_at32f403a_407.o(.data)
    __libspace_start                         0x200000b8   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x20000118   Data           0  libspace.o(.bss)
    stack_overflow_buf                       0x20001078   Data          16  at32_selftest_startup.o(STACK_BOTTOM)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000185

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00002fc8, Max: 0x00100000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00002f58, Max: 0x00100000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000184   Data   RO         3954    RESET               startup_at32f403a_407.o
    0x08000184   0x08000184   0x00000008   Code   RO         3997  * !!!main             c_w.l(__main.o)
    0x0800018c   0x0800018c   0x00000034   Code   RO         4162    !!!scatter          c_w.l(__scatter.o)
    0x080001c0   0x080001c0   0x0000001a   Code   RO         4164    !!handler_copy      c_w.l(__scatter_copy.o)
    0x080001da   0x080001da   0x00000000   Code   RO         3992    .ARM.Collect$$_printf_percent$$00000000  c_w.l(_printf_percent.o)
    0x080001da   0x080001da   0x00000006   Code   RO         3991    .ARM.Collect$$_printf_percent$$00000009  c_w.l(_printf_d.o)
    0x080001e0   0x080001e0   0x00000004   Code   RO         4006    .ARM.Collect$$_printf_percent$$00000017  c_w.l(_printf_percent_end.o)
    0x080001e4   0x080001e4   0x00000002   Code   RO         4039    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x080001e6   0x080001e6   0x00000004   Code   RO         4045    .ARM.Collect$$libinit$$00000001  c_w.l(libinit2.o)
    0x080001ea   0x080001ea   0x00000000   Code   RO         4048    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x080001ea   0x080001ea   0x00000000   Code   RO         4051    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x080001ea   0x080001ea   0x00000000   Code   RO         4053    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x080001ea   0x080001ea   0x00000000   Code   RO         4055    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x080001ea   0x080001ea   0x00000000   Code   RO         4058    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x080001ea   0x080001ea   0x00000000   Code   RO         4060    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x080001ea   0x080001ea   0x00000000   Code   RO         4062    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x080001ea   0x080001ea   0x00000000   Code   RO         4064    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x080001ea   0x080001ea   0x00000000   Code   RO         4066    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x080001ea   0x080001ea   0x00000000   Code   RO         4068    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x080001ea   0x080001ea   0x00000000   Code   RO         4070    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x080001ea   0x080001ea   0x00000000   Code   RO         4072    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x080001ea   0x080001ea   0x00000000   Code   RO         4074    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x080001ea   0x080001ea   0x00000000   Code   RO         4076    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x080001ea   0x080001ea   0x00000000   Code   RO         4078    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x080001ea   0x080001ea   0x00000000   Code   RO         4082    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x080001ea   0x080001ea   0x00000000   Code   RO         4084    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x080001ea   0x080001ea   0x00000000   Code   RO         4086    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x080001ea   0x080001ea   0x00000000   Code   RO         4088    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x080001ea   0x080001ea   0x00000002   Code   RO         4089    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x080001ec   0x080001ec   0x00000002   Code   RO         4107    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x080001ee   0x080001ee   0x00000000   Code   RO         4118    .ARM.Collect$$libshutdown$$00000003  c_w.l(libshutdown2.o)
    0x080001ee   0x080001ee   0x00000000   Code   RO         4121    .ARM.Collect$$libshutdown$$00000006  c_w.l(libshutdown2.o)
    0x080001ee   0x080001ee   0x00000000   Code   RO         4124    .ARM.Collect$$libshutdown$$00000009  c_w.l(libshutdown2.o)
    0x080001ee   0x080001ee   0x00000000   Code   RO         4126    .ARM.Collect$$libshutdown$$0000000B  c_w.l(libshutdown2.o)
    0x080001ee   0x080001ee   0x00000000   Code   RO         4129    .ARM.Collect$$libshutdown$$0000000E  c_w.l(libshutdown2.o)
    0x080001ee   0x080001ee   0x00000002   Code   RO         4130    .ARM.Collect$$libshutdown$$0000000F  c_w.l(libshutdown2.o)
    0x080001f0   0x080001f0   0x00000000   Code   RO         4001    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x080001f0   0x080001f0   0x00000000   Code   RO         4008    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x080001f0   0x080001f0   0x00000006   Code   RO         4020    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x080001f6   0x080001f6   0x00000000   Code   RO         4010    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x080001f6   0x080001f6   0x00000004   Code   RO         4011    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x080001fa   0x080001fa   0x00000000   Code   RO         4013    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x080001fa   0x080001fa   0x00000008   Code   RO         4014    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x08000202   0x08000202   0x00000002   Code   RO         4043    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x08000204   0x08000204   0x00000000   Code   RO         4091    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x08000204   0x08000204   0x00000004   Code   RO         4092    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x08000208   0x08000208   0x00000006   Code   RO         4093    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x0800020e   0x0800020e   0x00000002   PAD
    0x08000210   0x08000210   0x000001d8   Code   RO          318    .text               at32_selftest_cpurun_keil.o
    0x080003e8   0x080003e8   0x00000288   Code   RO          322    .text               at32_selftest_cpustart_keil.o
    0x08000670   0x08000670   0x00000250   Code   RO          326    .text               at32_selftest_ram_keil.o
    0x080008c0   0x080008c0   0x00000040   Code   RO         3955    .text               startup_at32f403a_407.o
    0x08000900   0x08000900   0x00000002   Code   RO         3961    .text               c_w.l(use_no_semi_2.o)
    0x08000902   0x08000902   0x00000002   PAD
    0x08000904   0x08000904   0x00000018   Code   RO         3965    .text               c_w.l(noretval__2printf.o)
    0x0800091c   0x0800091c   0x00000068   Code   RO         3967    .text               c_w.l(__printf.o)
    0x08000984   0x08000984   0x00000078   Code   RO         3969    .text               c_w.l(_printf_dec.o)
    0x080009fc   0x080009fc   0x00000004   Code   RO         3993    .text               c_w.l(putc.o)
    0x08000a00   0x08000a00   0x00000006   Code   RO         3995    .text               c_w.l(heapauxi.o)
    0x08000a06   0x08000a06   0x00000002   Code   RO         3999    .text               c_w.l(use_no_semi.o)
    0x08000a08   0x08000a08   0x000000b2   Code   RO         4002    .text               c_w.l(_printf_intcommon.o)
    0x08000aba   0x08000aba   0x00000002   PAD
    0x08000abc   0x08000abc   0x00000024   Code   RO         4004    .text               c_w.l(_printf_char_file.o)
    0x08000ae0   0x08000ae0   0x00000030   Code   RO         4022    .text               c_w.l(_printf_char_common.o)
    0x08000b10   0x08000b10   0x00000008   Code   RO         4024    .text               c_w.l(ferror.o)
    0x08000b18   0x08000b18   0x0000004a   Code   RO         4028    .text               c_w.l(sys_stackheap_outer.o)
    0x08000b62   0x08000b62   0x0000000c   Code   RO         4032    .text               c_w.l(exit.o)
    0x08000b6e   0x08000b6e   0x00000002   PAD
    0x08000b70   0x08000b70   0x00000008   Code   RO         4040    .text               c_w.l(libspace.o)
    0x08000b78   0x08000b78   0x00000028   Code   RO         3735    i.$Sub$$main        at32_selftest_startup.o
    0x08000ba0   0x08000ba0   0x00000004   Code   RO          238    i.BusFault_Handler  at32f403a_407_int.o
    0x08000ba4   0x08000ba4   0x00000002   Code   RO          239    i.DebugMon_Handler  at32f403a_407_int.o
    0x08000ba6   0x08000ba6   0x00000004   Code   RO          240    i.HardFault_Handler  at32f403a_407_int.o
    0x08000baa   0x08000baa   0x00000004   Code   RO          241    i.MemManage_Handler  at32f403a_407_int.o
    0x08000bae   0x08000bae   0x00000002   Code   RO          242    i.NMI_Handler       at32f403a_407_int.o
    0x08000bb0   0x08000bb0   0x00000002   Code   RO          243    i.PendSV_Handler    at32f403a_407_int.o
    0x08000bb2   0x08000bb2   0x00000002   Code   RO          244    i.SVC_Handler       at32f403a_407_int.o
    0x08000bb4   0x08000bb4   0x0000014c   Code   RO          245    i.SysTick_Handler   at32f403a_407_int.o
    0x08000d00   0x08000d00   0x00000074   Code   RO         3918    i.SystemInit        system_at32f403a_407.o
    0x08000d74   0x08000d74   0x00000094   Code   RO          246    i.TMR5_GLOBAL_IRQHandler  at32f403a_407_int.o
    0x08000e08   0x08000e08   0x00000004   Code   RO          247    i.UsageFault_Handler  at32f403a_407_int.o
    0x08000e0c   0x08000e0c   0x00000004   Code   RO          356    i._sys_exit         at32f403a_407_board.o
    0x08000e10   0x08000e10   0x00000028   Code   RO         3736    i.control_flow_check_point  at32_selftest_startup.o
    0x08000e38   0x08000e38   0x0000009c   Code   RO          329    i.crc32_fsl_continuous  crc32.o
    0x08000ed4   0x08000ed4   0x0000002c   Code   RO          330    i.crc32_fsl_single  crc32.o
    0x08000f00   0x08000f00   0x00000014   Code   RO         1108    i.crm_ahb_div_set   at32f403a_407_crm.o
    0x08000f14   0x08000f14   0x00000014   Code   RO         1109    i.crm_apb1_div_set  at32f403a_407_crm.o
    0x08000f28   0x08000f28   0x00000014   Code   RO         1110    i.crm_apb2_div_set  at32f403a_407_crm.o
    0x08000f3c   0x08000f3c   0x00000028   Code   RO         1111    i.crm_auto_step_mode_enable  at32f403a_407_crm.o
    0x08000f64   0x08000f64   0x00000014   Code   RO         1115    i.crm_clock_failure_detection_enable  at32f403a_407_crm.o
    0x08000f78   0x08000f78   0x00000060   Code   RO         1117    i.crm_clock_source_enable  at32f403a_407_crm.o
    0x08000fd8   0x08000fd8   0x0000012c   Code   RO         1118    i.crm_clocks_freq_get  at32f403a_407_crm.o
    0x08001104   0x08001104   0x000000c8   Code   RO         1119    i.crm_flag_clear    at32f403a_407_crm.o
    0x080011cc   0x080011cc   0x00000030   Code   RO         1120    i.crm_flag_get      at32f403a_407_crm.o
    0x080011fc   0x080011fc   0x00000014   Code   RO         1122    i.crm_hext_clock_div_set  at32f403a_407_crm.o
    0x08001210   0x08001210   0x0000002e   Code   RO         1123    i.crm_hext_stable_wait  at32f403a_407_crm.o
    0x0800123e   0x0800123e   0x00000002   PAD
    0x08001240   0x08001240   0x00000044   Code   RO         1130    i.crm_periph_clock_enable  at32f403a_407_crm.o
    0x08001284   0x08001284   0x0000006c   Code   RO         1133    i.crm_pll_config    at32f403a_407_crm.o
    0x080012f0   0x080012f0   0x0000005c   Code   RO         1134    i.crm_reset         at32f403a_407_crm.o
    0x0800134c   0x0800134c   0x00000014   Code   RO         1137    i.crm_sysclk_switch  at32f403a_407_crm.o
    0x08001360   0x08001360   0x00000010   Code   RO         1138    i.crm_sysclk_switch_status_get  at32f403a_407_crm.o
    0x08001370   0x08001370   0x00000020   Code   RO         1439    i.debug_periph_mode_set  at32f403a_407_debug.o
    0x08001390   0x08001390   0x00000024   Code   RO          370    i.fputc             at32f403a_407_board.o
    0x080013b4   0x080013b4   0x00000016   Code   RO         1922    i.gpio_default_para_init  at32f403a_407_gpio.o
    0x080013ca   0x080013ca   0x000000a8   Code   RO         1926    i.gpio_init         at32f403a_407_gpio.o
    0x08001472   0x08001472   0x00000002   PAD
    0x08001474   0x08001474   0x0000004c   Code   RO         1932    i.gpio_pin_remap_config  at32f403a_407_gpio.o
    0x080014c0   0x080014c0   0x00000028   Code   RO            4    i.main              main.o
    0x080014e8   0x080014e8   0x000000c8   Code   RO         2244    i.nvic_irq_enable   at32f403a_407_misc.o
    0x080015b0   0x080015b0   0x00000028   Code   RO         2246    i.nvic_priority_group_config  at32f403a_407_misc.o
    0x080015d8   0x080015d8   0x0000009c   Code   RO            5    i.selftest_clock_cross_measurement_config  main.o
    0x08001674   0x08001674   0x00000084   Code   RO         3888    i.selftest_clock_runtime_test  at32_selftest_clock.o
    0x080016f8   0x080016f8   0x0000011c   Code   RO         3889    i.selftest_clock_startup_test  at32_selftest_clock.o
    0x08001814   0x08001814   0x000000d0   Code   RO         3840    i.selftest_crc_runtime_test  at32_selftest_crc.o
    0x080018e4   0x080018e4   0x00000050   Code   RO         3737    i.selftest_fail_handle  at32_selftest_startup.o
    0x08001934   0x08001934   0x000002a8   Code   RO         3801    i.selftest_runtime_check  at32_selftest_runtime.o
    0x08001bdc   0x08001bdc   0x00000164   Code   RO         3802    i.selftest_runtime_init  at32_selftest_runtime.o
    0x08001d40   0x08001d40   0x00000084   Code   RO         3864    i.selftest_sampling_ram_test  at32_selftest_ram.o
    0x08001dc4   0x08001dc4   0x00000050   Code   RO         3803    i.selftest_stack_check  at32_selftest_runtime.o
    0x08001e14   0x08001e14   0x00000418   Code   RO         3738    i.selftest_startup_check  at32_selftest_startup.o
    0x0800222c   0x0800222c   0x000000dc   Code   RO            6    i.selftest_system_clock_config  main.o
    0x08002308   0x08002308   0x000002ac   Code   RO         3739    i.selftest_watchdog_test  at32_selftest_startup.o
    0x080025b4   0x080025b4   0x000000f8   Code   RO         3919    i.system_core_clock_update  system_at32f403a_407.o
    0x080026ac   0x080026ac   0x0000000c   Code   RO            7    i.systick_get       main.o
    0x080026b8   0x080026b8   0x00000010   Code   RO            8    i.systick_inc       main.o
    0x080026c8   0x080026c8   0x00000010   Code   RO         2812    i.tmr_base_init     at32f403a_407_tmr.o
    0x080026d8   0x080026d8   0x0000000a   Code   RO         2821    i.tmr_clock_source_div_set  at32f403a_407_tmr.o
    0x080026e2   0x080026e2   0x0000000a   Code   RO         2822    i.tmr_cnt_dir_set   at32f403a_407_tmr.o
    0x080026ec   0x080026ec   0x0000000a   Code   RO         2823    i.tmr_counter_enable  at32f403a_407_tmr.o
    0x080026f6   0x080026f6   0x00000006   Code   RO         2835    i.tmr_flag_clear    at32f403a_407_tmr.o
    0x080026fc   0x080026fc   0x00000012   Code   RO         2836    i.tmr_flag_get      at32f403a_407_tmr.o
    0x0800270e   0x0800270e   0x0000010e   Code   RO         2841    i.tmr_input_channel_init  at32f403a_407_tmr.o
    0x0800281c   0x0800281c   0x00000010   Code   RO         2842    i.tmr_input_default_para_init  at32f403a_407_tmr.o
    0x0800282c   0x0800282c   0x00000016   Code   RO         2844    i.tmr_interrupt_enable  at32f403a_407_tmr.o
    0x08002842   0x08002842   0x00000002   PAD
    0x08002844   0x08002844   0x00000020   Code   RO         2861    i.tmr_repetition_counter_set  at32f403a_407_tmr.o
    0x08002864   0x08002864   0x0000006c   Code   RO          371    i.uart_print_init   at32f403a_407_board.o
    0x080028d0   0x080028d0   0x00000008   Code   RO         3164    i.usart_data_transmit  at32f403a_407_usart.o
    0x080028d8   0x080028d8   0x0000000a   Code   RO         3167    i.usart_enable      at32f403a_407_usart.o
    0x080028e2   0x080028e2   0x00000010   Code   RO         3169    i.usart_flag_get    at32f403a_407_usart.o
    0x080028f2   0x080028f2   0x00000002   PAD
    0x080028f4   0x080028f4   0x00000088   Code   RO         3171    i.usart_init        at32f403a_407_usart.o
    0x0800297c   0x0800297c   0x00000028   Code   RO            9    i.usart_reconfigure  main.o
    0x080029a4   0x080029a4   0x0000000a   Code   RO         3185    i.usart_transmitter_enable  at32f403a_407_usart.o
    0x080029ae   0x080029ae   0x00000002   PAD
    0x080029b0   0x080029b0   0x00000010   Code   RO         3489    i.wdt_counter_reload  at32f403a_407_wdt.o
    0x080029c0   0x080029c0   0x00000014   Code   RO         3490    i.wdt_divider_set   at32f403a_407_wdt.o
    0x080029d4   0x080029d4   0x00000010   Code   RO         3491    i.wdt_enable        at32f403a_407_wdt.o
    0x080029e4   0x080029e4   0x00000018   Code   RO         3493    i.wdt_register_write_enable  at32f403a_407_wdt.o
    0x080029fc   0x080029fc   0x0000000c   Code   RO         3494    i.wdt_reload_value_set  at32f403a_407_wdt.o
    0x08002a08   0x08002a08   0x00000014   Code   RO         3543    i.wwdt_counter_set  at32f403a_407_wwdt.o
    0x08002a1c   0x08002a1c   0x00000014   Code   RO         3544    i.wwdt_divider_set  at32f403a_407_wwdt.o
    0x08002a30   0x08002a30   0x00000010   Code   RO         3545    i.wwdt_enable       at32f403a_407_wwdt.o
    0x08002a40   0x08002a40   0x00000014   Code   RO         3550    i.wwdt_window_counter_set  at32f403a_407_wwdt.o
    0x08002a54   0x08002a54   0x0000000a   Code   RO         4099    x$fpl$fpinit        fz_wm.l(fpinit.o)
    0x08002a5e   0x08002a5e   0x00000002   PAD
    0x08002a60   0x08002a60   0x00000400   Data   RO          331    .constdata          crc32.o
    0x08002e60   0x08002e60   0x00000028   Data   RO         1142    .constdata          at32f403a_407_crm.o
    0x08002e88   0x08002e88   0x00000010   Data   RO         3920    .constdata          system_at32f403a_407.o
    0x08002e98   0x08002e98   0x00000047   Data   RO           10    .conststring        main.o
    0x08002edf   0x08002edf   0x00000001   PAD
    0x08002ee0   0x08002ee0   0x00000047   Data   RO         3740    .conststring        at32_selftest_startup.o
    0x08002f27   0x08002f27   0x00000001   PAD
    0x08002f28   0x08002f28   0x00000030   Data   RO         4160    Region$$Table       anon$$obj.o


    Execution Region RAM_BUF (Exec base: 0x20000000, Load base: 0x08002f58, Size: 0x00000020, Max: 0x00000020, ABSOLUTE, UNINIT)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x08002f58   0x00000020   Data   RW         3743    RUNTIME_RAM_BUF     at32_selftest_startup.o


    Execution Region RAM_POINTER (Exec base: 0x20000020, Load base: 0x08002f78, Size: 0x00000010, Max: 0x00000010, ABSOLUTE, UNINIT)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000020   0x08002f78   0x00000010   Data   RW         3744    RUNTIME_RAM_POINTER  at32_selftest_startup.o


    Execution Region CLASSB (Exec base: 0x20000030, Load base: 0x08002f88, Size: 0x00000024, Max: 0x00000024, ABSOLUTE, UNINIT)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000030        -       0x00000024   Zero   RW         3741    CLASS_B_RAM         at32_selftest_startup.o


    Execution Region CLASSB_INV (Exec base: 0x20000054, Load base: 0x08002f88, Size: 0x00000024, Max: 0x00000024, ABSOLUTE, UNINIT)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000054        -       0x00000024   Zero   RW         3742    CLASS_B_RAM_REV     at32_selftest_startup.o


    Execution Region CLASSA (Exec base: 0x20000078, Load base: 0x08002f88, Size: 0x000002a0, Max: 0x00001000, ABSOLUTE, UNINIT)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000078   0x08002f88   0x00000004   Data   RW           11    .data               main.o
    0x2000007c   0x08002f8c   0x00000002   Data   RW          248    .data               at32f403a_407_int.o
    0x2000007e   0x08002f8e   0x00000002   PAD
    0x20000080   0x08002f90   0x00000004   Data   RW          332    .data               crc32.o
    0x20000084   0x08002f94   0x0000002d   Data   RW          372    .data               at32f403a_407_board.o
    0x200000b1   0x08002fc1   0x00000003   PAD
    0x200000b4   0x08002fc4   0x00000004   Data   RW         3921    .data               system_at32f403a_407.o
    0x200000b8        -       0x00000060   Zero   RW         4041    .bss                c_w.l(libspace.o)
    0x20000118        -       0x00000200   Zero   RW         3953    HEAP                startup_at32f403a_407.o


    Execution Region STACK_NO_HEAP (Exec base: 0x20001078, Load base: 0x08002fc8, Size: 0x00000410, Max: 0x00000410, ABSOLUTE, UNINIT)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20001078        -       0x00000010   Zero   RW         3745    STACK_BOTTOM        at32_selftest_startup.o
    0x20001088        -       0x00000400   Zero   RW         3952    STACK               startup_at32f403a_407.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       416         48          0          0          0       1760   at32_selftest_clock.o
       472          8          0          0          0        620   at32_selftest_cpurun_keil.o
       648          8          0          0          0        676   at32_selftest_cpustart_keil.o
       208         28          0          0          0        788   at32_selftest_crc.o
       132         18          0          0          0        765   at32_selftest_ram.o
       592         84          0          0          0        596   at32_selftest_ram_keil.o
      1116        368          0          0          0      32852   at32_selftest_runtime.o
      1892        816         71         48         88      36622   at32_selftest_startup.o
       148         18          0         45          0       5104   at32f403a_407_board.o
      1134        142         40          0          0      11931   at32f403a_407_crm.o
        32          6          0          0          0        724   at32f403a_407_debug.o
       266          6          0          0          0       3061   at32f403a_407_gpio.o
       504        138          0          2          0       6739   at32f403a_407_int.o
       240         18          0          0          0      32350   at32f403a_407_misc.o
       410         10          0          0          0       7745   at32f403a_407_tmr.o
       180         16          0          0          0       4214   at32f403a_407_usart.o
        88         28          0          0          0       3195   at32f403a_407_wdt.o
        76         24          0          0          0       2626   at32f403a_407_wwdt.o
       200         12       1024          4          0       2334   crc32.o
       484         64         71          4          0      60471   main.o
        64         26        388          0       1536       1024   startup_at32f403a_407.o
       364         40         16          4          0       2596   system_at32f403a_407.o

    ----------------------------------------------------------------------
      9676       <USER>       <GROUP>        112       1624     218793   Object Totals
         0          0         48          0          0          0   (incl. Generated)
        10          0          2          5          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         8          0          0          0          0         68   __main.o
       104          0          0          0          0         84   __printf.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        48          6          0          0          0         96   _printf_char_common.o
        36          4          0          0          0         80   _printf_char_file.o
         6          0          0          0          0          0   _printf_d.o
       120         16          0          0          0         92   _printf_dec.o
       178          0          0          0          0         88   _printf_intcommon.o
         0          0          0          0          0          0   _printf_percent.o
         4          0          0          0          0          0   _printf_percent_end.o
        12          0          0          0          0         72   exit.o
         8          0          0          0          0         68   ferror.o
         6          0          0          0          0        152   heapauxi.o
         2          0          0          0          0          0   libinit.o
         6          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
        24          4          0          0          0         84   noretval__2printf.o
         4          0          0          0          0         68   putc.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
         2          0          0          0          0         68   use_no_semi_2.o
        10          0          0          0          0        116   fpinit.o

    ----------------------------------------------------------------------
       784         <USER>          <GROUP>          0         96       1352   Library Totals
        10          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

       764         42          0          0         96       1236   c_w.l
        10          0          0          0          0        116   fz_wm.l

    ----------------------------------------------------------------------
       784         <USER>          <GROUP>          0         96       1352   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     10460       1968       1660        112       1720     211805   Grand Totals
     10460       1968       1660        112       1720     211805   ELF Image Totals
     10460       1968       1660        112          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                12120 (  11.84kB)
    Total RW  Size (RW Data + ZI Data)              1832 (   1.79kB)
    Total ROM Size (Code + RO Data + RW Data)      12232 (  11.95kB)

==============================================================================

